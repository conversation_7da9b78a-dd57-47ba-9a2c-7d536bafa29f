# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
.env
.venv
.venv/

# API Keys and Secrets (IMPORTANT!)
*.env
.env.*
config/config.json
astra_ai/.env
**/api_keys.json
**/secrets.json

# IDEs and editors
.idea/
.vscode/
*.swp
*.swo
*~
.project
.settings/
.classpath

# Logs and databases
*.log
*.sqlite
*.db
*.db-shm
*.db-wal

# Project-specific
data/
logs/
voice_recordings/
transcriptions/
temp/
cache/
downloads/

# OS-specific
.DS_Store
Thumbs.db
desktop.ini

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/
coverage.xml
*.cover
.hypothesis/

# OpenVoice downloads
openvoice_main.zip
OpenVoice-main/
checkpoints/
models/

# Audio files
*.wav
*.mp3
*.flac
*.ogg

# Large model files
*.bin
*.safetensors
*.ckpt
*.pth
*.pt

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# dotenv
.env

# virtualenv
.venv
venv/
ENV/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/ 
