# 🚀 GINI 1.5 - AI Screen Analyzer

**AI-Powered Screen Analysis System using Google Gemini 1.5 Pro Vision**

Gini 1.5 is an advanced screen capture and analysis system that uses Google's Gemini 1.5 Pro Vision API to provide intelligent, detailed analysis of your screen content. Simply ask "What's on my screen?" and get comprehensive AI-powered insights.

## ✨ Features

### 🎯 Core Functionality
- **One-Click Screen Capture**: Instant screen capture with browser's native `getDisplayMedia()` API
- **AI-Powered Analysis**: Advanced analysis using Google Gemini 1.5 Pro Vision model
- **Multiple Analysis Types**: Detailed, Quick Summary, and Custom Question modes
- **Follow-up Questions**: Ask specific questions about your captured screen
- **Real-time Processing**: Fast analysis with loading indicators and progress feedback

### 🎨 User Interface
- **Futuristic Design**: Neon green/cyan theme with glass morphism effects
- **Responsive Layout**: Works on desktop and mobile devices
- **Animated Background**: Dynamic grid animation for visual appeal
- **Intuitive Controls**: Easy-to-use buttons and clear visual feedback
- **Keyboard Shortcuts**: Ctrl+Enter to capture, Escape to clear

### 🔧 Technical Features
- **Desktop Integration**: Runs as PyWebView desktop application
- **Flask Backend**: RESTful API for screen analysis processing
- **Error Handling**: Comprehensive error handling and user feedback
- **Security**: Permission-based screen capture with user consent
- **Performance**: Optimized image processing and API communication

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.8+
- Modern web browser (Chrome, Firefox, Edge)
- Google AI Studio API key

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Get Google Gemini API Key
1. Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create a new API key
3. Copy the key for configuration

### 3. Configure API Key
Create a `.env` file in the `astra_ai` directory:
```env
GOOGLE_AI_API_KEY=your_gemini_api_key_here
```

Or set environment variable:
```bash
export GOOGLE_AI_API_KEY=your_gemini_api_key_here
```

### 4. Test Installation
```bash
python test_screen_analyzer.py
```

## 🚀 Usage

### Starting the Application
```bash
python astra_ai/scripts/run_desktop_nova.py
```

The application will:
1. Start Flask API server
2. Start UI server
3. Open PyWebView desktop window
4. Navigate to Gini 1.5 interface

### Using the Screen Analyzer

#### Method 1: Direct Access
Navigate to: `http://localhost:PORT/Gini.15.html`

#### Method 2: Voice Commands (when integrated)
- "What's on my screen?"
- "Analyze my screen"
- "Describe what I'm looking at"
- "Screen capture"

### Analysis Types

#### 🔍 Detailed Analysis
Comprehensive breakdown of all screen elements including:
- Main content identification
- UI elements description
- Text content summary
- Activity analysis
- Context understanding
- Notable details

#### ⚡ Quick Summary
Brief, one-paragraph overview focusing on:
- Most important elements
- Current user activity
- Key information

#### ❓ Custom Question
Ask specific questions about your screen:
- "What programming language is being used?"
- "Summarize the main points of this document"
- "What errors are visible on the screen?"
- "Describe the user interface layout"

## 🎮 Controls & Shortcuts

### Mouse Controls
- **Main Button**: Click "What's on my screen?" to capture and analyze
- **Analysis Type**: Click cards to select analysis mode
- **Follow-up**: Type questions in chat input after analysis

### Keyboard Shortcuts
- `Ctrl + Enter`: Capture and analyze screen
- `Escape`: Clear results and reset interface
- `Enter` (in chat): Send follow-up question

## 🔧 API Endpoints

### Screen Analysis
```http
POST /api/screen-capture/analyze
Content-Type: application/json

{
  "image_data": "base64_encoded_image",
  "type": "detailed|quick|custom",
  "question": "optional_custom_question"
}
```

### Connection Test
```http
GET /api/screen-capture/test
```

## 🛡️ Security & Privacy

### Screen Capture Permissions
- Requires explicit user permission for screen capture
- Uses browser's native permission system
- No automatic or background capturing

### Data Handling
- Images processed in real-time
- No permanent storage of screen captures
- API communication over localhost only
- Gemini API calls follow Google's privacy policies

### Privacy Controls
- User controls when to capture screen
- Clear indication of analysis in progress
- Option to clear results and data

## 🔍 Troubleshooting

### Common Issues

#### "Screen capture permission denied"
- **Solution**: Allow screen sharing when prompted by browser
- **Note**: Permission is required for each session

#### "Gemini Vision API not available"
- **Check**: API key is correctly set in environment
- **Verify**: API key is valid and has proper permissions
- **Test**: Run `python test_screen_analyzer.py`

#### "API connection failed"
- **Check**: Flask server is running
- **Verify**: No firewall blocking localhost connections
- **Solution**: Restart the application

#### Browser compatibility
- **Supported**: Chrome 72+, Firefox 66+, Edge 79+
- **Not supported**: Internet Explorer, older browser versions

### Debug Mode
Enable debug logging by setting:
```bash
export FLASK_DEBUG=1
```

## 🎯 Use Cases

### Development
- Code review and documentation
- Bug identification and reporting
- UI/UX analysis and feedback
- Technical documentation creation

### Productivity
- Document summarization
- Meeting notes from screen content
- Research and information gathering
- Content analysis and insights

### Education
- Learning material analysis
- Tutorial step-by-step guidance
- Technical concept explanation
- Visual content understanding

### Accessibility
- Screen reader enhancement
- Visual content description
- Interface navigation assistance
- Content accessibility analysis

## 🔮 Future Enhancements

- Voice command integration
- Multi-monitor support
- Batch analysis capabilities
- Export and sharing features
- Integration with other AI models
- Advanced image preprocessing
- Custom analysis templates
- API rate limiting and optimization

## 📝 License

This project is part of the Astra AI system. Please refer to the main project license.

## 🤝 Contributing

Contributions are welcome! Please:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Run the test script: `python test_screen_analyzer.py`
3. Review logs for error details
4. Create an issue with detailed information

---

**Gini 1.5** - Bringing AI vision to your desktop experience! 🚀✨
