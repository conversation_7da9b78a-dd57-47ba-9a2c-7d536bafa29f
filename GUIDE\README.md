# 🚀 Astra AI - Advanced AI Assistant System

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![OpenVoice](https://img.shields.io/badge/TTS-OpenVoice-green.svg)](https://github.com/myshell-ai/OpenVoice)
[![Gemini](https://img.shields.io/badge/Vision-Gemini%201.5-orange.svg)](https://ai.google.dev/)

A powerful, multi-modal AI assistant featuring voice synthesis, screen analysis, and intelligent conversation capabilities.

## ✨ Features

- 🎤 **Voice Synthesis**: High-quality TTS using OpenVoice
- 👁️ **Screen Analysis**: AI-powered screen capture and analysis with Gemini Vision
- 🧠 **Memory System**: Persistent conversation memory and context
- 🌐 **Web Interface**: Modern HTML5 interface with real-time interaction
- 💬 **Terminal Mode**: Command-line interface for quick interactions
- 🔍 **Smart Search**: Integrated web search and information retrieval
- 🎯 **Personalization**: Adaptive responses based on user preferences

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Windows/Linux/macOS
- Internet connection for AI services

### Installation

1. **Clone the repository:**
```bash
git clone https://github.com/yourusername/astra-ai.git
cd astra-ai
```

2. **Create virtual environment:**
```bash
python -m venv .venv
# Windows
.venv\Scripts\activate
# Linux/macOS
source .venv/bin/activate
```

3. **Install dependencies:**
```bash
pip install -r requirements.txt
```

4. **Set up API keys:**
```bash
# Copy example config
cp config/config.example.json config/config.json

# Edit with your API keys
# - Google AI API key for Gemini Vision
# - Groq API key for language models
# - SerpAPI key for web search (optional)
```

5. **Install OpenVoice (for TTS):**
```bash
# If you have Git installed:
python openvoice_system.py

# If you don't have Git:
python setup_openvoice_nogit.py
```

## 🎮 Usage

### Web Interface
```bash
python astra_ai/scripts/run_desktop_nova.py
```
Then open your browser to the displayed URL.

### Terminal Mode
```bash
# Interactive mode
python terminal_screen_analyzer.py

# Direct command
python terminal_screen_analyzer.py "analyze my screen"
```

### Screen Analysis Demo
```bash
python demo_screen_analysis.py
```

## 📁 Project Structure

```
astra-ai/
├── astra_ai/
│   ├── core/                 # Core AI components
│   │   ├── nova_ai.py       # Main AI class
│   │   ├── config_manager.py # Configuration management
│   │   └── memory/          # Memory system
│   ├── services/            # External service integrations
│   │   ├── gemini_vision.py # Google Gemini Vision API
│   │   └── enhanced_search.py # Web search capabilities
│   ├── speech/              # Voice synthesis
│   ├── ui/                  # User interfaces
│   │   └── Gini.15.html    # Web interface
│   └── utils/               # Utility functions
├── config/
│   └── config.json         # Configuration file
├── tests/                  # Test suite
├── openvoice_system.py     # OpenVoice TTS system
├── terminal_screen_analyzer.py # Terminal interface
└── requirements.txt        # Dependencies
```

## 🔧 Configuration

Edit `config/config.json`:

```json
{
  "api_keys": {
    "google_ai": "your-gemini-api-key",
    "groq": "your-groq-api-key",
    "serpapi": "your-serpapi-key"
  },
  "memory": {
    "enabled": true,
    "max_context_length": 4000
  },
  "voice": {
    "enabled": true,
    "speed": 1.0
  }
}
```

## 🧪 Testing

```bash
# Run all tests
pytest tests/

# Test specific components
python tests/gini_15_complete.py
python test_terminal_analyzer.py
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [OpenVoice](https://github.com/myshell-ai/OpenVoice) for TTS capabilities
- [Google Gemini](https://ai.google.dev/) for vision AI
- [Groq](https://groq.com/) for fast language model inference

## 🐛 Issues & Support

If you encounter any issues, please [open an issue](https://github.com/yourusername/astra-ai/issues) on GitHub.

## 🔮 Roadmap

- [ ] Multi-language support
- [ ] Plugin system
- [ ] Mobile app
- [ ] Advanced memory clustering
- [ ] Custom voice training
