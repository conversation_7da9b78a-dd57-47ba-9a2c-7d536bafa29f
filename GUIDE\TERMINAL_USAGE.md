# 🚀 Gini 1.5 Terminal Screen Analyzer

**AI-powered screen analysis directly in your terminal!**

## 🎯 Quick Start

### 1. Run the Terminal Analyzer
```bash
python terminal_screen_analyzer.py
```

### 2. Ask About Your Screen
```
🎤 You: what do you see on my screen?
```

### 3. Analyze Images
```
🎤 You: analyze screenshot.png
🎤 You: quick my_image.jpg
```

## 💬 Voice-like Commands

The system responds to natural language like:

### Screen Analysis
- `"what do you see on my screen?"`
- `"whats on my screen?"`
- `"describe my screen"`
- `"analyze my screen"`

### Custom Questions
- `"What errors do you see on my screen?"`
- `"What programming language is being used?"`
- `"Summarize the document on my screen"`
- `"What's the main content of this screen?"`

### Image File Analysis
- `"analyze path/to/image.jpg"`
- `"quick screenshot.png"`
- `"question image.jpg What is this showing?"`

## 🎮 Commands

| Command | Description | Example |
|---------|-------------|---------|
| `what do you see on my screen?` | Capture and analyze current screen | Full screen analysis |
| `analyze [image_path]` | Analyze specific image file | `analyze screenshot.png` |
| `quick [image_path]` | Quick summary of image | `quick photo.jpg` |
| `question [image] [question]` | Ask specific question about image | `question code.png What language is this?` |
| `help` | Show help information | Command reference |
| `quit` or `exit` | Exit the program | Close analyzer |

## 🔧 Setup

### Prerequisites
```bash
pip install google-generativeai mss pillow python-dotenv
```

### API Key Configuration
The API key is already configured in `astra_ai/.env`:
```
GOOGLE_AI_API_KEY=AIzaSyBe7dZj5mN_40Yo9t2xIeNgEcKdJWSe42w
```

## 📸 How It Works

1. **Screen Capture**: Uses `mss` library to capture your screen
2. **Image Processing**: Converts to base64 for API transmission
3. **AI Analysis**: Sends to Google Gemini 1.5 Pro Vision
4. **Smart Response**: Returns detailed analysis in terminal

## 🎯 Use Cases

### Development
```
🎤 You: what errors do you see on my screen?
🤖 AI: I can see a Python error in your terminal showing a "ModuleNotFoundError"...
```

### Documentation
```
🎤 You: summarize the document on my screen
🤖 AI: This appears to be a technical specification document about API endpoints...
```

### Code Review
```
🎤 You: what programming language is being used?
🤖 AI: The code on your screen appears to be Python, specifically showing a Flask application...
```

### General Analysis
```
🎤 You: what do you see on my screen?
🤖 AI: I can see a code editor with multiple files open, a terminal window, and a browser tab...
```

## 🚀 Example Session

```bash
$ python terminal_screen_analyzer.py

======================================================================
🚀 GINI 1.5 - TERMINAL SCREEN ANALYZER
======================================================================
🧠 Powered by Google Gemini 1.5 Pro Vision
📸 Ask 'What do you see on my screen?' or provide image paths
======================================================================

💡 Commands:
  - 'what do you see on my screen?' - Capture and analyze screen
  - 'analyze [image_path]' - Analyze specific image file
  - 'quick [image_path]' - Quick summary of image
  - 'help' - Show this help
  - 'quit' or 'exit' - Exit the program

🎤 You: what do you see on my screen?
📸 Capturing screen 1: 1920x1080
✅ Screen captured successfully: 1920x1080 pixels
🧠 Analyzing with Gemini Vision AI...

======================================================================
🤖 AI ANALYSIS RESULTS
======================================================================
📊 Type: detailed | Size: 1920x1080 | Model: gemini-1.5-pro
----------------------------------------------------------------------
I can see a code editor (appears to be VS Code) with multiple files open. 
The main content shows:

1. **Main Content**: A Python file is open showing what appears to be a 
   Flask application with API endpoints
2. **UI Elements**: File explorer on the left, main editor in center, 
   terminal at bottom
3. **Text Content**: Python code with imports, route definitions, and 
   function implementations
4. **Activity**: Active development work on a web application
5. **Context**: Software development environment for building APIs
6. **Notable Details**: Multiple tabs open, dark theme enabled, 
   integrated terminal showing command output

======================================================================

🎤 You: what programming language is this?
🤔 I'll capture your screen and answer that question...
📸 Capturing screen 1: 1920x1080
✅ Screen captured successfully: 1920x1080 pixels
🧠 Analyzing with Gemini Vision AI...

======================================================================
🤖 AI ANALYSIS RESULTS
======================================================================
📊 Type: detailed | Size: 1920x1080 | Model: gemini-1.5-pro
----------------------------------------------------------------------
The programming language shown on your screen is **Python**. 

Specifically, I can see:
- Python import statements (import os, sys, etc.)
- Flask framework code with @app.route decorators
- Python function definitions with def keywords
- Python-style indentation and syntax
- File extensions showing .py files

This appears to be a Flask web application written in Python.
======================================================================

🎤 You: quit
👋 Goodbye!
```

## 🛠️ Troubleshooting

### Common Issues

**"Screen capture failed"**
- Make sure `mss` is installed: `pip install mss`
- Check screen permissions on macOS/Linux

**"Gemini Vision not available"**
- Verify API key in `astra_ai/.env`
- Check internet connection
- Ensure `google-generativeai` is installed

**"Import errors"**
- Install required packages: `pip install -r requirements.txt`
- Check Python path and virtual environment

### Test Your Setup
```bash
python test_terminal_analyzer.py
```

## 🎉 Features

- ✅ **Natural Language**: Ask questions like you would to a human
- ✅ **Screen Capture**: Automatic screen capture and analysis
- ✅ **Image Files**: Analyze any image file format
- ✅ **Custom Questions**: Ask specific questions about content
- ✅ **Multiple Modes**: Detailed, quick, or custom analysis
- ✅ **Terminal Interface**: No GUI required, works in any terminal
- ✅ **Real-time**: Fast analysis with immediate results

## 🔮 Advanced Usage

### Command Line Mode
```bash
# Analyze screen directly
python terminal_screen_analyzer.py "what do you see on my screen?"

# Analyze specific image
python terminal_screen_analyzer.py "screenshot.png"
```

### Batch Processing
```bash
# Analyze multiple images
for img in *.png; do
    python terminal_screen_analyzer.py "analyze $img"
done
```

---

**Ready to analyze your screen with AI? Just ask: "What do you see on my screen?"** 🚀
