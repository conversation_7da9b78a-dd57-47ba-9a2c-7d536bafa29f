"""
Admin API for Enhanced Nova AI Server
Handles administrative functions, configuration, and system management
"""

import logging
import time
import os
from flask import Blueprint, request, jsonify
from typing import Dict, Any

# Create blueprint
admin_bp = Blueprint('admin', __name__)

# Services will be injected by the main server
services = None

logger = logging.getLogger(__name__)

@admin_bp.route('/config', methods=['GET'])
def get_configuration():
    """Get current configuration (masked sensitive values)"""
    try:
        if not services or 'config' not in services:
            return jsonify({'error': 'Configuration service not available'}), 500
        
        config_service = services['config']
        masked_config = config_service.get_masked()
        
        return jsonify({
            'success': True,
            'config': masked_config,
            'timestamp': time.time()
        })
        
    except Exception as e:
        logger.error(f"Error getting configuration: {e}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/config', methods=['POST'])
def update_configuration():
    """Update configuration values"""
    try:
        if not services or 'config' not in services:
            return jsonify({'error': 'Configuration service not available'}), 500
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No configuration data provided'}), 400
        
        config_service = services['config']
        
        # Update configuration values
        updated_keys = []
        for key, value in data.items():
            try:
                config_service.set(key, value)
                updated_keys.append(key)
                logger.info(f"Configuration updated: {key}")
            except Exception as e:
                logger.error(f"Failed to update config key {key}: {e}")
        
        if updated_keys:
            # Save configuration
            config_service.save()
            
            return jsonify({
                'success': True,
                'message': f'Updated {len(updated_keys)} configuration values',
                'updated_keys': updated_keys,
                'timestamp': time.time()
            })
        else:
            return jsonify({
                'success': False,
                'message': 'No configuration values were updated'
            }), 400
        
    except Exception as e:
        logger.error(f"Error updating configuration: {e}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/reload', methods=['POST'])
def reload_services():
    """Reload services and configuration"""
    try:
        data = request.get_json() or {}
        service_name = data.get('service', 'all')
        
        reload_results = {}
        
        if service_name == 'all' or service_name == 'config':
            # Reload configuration
            if 'config' in services:
                try:
                    services['config'].reload()
                    reload_results['config'] = {'status': 'success', 'message': 'Configuration reloaded'}
                    logger.info("Configuration reloaded")
                except Exception as e:
                    reload_results['config'] = {'status': 'error', 'message': str(e)}
        
        if service_name == 'all' or service_name == 'core':
            # Reload Nova AI core
            if 'core' in services:
                try:
                    success = services['core'].reload_nova_ai()
                    if success:
                        reload_results['core'] = {'status': 'success', 'message': 'Nova AI core reloaded'}
                        logger.info("Nova AI core reloaded")
                    else:
                        reload_results['core'] = {'status': 'error', 'message': 'Failed to reload Nova AI core'}
                except Exception as e:
                    reload_results['core'] = {'status': 'error', 'message': str(e)}
        
        if service_name == 'all' or service_name == 'cache':
            # Clear cache
            if 'cache' in services:
                try:
                    services['cache'].clear()
                    reload_results['cache'] = {'status': 'success', 'message': 'Cache cleared'}
                    logger.info("Cache cleared")
                except Exception as e:
                    reload_results['cache'] = {'status': 'error', 'message': str(e)}
        
        return jsonify({
            'success': True,
            'service': service_name,
            'results': reload_results,
            'timestamp': time.time()
        })
        
    except Exception as e:
        logger.error(f"Error reloading services: {e}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/services', methods=['GET'])
def get_service_status():
    """Get status of all services"""
    try:
        service_status = {
            'timestamp': time.time(),
            'services': {}
        }
        
        if services:
            for service_name, service in services.items():
                try:
                    if hasattr(service, 'health_check'):
                        health = service.health_check()
                        service_status['services'][service_name] = {
                            'available': True,
                            'health': health,
                            'type': type(service).__name__
                        }
                    else:
                        service_status['services'][service_name] = {
                            'available': True,
                            'health': {'status': 'unknown', 'message': 'No health check available'},
                            'type': type(service).__name__
                        }
                except Exception as e:
                    service_status['services'][service_name] = {
                        'available': False,
                        'error': str(e),
                        'type': type(service).__name__ if service else 'unknown'
                    }
        
        return jsonify(service_status)
        
    except Exception as e:
        logger.error(f"Error getting service status: {e}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/logs', methods=['GET'])
def get_logs():
    """Get system logs"""
    try:
        # Get parameters
        lines = request.args.get('lines', 100, type=int)
        level = request.args.get('level', 'INFO')
        service = request.args.get('service')
        
        log_data = {
            'timestamp': time.time(),
            'lines_requested': lines,
            'level_filter': level,
            'service_filter': service,
            'logs': []
        }
        
        # Get log file path from config
        log_file = 'logs/nova_ai.log'
        if services and 'config' in services:
            log_file = services['config'].get('logging.file', log_file)
        
        try:
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    all_lines = f.readlines()
                    
                    # Filter lines
                    filtered_lines = []
                    for line in all_lines:
                        # Level filter
                        if level and level.upper() not in line:
                            continue
                        
                        # Service filter
                        if service and service not in line:
                            continue
                        
                        filtered_lines.append(line.strip())
                    
                    # Get recent lines
                    recent_lines = filtered_lines[-lines:] if len(filtered_lines) > lines else filtered_lines
                    log_data['logs'] = recent_lines
                    log_data['total_lines'] = len(all_lines)
                    log_data['filtered_lines'] = len(filtered_lines)
            else:
                log_data['error'] = 'Log file not found'
                
        except Exception as e:
            log_data['error'] = f'Error reading log file: {str(e)}'
        
        return jsonify(log_data)
        
    except Exception as e:
        logger.error(f"Error getting logs: {e}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/cleanup', methods=['POST'])
def cleanup_system():
    """Perform system cleanup operations"""
    try:
        data = request.get_json() or {}
        cleanup_type = data.get('type', 'all')
        max_age_days = data.get('max_age_days', 30)
        
        cleanup_results = {}
        
        if cleanup_type in ['all', 'files']:
            # Clean up old files
            if 'file' in services:
                try:
                    services['file'].cleanup_old_files(max_age_days)
                    cleanup_results['files'] = {'status': 'success', 'message': f'Cleaned files older than {max_age_days} days'}
                    logger.info(f"File cleanup completed (max age: {max_age_days} days)")
                except Exception as e:
                    cleanup_results['files'] = {'status': 'error', 'message': str(e)}
        
        if cleanup_type in ['all', 'database']:
            # Clean up old database records
            if 'database' in services:
                try:
                    services['database'].cleanup_old_data(max_age_days)
                    cleanup_results['database'] = {'status': 'success', 'message': f'Cleaned database records older than {max_age_days} days'}
                    logger.info(f"Database cleanup completed (max age: {max_age_days} days)")
                except Exception as e:
                    cleanup_results['database'] = {'status': 'error', 'message': str(e)}
        
        if cleanup_type in ['all', 'cache']:
            # Clean up cache
            if 'cache' in services:
                try:
                    services['cache'].cleanup_expired()
                    cleanup_results['cache'] = {'status': 'success', 'message': 'Expired cache entries cleaned'}
                    logger.info("Cache cleanup completed")
                except Exception as e:
                    cleanup_results['cache'] = {'status': 'error', 'message': str(e)}
        
        if cleanup_type in ['all', 'sessions']:
            # Clean up old sessions
            if 'core' in services:
                try:
                    services['core'].cleanup_old_sessions(max_age_days * 24)  # Convert to hours
                    cleanup_results['sessions'] = {'status': 'success', 'message': f'Cleaned sessions older than {max_age_days} days'}
                    logger.info(f"Session cleanup completed (max age: {max_age_days} days)")
                except Exception as e:
                    cleanup_results['sessions'] = {'status': 'error', 'message': str(e)}
        
        return jsonify({
            'success': True,
            'cleanup_type': cleanup_type,
            'max_age_days': max_age_days,
            'results': cleanup_results,
            'timestamp': time.time()
        })
        
    except Exception as e:
        logger.error(f"Error performing cleanup: {e}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/backup', methods=['POST'])
def create_backup():
    """Create system backup"""
    try:
        data = request.get_json() or {}
        backup_type = data.get('type', 'full')
        
        backup_results = {
            'timestamp': time.time(),
            'backup_type': backup_type,
            'files': []
        }
        
        # Create backup directory
        backup_dir = f"backups/backup_{int(time.time())}"
        os.makedirs(backup_dir, exist_ok=True)
        
        if backup_type in ['full', 'config']:
            # Backup configuration
            try:
                if 'config' in services:
                    config_backup_path = os.path.join(backup_dir, 'config.yaml')
                    services['config'].save(config_backup_path)
                    backup_results['files'].append(config_backup_path)
                    logger.info(f"Configuration backed up to {config_backup_path}")
            except Exception as e:
                backup_results['config_error'] = str(e)
        
        if backup_type in ['full', 'database']:
            # Backup database
            try:
                if 'database' in services:
                    # This would depend on database type
                    # For SQLite, we could copy the file
                    # For PostgreSQL/MySQL, we'd need to dump
                    backup_results['database_note'] = 'Database backup not implemented for this database type'
            except Exception as e:
                backup_results['database_error'] = str(e)
        
        if backup_type in ['full', 'logs']:
            # Backup logs
            try:
                import shutil
                log_file = 'logs/nova_ai.log'
                if services and 'config' in services:
                    log_file = services['config'].get('logging.file', log_file)
                
                if os.path.exists(log_file):
                    log_backup_path = os.path.join(backup_dir, 'nova_ai.log')
                    shutil.copy2(log_file, log_backup_path)
                    backup_results['files'].append(log_backup_path)
                    logger.info(f"Logs backed up to {log_backup_path}")
            except Exception as e:
                backup_results['logs_error'] = str(e)
        
        backup_results['backup_directory'] = backup_dir
        backup_results['success'] = len(backup_results['files']) > 0
        
        return jsonify(backup_results)
        
    except Exception as e:
        logger.error(f"Error creating backup: {e}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/system-info', methods=['GET'])
def get_system_info():
    """Get comprehensive system information"""
    try:
        import platform
        import sys
        
        system_info = {
            'timestamp': time.time(),
            'python': {
                'version': sys.version,
                'executable': sys.executable,
                'platform': platform.platform(),
                'architecture': platform.architecture(),
                'processor': platform.processor()
            },
            'system': {
                'node': platform.node(),
                'system': platform.system(),
                'release': platform.release(),
                'version': platform.version(),
                'machine': platform.machine()
            },
            'application': {
                'name': 'Enhanced Nova AI Server',
                'version': '1.0.0',
                'services_count': len(services) if services else 0
            }
        }
        
        # Add resource information
        try:
            import psutil
            system_info['resources'] = {
                'cpu_count': psutil.cpu_count(),
                'cpu_count_logical': psutil.cpu_count(logical=True),
                'memory_total': psutil.virtual_memory().total,
                'disk_total': psutil.disk_usage('/').total,
                'boot_time': psutil.boot_time()
            }
        except Exception as e:
            system_info['resources_error'] = str(e)
        
        return jsonify(system_info)
        
    except Exception as e:
        logger.error(f"Error getting system info: {e}")
        return jsonify({'error': str(e)}), 500

@admin_bp.route('/health', methods=['GET'])
def admin_health_check():
    """Health check for admin functions"""
    try:
        health_status = {
            'status': 'healthy',
            'timestamp': time.time(),
            'admin_functions': {
                'config_access': 'config' in services if services else False,
                'log_access': os.path.exists('logs/nova_ai.log'),
                'backup_directory': os.path.exists('backups'),
                'services_available': len(services) if services else 0
            }
        }
        
        # Check if any critical admin functions are unavailable
        if not health_status['admin_functions']['config_access']:
            health_status['status'] = 'degraded'
            health_status['warnings'] = health_status.get('warnings', [])
            health_status['warnings'].append('Configuration service not available')
        
        return jsonify(health_status)
        
    except Exception as e:
        logger.error(f"Error in admin health check: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': time.time()
        }), 500
