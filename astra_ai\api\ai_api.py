"""
AI API for Enhanced Nova AI Server
Handles direct AI provider interactions and model management
"""

import logging
import time
import asyncio
from flask import Blueprint, request, jsonify
from typing import Dict, Any, List, Optional

# Create blueprint
ai_bp = Blueprint('ai', __name__)

# Services will be injected by the main server
services = None

logger = logging.getLogger(__name__)

@ai_bp.route('/providers', methods=['GET'])
def get_providers():
    """Get list of available AI providers"""
    try:
        if not services or 'ai' not in services:
            return jsonify({'error': 'AI service not available'}), 500
        
        ai_service = services['ai']
        
        # Get provider status
        provider_status = {}
        for provider_name, status in ai_service.provider_status.items():
            provider_status[provider_name] = {
                'available': status.available,
                'last_check': status.last_check,
                'error_count': status.error_count,
                'avg_response_time': status.avg_response_time
            }
        
        return jsonify({
            'providers': provider_status,
            'available_count': len([p for p in provider_status.values() if p['available']])
        })
        
    except Exception as e:
        logger.error(f"Error getting providers: {e}")
        return jsonify({'error': str(e)}), 500

@ai_bp.route('/generate', methods=['POST'])
def generate_response():
    """Generate AI response using specified provider"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        messages = data.get('messages', [])
        provider = data.get('provider')  # Optional - will use load balancing if not specified
        model = data.get('model')
        max_tokens = data.get('max_tokens', 4000)
        temperature = data.get('temperature', 0.7)
        
        # Validate input
        if not messages:
            return jsonify({'error': 'No messages provided'}), 400
        
        if not isinstance(messages, list):
            return jsonify({'error': 'Messages must be a list'}), 400
        
        if not services or 'ai' not in services:
            return jsonify({'error': 'AI service not available'}), 500
        
        # Generate response
        ai_service = services['ai']
        response = asyncio.run(ai_service.get_response(
            messages=messages,
            provider=provider,
            model=model,
            max_tokens=max_tokens,
            temperature=temperature
        ))
        
        # Log the request
        logger.info(f"AI response generated using {response.provider} in {response.response_time:.2f}s")
        
        # Save to database if available
        if 'database' in services:
            try:
                from services.database_service import AIRequest
                request_data = AIRequest(
                    session_id=data.get('session_id', 'api'),
                    provider=response.provider,
                    model=response.model,
                    request_data={'messages': messages, 'max_tokens': max_tokens, 'temperature': temperature},
                    response_data={'content': response.content, 'cached': response.cached},
                    tokens_used=response.tokens_used,
                    response_time=response.response_time,
                    success=True
                )
                services['database'].save_ai_request(request_data)
            except Exception as e:
                logger.warning(f"Failed to save AI request to database: {e}")
        
        return jsonify({
            'content': response.content,
            'provider': response.provider,
            'model': response.model,
            'tokens_used': response.tokens_used,
            'response_time': response.response_time,
            'cached': response.cached,
            'metadata': response.metadata
        })
        
    except Exception as e:
        logger.error(f"Error generating AI response: {e}")
        
        # Save error to database if available
        if 'database' in services and data:
            try:
                from services.database_service import AIRequest
                error_data = AIRequest(
                    session_id=data.get('session_id', 'api'),
                    provider=data.get('provider', 'unknown'),
                    model=data.get('model', 'unknown'),
                    request_data=data,
                    success=False,
                    error_message=str(e)
                )
                services['database'].save_ai_request(error_data)
            except Exception as db_error:
                logger.warning(f"Failed to save error to database: {db_error}")
        
        return jsonify({'error': str(e)}), 500

@ai_bp.route('/health', methods=['GET'])
def health_check():
    """Perform health check on all AI providers"""
    try:
        if not services or 'ai' not in services:
            return jsonify({'error': 'AI service not available'}), 500
        
        ai_service = services['ai']
        health_status = asyncio.run(ai_service.health_check())
        
        return jsonify({
            'timestamp': time.time(),
            'providers': health_status,
            'overall_status': 'healthy' if any(p.get('status') == 'healthy' for p in health_status.values()) else 'unhealthy'
        })
        
    except Exception as e:
        logger.error(f"Error in AI health check: {e}")
        return jsonify({'error': str(e)}), 500

@ai_bp.route('/stats', methods=['GET'])
def get_ai_stats():
    """Get AI service statistics"""
    try:
        if not services or 'ai' not in services:
            return jsonify({'error': 'AI service not available'}), 500
        
        ai_service = services['ai']
        stats = ai_service.get_stats()
        
        return jsonify(stats)
        
    except Exception as e:
        logger.error(f"Error getting AI stats: {e}")
        return jsonify({'error': str(e)}), 500

@ai_bp.route('/cache', methods=['DELETE'])
def clear_cache():
    """Clear AI response cache"""
    try:
        if not services or 'ai' not in services:
            return jsonify({'error': 'AI service not available'}), 500
        
        ai_service = services['ai']
        ai_service.clear_cache()
        
        return jsonify({
            'message': 'AI cache cleared successfully',
            'timestamp': time.time()
        })
        
    except Exception as e:
        logger.error(f"Error clearing AI cache: {e}")
        return jsonify({'error': str(e)}), 500

@ai_bp.route('/models', methods=['GET'])
def get_available_models():
    """Get available models for each provider"""
    try:
        if not services or 'config' not in services:
            return jsonify({'error': 'Configuration service not available'}), 500
        
        config = services['config']
        providers_config = config.get('ai.providers', {})
        
        models = {}
        for provider_name, provider_config in providers_config.items():
            if provider_config.get('enabled', False):
                models[provider_name] = {
                    'default_model': provider_config.get('model', 'unknown'),
                    'max_tokens': provider_config.get('max_tokens', 4000),
                    'available': provider_name in services.get('ai', {}).providers if 'ai' in services else False
                }
        
        return jsonify({
            'models': models,
            'provider_count': len(models)
        })
        
    except Exception as e:
        logger.error(f"Error getting available models: {e}")
        return jsonify({'error': str(e)}), 500

@ai_bp.route('/load-balancing', methods=['GET', 'POST'])
def manage_load_balancing():
    """Get or update load balancing configuration"""
    try:
        if not services or 'ai' not in services:
            return jsonify({'error': 'AI service not available'}), 500
        
        ai_service = services['ai']
        
        if request.method == 'GET':
            # Get current load balancing configuration
            return jsonify({
                'strategy': ai_service.load_balancer.strategy,
                'current_index': ai_service.load_balancer.current_index,
                'provider_weights': ai_service.load_balancer.provider_weights,
                'provider_stats': ai_service.load_balancer.provider_stats
            })
        
        elif request.method == 'POST':
            # Update load balancing configuration
            data = request.get_json()
            if not data:
                return jsonify({'error': 'No data provided'}), 400
            
            strategy = data.get('strategy')
            if strategy and strategy in ['round_robin', 'random', 'weighted', 'fastest']:
                ai_service.load_balancer.strategy = strategy
            
            weights = data.get('provider_weights')
            if weights and isinstance(weights, dict):
                ai_service.load_balancer.provider_weights.update(weights)
            
            return jsonify({
                'message': 'Load balancing configuration updated',
                'strategy': ai_service.load_balancer.strategy,
                'provider_weights': ai_service.load_balancer.provider_weights
            })
            
    except Exception as e:
        logger.error(f"Error managing load balancing: {e}")
        return jsonify({'error': str(e)}), 500

@ai_bp.route('/batch', methods=['POST'])
def batch_generate():
    """Generate multiple AI responses in batch"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        requests = data.get('requests', [])
        if not requests or not isinstance(requests, list):
            return jsonify({'error': 'No requests provided or invalid format'}), 400
        
        if len(requests) > 10:  # Limit batch size
            return jsonify({'error': 'Batch size too large (max 10)'}), 400
        
        if not services or 'ai' not in services:
            return jsonify({'error': 'AI service not available'}), 500
        
        ai_service = services['ai']
        results = []
        
        # Process each request
        for i, req in enumerate(requests):
            try:
                messages = req.get('messages', [])
                if not messages:
                    results.append({'error': 'No messages provided', 'index': i})
                    continue
                
                response = asyncio.run(ai_service.get_response(
                    messages=messages,
                    provider=req.get('provider'),
                    model=req.get('model'),
                    max_tokens=req.get('max_tokens', 4000),
                    temperature=req.get('temperature', 0.7)
                ))
                
                results.append({
                    'index': i,
                    'content': response.content,
                    'provider': response.provider,
                    'model': response.model,
                    'tokens_used': response.tokens_used,
                    'response_time': response.response_time,
                    'cached': response.cached
                })
                
            except Exception as e:
                results.append({
                    'index': i,
                    'error': str(e)
                })
        
        return jsonify({
            'results': results,
            'total_requests': len(requests),
            'successful_requests': len([r for r in results if 'error' not in r])
        })
        
    except Exception as e:
        logger.error(f"Error in batch generate: {e}")
        return jsonify({'error': str(e)}), 500

@ai_bp.route('/usage', methods=['GET'])
def get_usage_stats():
    """Get detailed usage statistics"""
    try:
        if not services or 'ai' not in services:
            return jsonify({'error': 'AI service not available'}), 500
        
        ai_service = services['ai']
        stats = ai_service.get_stats()
        
        # Add time-based filtering if requested
        hours = request.args.get('hours', 24, type=int)
        
        # Get database stats if available
        if 'database' in services:
            try:
                db_stats = services['database'].get_performance_metrics('ai_requests', hours)
                stats['recent_requests'] = len(db_stats)
            except Exception as e:
                logger.warning(f"Failed to get database usage stats: {e}")
        
        return jsonify(stats)
        
    except Exception as e:
        logger.error(f"Error getting usage stats: {e}")
        return jsonify({'error': str(e)}), 500

@ai_bp.route('/test', methods=['POST'])
def test_provider():
    """Test a specific AI provider"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        provider = data.get('provider')
        if not provider:
            return jsonify({'error': 'Provider not specified'}), 400
        
        if not services or 'ai' not in services:
            return jsonify({'error': 'AI service not available'}), 500
        
        ai_service = services['ai']
        
        # Test with a simple message
        test_messages = [{"role": "user", "content": "Hello, this is a test message."}]
        
        start_time = time.time()
        response = asyncio.run(ai_service.get_response(
            messages=test_messages,
            provider=provider,
            max_tokens=50
        ))
        test_time = time.time() - start_time
        
        return jsonify({
            'provider': provider,
            'status': 'success',
            'response_time': test_time,
            'model': response.model,
            'tokens_used': response.tokens_used,
            'test_response': response.content[:100] + '...' if len(response.content) > 100 else response.content
        })
        
    except Exception as e:
        logger.error(f"Error testing provider: {e}")
        return jsonify({
            'provider': data.get('provider', 'unknown') if data else 'unknown',
            'status': 'error',
            'error': str(e)
        }), 500
