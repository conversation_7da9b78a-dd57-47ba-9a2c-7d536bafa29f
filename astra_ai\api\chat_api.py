"""
Chat API for Enhanced Nova AI Server
Handles chat interactions and conversation management
"""

import logging
import time
import uuid
from flask import Blueprint, request, jsonify, session
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from typing import Dict, Any, Optional
import asyncio

# Create blueprint
chat_bp = Blueprint('chat', __name__)

# Services will be injected by the main server
services = None

logger = logging.getLogger(__name__)

@chat_bp.route('/message', methods=['POST'])
def send_message():
    """Send a chat message and get AI response"""
    try:
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        user_message = data.get('message', '').strip()
        session_id = data.get('session_id', str(uuid.uuid4()))
        user_location = data.get('user_location', '')
        context = data.get('context', {})
        
        # Validate input
        if not user_message:
            return jsonify({'error': 'No message provided'}), 400
        
        # Check if core service is available
        if not services or 'core' not in services:
            return jsonify({'error': 'Core service not available'}), 500
        
        # Process message through core service
        result = asyncio.run(services['core'].process_chat_message(
            message=user_message,
            session_id=session_id,
            user_location=user_location,
            context=context
        ))
        
        if not result.get('success'):
            return jsonify({
                'error': result.get('error', 'Unknown error'),
                'session_id': session_id
            }), 500
        
        # Log the interaction
        logger.info(f"Chat message processed for session {session_id[:8]}... in {result.get('response_time', 0):.2f}s")
        
        # Save to database if available
        if 'database' in services:
            try:
                from services.database_service import ChatMessage
                message_data = ChatMessage(
                    session_id=session_id,
                    role='user',
                    content=user_message,
                    response_time=result.get('response_time', 0)
                )
                services['database'].save_chat_message(message_data)
                
                response_data = ChatMessage(
                    session_id=session_id,
                    role='assistant',
                    content=result.get('response', ''),
                    response_time=result.get('response_time', 0)
                )
                services['database'].save_chat_message(response_data)
            except Exception as e:
                logger.warning(f"Failed to save chat to database: {e}")
        
        return jsonify({
            'response': result.get('response'),
            'session_id': session_id,
            'location': result.get('location'),
            'response_time': result.get('response_time'),
            'success': True
        })
        
    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        return jsonify({'error': str(e)}), 500

@chat_bp.route('/history/<session_id>', methods=['GET'])
def get_chat_history(session_id: str):
    """Get chat history for a session"""
    try:
        limit = request.args.get('limit', 50, type=int)
        
        if not services or 'core' not in services:
            return jsonify({'error': 'Core service not available'}), 500
        
        history = services['core'].get_session_history(session_id)
        
        # Limit results
        if len(history) > limit:
            history = history[-limit:]
        
        return jsonify({
            'session_id': session_id,
            'history': history,
            'count': len(history)
        })
        
    except Exception as e:
        logger.error(f"Error getting chat history: {e}")
        return jsonify({'error': str(e)}), 500

@chat_bp.route('/sessions', methods=['GET'])
def get_active_sessions():
    """Get list of active chat sessions"""
    try:
        if not services or 'core' not in services:
            return jsonify({'error': 'Core service not available'}), 500
        
        sessions = services['core'].get_active_sessions()
        
        # Get session details
        session_details = []
        for session_id in sessions:
            stats = services['core'].get_session_stats(session_id)
            session_details.append(stats)
        
        return jsonify({
            'sessions': session_details,
            'count': len(session_details)
        })
        
    except Exception as e:
        logger.error(f"Error getting active sessions: {e}")
        return jsonify({'error': str(e)}), 500

@chat_bp.route('/sessions/<session_id>', methods=['DELETE'])
def clear_session(session_id: str):
    """Clear chat history for a session"""
    try:
        if not services or 'core' not in services:
            return jsonify({'error': 'Core service not available'}), 500
        
        success = services['core'].clear_session_history(session_id)
        
        if success:
            return jsonify({
                'message': f'Session {session_id} cleared successfully',
                'session_id': session_id
            })
        else:
            return jsonify({
                'error': f'Session {session_id} not found'
            }), 404
            
    except Exception as e:
        logger.error(f"Error clearing session: {e}")
        return jsonify({'error': str(e)}), 500

@chat_bp.route('/location/<session_id>', methods=['GET', 'POST'])
def manage_user_location(session_id: str):
    """Get or set user location for a session"""
    try:
        if not services or 'core' not in services:
            return jsonify({'error': 'Core service not available'}), 500
        
        if request.method == 'GET':
            # Get current location
            location = services['core'].get_user_location(session_id)
            return jsonify({
                'session_id': session_id,
                'location': location
            })
        
        elif request.method == 'POST':
            # Set new location
            data = request.get_json()
            if not data or 'location' not in data:
                return jsonify({'error': 'Location not provided'}), 400
            
            location = data['location'].strip()
            if not location:
                return jsonify({'error': 'Invalid location'}), 400
            
            services['core'].set_user_location(session_id, location)
            
            return jsonify({
                'message': 'Location updated successfully',
                'session_id': session_id,
                'location': location
            })
            
    except Exception as e:
        logger.error(f"Error managing user location: {e}")
        return jsonify({'error': str(e)}), 500

@chat_bp.route('/memory/search', methods=['POST'])
def search_memories():
    """Search through AI memories"""
    try:
        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({'error': 'Query not provided'}), 400
        
        query = data['query'].strip()
        limit = data.get('limit', 5)
        
        if not query:
            return jsonify({'error': 'Invalid query'}), 400
        
        if not services or 'core' not in services:
            return jsonify({'error': 'Core service not available'}), 500
        
        memories = asyncio.run(services['core'].search_memories(query, limit))
        
        return jsonify({
            'query': query,
            'memories': memories,
            'count': len(memories)
        })
        
    except Exception as e:
        logger.error(f"Error searching memories: {e}")
        return jsonify({'error': str(e)}), 500

@chat_bp.route('/memory/status', methods=['GET'])
def get_memory_status():
    """Get memory system status"""
    try:
        if not services or 'core' not in services:
            return jsonify({'error': 'Core service not available'}), 500
        
        status = services['core'].get_memory_status()
        return jsonify(status)
        
    except Exception as e:
        logger.error(f"Error getting memory status: {e}")
        return jsonify({'error': str(e)}), 500

@chat_bp.route('/time', methods=['POST'])
def get_time_for_location():
    """Get current time for a specific location"""
    try:
        data = request.get_json()
        location = data.get('location', '') if data else ''
        
        if not services or 'core' not in services:
            return jsonify({'error': 'Core service not available'}), 500
        
        if not location:
            # Return server's local time
            from datetime import datetime
            now = datetime.now()
            return jsonify({'time': now.strftime('%I:%M %p')})
        
        time_str = asyncio.run(services['core'].get_time_for_location(location))
        
        return jsonify({
            'location': location,
            'time': time_str
        })
        
    except Exception as e:
        logger.error(f"Error getting time: {e}")
        return jsonify({'time': '--:--'})

@chat_bp.route('/stats', methods=['GET'])
def get_chat_stats():
    """Get chat service statistics"""
    try:
        if not services or 'core' not in services:
            return jsonify({'error': 'Core service not available'}), 500
        
        stats = services['core'].get_stats()
        
        # Add additional stats if database is available
        if 'database' in services:
            try:
                db_stats = services['database'].get_database_stats()
                stats['database'] = db_stats
            except Exception as e:
                logger.warning(f"Failed to get database stats: {e}")
        
        return jsonify(stats)
        
    except Exception as e:
        logger.error(f"Error getting chat stats: {e}")
        return jsonify({'error': str(e)}), 500

@chat_bp.route('/reload', methods=['POST'])
def reload_nova_ai():
    """Reload Nova AI core module"""
    try:
        if not services or 'core' not in services:
            return jsonify({'error': 'Core service not available'}), 500
        
        success = services['core'].reload_nova_ai()
        
        if success:
            return jsonify({
                'message': 'Nova AI reloaded successfully',
                'timestamp': time.time()
            })
        else:
            return jsonify({
                'error': 'Failed to reload Nova AI'
            }), 500
            
    except Exception as e:
        logger.error(f"Error reloading Nova AI: {e}")
        return jsonify({'error': str(e)}), 500

@chat_bp.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for chat service"""
    try:
        health_status = {
            'status': 'healthy',
            'timestamp': time.time(),
            'services': {}
        }
        
        # Check core service
        if services and 'core' in services:
            try:
                stats = services['core'].get_stats()
                health_status['services']['core'] = {
                    'status': 'healthy',
                    'nova_ai_initialized': stats.get('nova_ai_initialized', False)
                }
            except Exception as e:
                health_status['services']['core'] = {
                    'status': 'unhealthy',
                    'error': str(e)
                }
        else:
            health_status['services']['core'] = {
                'status': 'unavailable'
            }
        
        # Check database service
        if services and 'database' in services:
            try:
                db_stats = services['database'].get_database_stats()
                health_status['services']['database'] = {
                    'status': 'healthy',
                    'type': db_stats.get('type', 'unknown')
                }
            except Exception as e:
                health_status['services']['database'] = {
                    'status': 'unhealthy',
                    'error': str(e)
                }
        else:
            health_status['services']['database'] = {
                'status': 'unavailable'
            }
        
        # Determine overall status
        service_statuses = [s.get('status') for s in health_status['services'].values()]
        if 'unhealthy' in service_statuses:
            health_status['status'] = 'degraded'
        elif 'unavailable' in service_statuses:
            health_status['status'] = 'limited'
        
        return jsonify(health_status)
        
    except Exception as e:
        logger.error(f"Error in health check: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': time.time()
        }), 500
