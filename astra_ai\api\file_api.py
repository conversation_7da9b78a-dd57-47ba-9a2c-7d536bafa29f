"""
File API for Enhanced Nova AI Server
Handles file upload, processing, and management endpoints
"""

import logging
import os
from flask import Blueprint, request, jsonify, send_file
from werkzeug.utils import secure_filename
from typing import Dict, Any

# Create blueprint
file_bp = Blueprint('files', __name__)

# Services will be injected by the main server
services = None

logger = logging.getLogger(__name__)

@file_bp.route('/upload', methods=['POST'])
def upload_file():
    """Upload a file"""
    try:
        if not services or 'file' not in services:
            return jsonify({'error': 'File service not available'}), 500
        
        # Check if file is present
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        # Get user ID (from auth if available)
        user_id = 'anonymous'
        if 'auth' in services:
            # Try to get user from auth header
            auth_header = request.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                token = auth_header[7:]
                user_data = services['auth'].verify_jwt_token(token)
                if user_data:
                    user_id = user_data.get('username', 'anonymous')
        
        # Validate file
        file_service = services['file']
        validation = file_service.validate_file(file.filename, len(file.read()))
        file.seek(0)  # Reset file pointer
        
        if not validation['valid']:
            return jsonify({'error': validation['error']}), 400
        
        # Save file
        result = file_service.save_file(file, file.filename, user_id)
        
        if not result['success']:
            return jsonify({'error': result['error']}), 500
        
        logger.info(f"File uploaded successfully: {result['file_id']}")
        
        return jsonify({
            'success': True,
            'file_id': result['file_id'],
            'filename': result['filename'],
            'size': result['size'],
            'hash': result['hash']
        })
        
    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        return jsonify({'error': str(e)}), 500

@file_bp.route('/<file_id>', methods=['GET'])
def get_file_info(file_id: str):
    """Get file information"""
    try:
        if not services or 'file' not in services:
            return jsonify({'error': 'File service not available'}), 500
        
        file_service = services['file']
        metadata = file_service.get_file(file_id)
        
        if not metadata:
            return jsonify({'error': 'File not found'}), 404
        
        # Return safe metadata (exclude sensitive paths)
        safe_metadata = {
            'file_id': metadata['file_id'],
            'original_filename': metadata['original_filename'],
            'file_size': metadata['file_size'],
            'mime_type': metadata['mime_type'],
            'upload_time': metadata['upload_time'],
            'processed': metadata.get('processed', False)
        }
        
        return jsonify(safe_metadata)
        
    except Exception as e:
        logger.error(f"Error getting file info: {e}")
        return jsonify({'error': str(e)}), 500

@file_bp.route('/<file_id>/download', methods=['GET'])
def download_file(file_id: str):
    """Download a file"""
    try:
        if not services or 'file' not in services:
            return jsonify({'error': 'File service not available'}), 500
        
        file_service = services['file']
        file_path = file_service.get_file_path(file_id)
        
        if not file_path:
            return jsonify({'error': 'File not found'}), 404
        
        metadata = file_service.get_file(file_id)
        original_filename = metadata['original_filename']
        
        return send_file(
            file_path,
            as_attachment=True,
            download_name=original_filename,
            mimetype=metadata.get('mime_type')
        )
        
    except Exception as e:
        logger.error(f"Error downloading file: {e}")
        return jsonify({'error': str(e)}), 500

@file_bp.route('/<file_id>', methods=['DELETE'])
def delete_file(file_id: str):
    """Delete a file"""
    try:
        if not services or 'file' not in services:
            return jsonify({'error': 'File service not available'}), 500
        
        file_service = services['file']
        success = file_service.delete_file(file_id)
        
        if not success:
            return jsonify({'error': 'File not found or could not be deleted'}), 404
        
        logger.info(f"File deleted: {file_id}")
        
        return jsonify({
            'success': True,
            'message': f'File {file_id} deleted successfully'
        })
        
    except Exception as e:
        logger.error(f"Error deleting file: {e}")
        return jsonify({'error': str(e)}), 500

@file_bp.route('/', methods=['GET'])
def list_files():
    """List files"""
    try:
        if not services or 'file' not in services:
            return jsonify({'error': 'File service not available'}), 500
        
        # Get query parameters
        limit = request.args.get('limit', 100, type=int)
        user_id = request.args.get('user_id')
        
        # Get user ID from auth if not provided
        if not user_id and 'auth' in services:
            auth_header = request.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                token = auth_header[7:]
                user_data = services['auth'].verify_jwt_token(token)
                if user_data:
                    user_id = user_data.get('username')
        
        file_service = services['file']
        files = file_service.list_files(user_id, limit)
        
        return jsonify({
            'success': True,
            'files': files,
            'count': len(files)
        })
        
    except Exception as e:
        logger.error(f"Error listing files: {e}")
        return jsonify({'error': str(e)}), 500

@file_bp.route('/<file_id>/process', methods=['POST'])
def process_file(file_id: str):
    """Process a file (extract content, analyze, etc.)"""
    try:
        if not services or 'file' not in services:
            return jsonify({'error': 'File service not available'}), 500
        
        file_service = services['file']
        result = file_service.process_file(file_id)
        
        if not result['success']:
            return jsonify({'error': result['error']}), 400
        
        logger.info(f"File processed: {file_id}")
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error processing file: {e}")
        return jsonify({'error': str(e)}), 500

@file_bp.route('/<file_id>/analyze', methods=['POST'])
def analyze_file_with_ai():
    """Analyze file content using AI"""
    try:
        if not services or 'file' not in services or 'ai' not in services:
            return jsonify({'error': 'Required services not available'}), 500
        
        data = request.get_json() or {}
        analysis_type = data.get('type', 'general')
        
        file_service = services['file']
        ai_service = services['ai']
        
        # Get file metadata
        metadata = file_service.get_file(file_id)
        if not metadata:
            return jsonify({'error': 'File not found'}), 404
        
        # Process file if not already processed
        if not metadata.get('processed'):
            process_result = file_service.process_file(file_id)
            if not process_result['success']:
                return jsonify({'error': f'Failed to process file: {process_result["error"]}'}), 400
            metadata = file_service.get_file(file_id)  # Refresh metadata
        
        # Get file content
        processing_result = metadata.get('processing_result', {})
        content = processing_result.get('content', '')
        
        if not content:
            return jsonify({'error': 'No content available for analysis'}), 400
        
        # Create AI analysis prompt
        if analysis_type == 'summary':
            prompt = f"Please provide a concise summary of the following content:\n\n{content}"
        elif analysis_type == 'keywords':
            prompt = f"Extract the main keywords and topics from the following content:\n\n{content}"
        elif analysis_type == 'sentiment':
            prompt = f"Analyze the sentiment and tone of the following content:\n\n{content}"
        else:
            prompt = f"Analyze the following content and provide insights:\n\n{content}"
        
        messages = [
            {"role": "system", "content": "You are an expert content analyst. Provide clear, structured analysis."},
            {"role": "user", "content": prompt}
        ]
        
        # Get AI analysis
        import asyncio
        response = asyncio.run(ai_service.get_response(messages=messages, max_tokens=1000))
        
        logger.info(f"File analyzed with AI: {file_id}")
        
        return jsonify({
            'success': True,
            'file_id': file_id,
            'analysis_type': analysis_type,
            'analysis': response.content,
            'provider': response.provider,
            'response_time': response.response_time
        })
        
    except Exception as e:
        logger.error(f"Error analyzing file with AI: {e}")
        return jsonify({'error': str(e)}), 500

@file_bp.route('/stats', methods=['GET'])
def get_file_stats():
    """Get file storage statistics"""
    try:
        if not services or 'file' not in services:
            return jsonify({'error': 'File service not available'}), 500
        
        file_service = services['file']
        stats = file_service.get_storage_stats()
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"Error getting file stats: {e}")
        return jsonify({'error': str(e)}), 500

@file_bp.route('/cleanup', methods=['POST'])
def cleanup_old_files():
    """Clean up old files"""
    try:
        if not services or 'file' not in services:
            return jsonify({'error': 'File service not available'}), 500
        
        data = request.get_json() or {}
        max_age_days = data.get('max_age_days', 30)
        
        file_service = services['file']
        file_service.cleanup_old_files(max_age_days)
        
        logger.info(f"File cleanup completed (max age: {max_age_days} days)")
        
        return jsonify({
            'success': True,
            'message': f'Cleanup completed for files older than {max_age_days} days'
        })
        
    except Exception as e:
        logger.error(f"Error cleaning up files: {e}")
        return jsonify({'error': str(e)}), 500

@file_bp.route('/health', methods=['GET'])
def file_health_check():
    """Health check for file service"""
    try:
        if not services or 'file' not in services:
            return jsonify({'error': 'File service not available'}), 500
        
        file_service = services['file']
        health_status = file_service.health_check()
        
        return jsonify(health_status)
        
    except Exception as e:
        logger.error(f"Error in file health check: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e)
        }), 500
