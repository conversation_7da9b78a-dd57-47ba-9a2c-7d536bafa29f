"""
Integration API for Enhanced Nova AI Server
Handles cross-service communication and external integrations
"""

import logging
import time
import asyncio
from flask import Blueprint, request, jsonify
from typing import Dict, Any

# Create blueprint
integration_bp = Blueprint('integration', __name__)

# Services will be injected by the main server
services = None

logger = logging.getLogger(__name__)

@integration_bp.route('/nova-ai/chat', methods=['POST'])
def nova_ai_chat():
    """Direct integration with Nova AI chat functionality"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        message = data.get('message', '').strip()
        session_id = data.get('session_id', 'integration')
        user_location = data.get('user_location', '')
        context = data.get('context', {})
        
        if not message:
            return jsonify({'error': 'No message provided'}), 400
        
        if not services or 'core' not in services:
            return jsonify({'error': 'Core service not available'}), 500
        
        # Process through core service
        result = asyncio.run(services['core'].process_chat_message(
            message=message,
            session_id=session_id,
            user_location=user_location,
            context=context
        ))
        
        logger.info(f"Nova AI integration chat processed for session {session_id}")
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error in Nova AI chat integration: {e}")
        return jsonify({'error': str(e)}), 500

@integration_bp.route('/ai/multi-provider', methods=['POST'])
def multi_provider_ai():
    """Get responses from multiple AI providers simultaneously"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        messages = data.get('messages', [])
        providers = data.get('providers', [])
        
        if not messages:
            return jsonify({'error': 'No messages provided'}), 400
        
        if not services or 'ai' not in services:
            return jsonify({'error': 'AI service not available'}), 500
        
        ai_service = services['ai']
        
        # Get available providers if none specified
        if not providers:
            providers = ai_service._get_available_providers()
        
        if not providers:
            return jsonify({'error': 'No AI providers available'}), 500
        
        # Get responses from multiple providers
        responses = {}
        for provider in providers:
            try:
                response = asyncio.run(ai_service.get_response(
                    messages=messages,
                    provider=provider,
                    max_tokens=data.get('max_tokens', 1000)
                ))
                
                responses[provider] = {
                    'content': response.content,
                    'model': response.model,
                    'tokens_used': response.tokens_used,
                    'response_time': response.response_time,
                    'cached': response.cached
                }
                
            except Exception as e:
                responses[provider] = {
                    'error': str(e)
                }
        
        logger.info(f"Multi-provider AI responses generated for {len(providers)} providers")
        
        return jsonify({
            'success': True,
            'providers': list(responses.keys()),
            'responses': responses,
            'timestamp': time.time()
        })
        
    except Exception as e:
        logger.error(f"Error in multi-provider AI: {e}")
        return jsonify({'error': str(e)}), 500

@integration_bp.route('/widgets/sync', methods=['POST'])
def sync_widget_data():
    """Synchronize widget data across instances"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        widget_type = data.get('widget_type')
        widget_data = data.get('data')
        action = data.get('action', 'update')  # update, get, delete
        
        if not widget_type:
            return jsonify({'error': 'Widget type not specified'}), 400
        
        sync_results = {}
        
        if widget_type == 'notepad':
            # Handle notepad synchronization
            if action == 'update' and widget_data:
                # Save notepad data
                try:
                    import json
                    import os
                    
                    widget_data_dir = "data/widgets"
                    os.makedirs(widget_data_dir, exist_ok=True)
                    
                    notepad_file = os.path.join(widget_data_dir, "notepad_notes.json")
                    with open(notepad_file, 'w', encoding='utf-8') as f:
                        json.dump(widget_data, f, indent=2, ensure_ascii=False)
                    
                    sync_results['notepad'] = {
                        'status': 'success',
                        'action': 'updated',
                        'notes_count': len(widget_data.get('notes', []))
                    }
                    
                except Exception as e:
                    sync_results['notepad'] = {'status': 'error', 'message': str(e)}
            
            elif action == 'get':
                # Get notepad data
                try:
                    import json
                    import os
                    
                    notepad_file = "data/widgets/notepad_notes.json"
                    if os.path.exists(notepad_file):
                        with open(notepad_file, 'r', encoding='utf-8') as f:
                            notepad_data = json.load(f)
                        
                        sync_results['notepad'] = {
                            'status': 'success',
                            'action': 'retrieved',
                            'data': notepad_data
                        }
                    else:
                        sync_results['notepad'] = {
                            'status': 'success',
                            'action': 'retrieved',
                            'data': {'notes': []}
                        }
                        
                except Exception as e:
                    sync_results['notepad'] = {'status': 'error', 'message': str(e)}
        
        elif widget_type == 'search_history':
            # Handle search history synchronization
            if action == 'update' and widget_data:
                try:
                    import json
                    import os
                    
                    widget_data_dir = "data/widgets"
                    os.makedirs(widget_data_dir, exist_ok=True)
                    
                    search_file = os.path.join(widget_data_dir, "search_history.json")
                    with open(search_file, 'w', encoding='utf-8') as f:
                        json.dump(widget_data, f, indent=2, ensure_ascii=False)
                    
                    sync_results['search_history'] = {
                        'status': 'success',
                        'action': 'updated',
                        'searches_count': len(widget_data.get('searches', []))
                    }
                    
                except Exception as e:
                    sync_results['search_history'] = {'status': 'error', 'message': str(e)}
        
        else:
            return jsonify({'error': f'Unsupported widget type: {widget_type}'}), 400
        
        return jsonify({
            'success': True,
            'widget_type': widget_type,
            'action': action,
            'results': sync_results,
            'timestamp': time.time()
        })
        
    except Exception as e:
        logger.error(f"Error in widget sync: {e}")
        return jsonify({'error': str(e)}), 500

@integration_bp.route('/memory/cross-session', methods=['POST'])
def cross_session_memory():
    """Access memory across different sessions"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        action = data.get('action', 'search')  # search, store, retrieve
        query = data.get('query', '')
        session_ids = data.get('session_ids', [])
        
        if not services or 'core' not in services:
            return jsonify({'error': 'Core service not available'}), 500
        
        core_service = services['core']
        
        if action == 'search':
            # Search memories across sessions
            if not query:
                return jsonify({'error': 'Query required for search'}), 400
            
            memories = asyncio.run(core_service.search_memories(query, limit=20))
            
            return jsonify({
                'success': True,
                'action': 'search',
                'query': query,
                'memories': memories,
                'count': len(memories)
            })
        
        elif action == 'retrieve':
            # Retrieve session histories
            if not session_ids:
                session_ids = core_service.get_active_sessions()
            
            session_data = {}
            for session_id in session_ids:
                try:
                    history = core_service.get_session_history(session_id)
                    stats = core_service.get_session_stats(session_id)
                    session_data[session_id] = {
                        'history': history,
                        'stats': stats
                    }
                except Exception as e:
                    session_data[session_id] = {'error': str(e)}
            
            return jsonify({
                'success': True,
                'action': 'retrieve',
                'session_data': session_data,
                'sessions_count': len(session_data)
            })
        
        else:
            return jsonify({'error': f'Unsupported action: {action}'}), 400
        
    except Exception as e:
        logger.error(f"Error in cross-session memory: {e}")
        return jsonify({'error': str(e)}), 500

@integration_bp.route('/services/communicate', methods=['POST'])
def inter_service_communication():
    """Enable communication between services"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        source_service = data.get('source')
        target_service = data.get('target')
        method = data.get('method')
        parameters = data.get('parameters', {})
        
        if not all([source_service, target_service, method]):
            return jsonify({'error': 'Source service, target service, and method are required'}), 400
        
        if not services or target_service not in services:
            return jsonify({'error': f'Target service {target_service} not available'}), 500
        
        target = services[target_service]
        
        # Check if method exists
        if not hasattr(target, method):
            return jsonify({'error': f'Method {method} not available on service {target_service}'}), 400
        
        # Call the method
        try:
            method_func = getattr(target, method)
            
            if parameters:
                result = method_func(**parameters)
            else:
                result = method_func()
            
            logger.info(f"Inter-service communication: {source_service} -> {target_service}.{method}")
            
            return jsonify({
                'success': True,
                'source': source_service,
                'target': target_service,
                'method': method,
                'result': result,
                'timestamp': time.time()
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'Method execution failed: {str(e)}'
            }), 500
        
    except Exception as e:
        logger.error(f"Error in inter-service communication: {e}")
        return jsonify({'error': str(e)}), 500

@integration_bp.route('/external/webhook', methods=['POST'])
def external_webhook():
    """Handle external webhooks"""
    try:
        data = request.get_json()
        headers = dict(request.headers)
        
        webhook_data = {
            'timestamp': time.time(),
            'source_ip': request.remote_addr,
            'headers': headers,
            'data': data
        }
        
        # Log webhook for debugging
        logger.info(f"External webhook received from {request.remote_addr}")
        
        # Process webhook based on source or type
        webhook_type = data.get('type') if data else 'unknown'
        
        if webhook_type == 'ai_update':
            # Handle AI model updates
            if 'ai' in services:
                try:
                    # Trigger AI service health check or reload
                    health_status = asyncio.run(services['ai'].health_check())
                    webhook_data['ai_health'] = health_status
                except Exception as e:
                    webhook_data['ai_error'] = str(e)
        
        elif webhook_type == 'config_update':
            # Handle configuration updates
            if 'config' in services:
                try:
                    services['config'].reload()
                    webhook_data['config_reloaded'] = True
                except Exception as e:
                    webhook_data['config_error'] = str(e)
        
        # Store webhook for audit
        # In production, you might want to store this in database
        
        return jsonify({
            'success': True,
            'message': 'Webhook processed',
            'type': webhook_type,
            'timestamp': webhook_data['timestamp']
        })
        
    except Exception as e:
        logger.error(f"Error processing external webhook: {e}")
        return jsonify({'error': str(e)}), 500

@integration_bp.route('/status', methods=['GET'])
def integration_status():
    """Get integration service status"""
    try:
        status = {
            'timestamp': time.time(),
            'integrations': {
                'nova_ai_chat': 'core' in services if services else False,
                'multi_provider_ai': 'ai' in services if services else False,
                'widget_sync': True,  # Always available
                'cross_session_memory': 'core' in services if services else False,
                'inter_service_communication': len(services) > 0 if services else False,
                'external_webhooks': True  # Always available
            },
            'services_available': list(services.keys()) if services else []
        }
        
        return jsonify(status)
        
    except Exception as e:
        logger.error(f"Error getting integration status: {e}")
        return jsonify({'error': str(e)}), 500

@integration_bp.route('/health', methods=['GET'])
def integration_health_check():
    """Health check for integration services"""
    try:
        health_status = {
            'status': 'healthy',
            'timestamp': time.time(),
            'integrations': {}
        }
        
        # Check Nova AI integration
        if services and 'core' in services:
            try:
                core_stats = services['core'].get_stats()
                health_status['integrations']['nova_ai'] = {
                    'status': 'healthy',
                    'nova_ai_initialized': core_stats.get('nova_ai_initialized', False)
                }
            except Exception as e:
                health_status['integrations']['nova_ai'] = {
                    'status': 'unhealthy',
                    'error': str(e)
                }
        else:
            health_status['integrations']['nova_ai'] = {
                'status': 'unavailable'
            }
        
        # Check AI service integration
        if services and 'ai' in services:
            try:
                ai_stats = services['ai'].get_stats()
                health_status['integrations']['ai_service'] = {
                    'status': 'healthy',
                    'providers_available': len(ai_stats.get('provider_status', {}))
                }
            except Exception as e:
                health_status['integrations']['ai_service'] = {
                    'status': 'unhealthy',
                    'error': str(e)
                }
        else:
            health_status['integrations']['ai_service'] = {
                'status': 'unavailable'
            }
        
        # Determine overall status
        integration_statuses = [i.get('status') for i in health_status['integrations'].values()]
        if 'unhealthy' in integration_statuses:
            health_status['status'] = 'degraded'
        elif 'unavailable' in integration_statuses:
            health_status['status'] = 'limited'
        
        return jsonify(health_status)
        
    except Exception as e:
        logger.error(f"Error in integration health check: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': time.time()
        }), 500
