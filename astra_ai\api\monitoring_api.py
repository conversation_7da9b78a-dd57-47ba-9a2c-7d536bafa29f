"""
Monitoring API for Enhanced Nova AI Server
Handles system monitoring, health checks, and performance metrics
"""

import logging
import time
import psutil
from flask import Blueprint, request, jsonify
from typing import Dict, Any

# Create blueprint
monitoring_bp = Blueprint('monitoring', __name__)

# Services will be injected by the main server
services = None

logger = logging.getLogger(__name__)

@monitoring_bp.route('/health', methods=['GET'])
def system_health_check():
    """Comprehensive system health check"""
    try:
        health_status = {
            'status': 'healthy',
            'timestamp': time.time(),
            'services': {},
            'system': {}
        }
        
        # Check all services
        if services:
            for service_name, service in services.items():
                try:
                    if hasattr(service, 'health_check'):
                        service_health = service.health_check()
                        health_status['services'][service_name] = service_health
                    else:
                        health_status['services'][service_name] = {
                            'status': 'unknown',
                            'message': 'No health check method available'
                        }
                except Exception as e:
                    health_status['services'][service_name] = {
                        'status': 'unhealthy',
                        'error': str(e)
                    }
        
        # System health checks
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            health_status['system']['cpu_percent'] = cpu_percent
            
            # Memory usage
            memory = psutil.virtual_memory()
            health_status['system']['memory'] = {
                'total': memory.total,
                'available': memory.available,
                'percent': memory.percent,
                'used': memory.used
            }
            
            # Disk usage
            disk = psutil.disk_usage('/')
            health_status['system']['disk'] = {
                'total': disk.total,
                'used': disk.used,
                'free': disk.free,
                'percent': (disk.used / disk.total) * 100
            }
            
            # Network stats
            network = psutil.net_io_counters()
            health_status['system']['network'] = {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }
            
        except Exception as e:
            health_status['system']['error'] = str(e)
        
        # Determine overall status
        service_statuses = [s.get('status') for s in health_status['services'].values()]
        if 'unhealthy' in service_statuses:
            health_status['status'] = 'degraded'
        
        # Check system thresholds
        if health_status['system'].get('cpu_percent', 0) > 90:
            health_status['status'] = 'degraded'
            health_status['warnings'] = health_status.get('warnings', [])
            health_status['warnings'].append('High CPU usage')
        
        if health_status['system'].get('memory', {}).get('percent', 0) > 90:
            health_status['status'] = 'degraded'
            health_status['warnings'] = health_status.get('warnings', [])
            health_status['warnings'].append('High memory usage')
        
        return jsonify(health_status)
        
    except Exception as e:
        logger.error(f"Error in system health check: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': time.time()
        }), 500

@monitoring_bp.route('/metrics', methods=['GET'])
def get_system_metrics():
    """Get detailed system metrics"""
    try:
        metrics = {
            'timestamp': time.time(),
            'system': {},
            'services': {}
        }
        
        # System metrics
        try:
            # CPU metrics
            metrics['system']['cpu'] = {
                'percent': psutil.cpu_percent(interval=1),
                'count': psutil.cpu_count(),
                'count_logical': psutil.cpu_count(logical=True),
                'freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
            }
            
            # Memory metrics
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            metrics['system']['memory'] = {
                'virtual': memory._asdict(),
                'swap': swap._asdict()
            }
            
            # Disk metrics
            disk_usage = psutil.disk_usage('/')
            disk_io = psutil.disk_io_counters()
            metrics['system']['disk'] = {
                'usage': disk_usage._asdict(),
                'io': disk_io._asdict() if disk_io else None
            }
            
            # Network metrics
            network_io = psutil.net_io_counters()
            metrics['system']['network'] = {
                'io': network_io._asdict() if network_io else None,
                'connections': len(psutil.net_connections())
            }
            
            # Process metrics
            process = psutil.Process()
            metrics['system']['process'] = {
                'pid': process.pid,
                'cpu_percent': process.cpu_percent(),
                'memory_info': process.memory_info()._asdict(),
                'num_threads': process.num_threads(),
                'create_time': process.create_time()
            }
            
        except Exception as e:
            metrics['system']['error'] = str(e)
        
        # Service metrics
        if services:
            for service_name, service in services.items():
                try:
                    if hasattr(service, 'get_stats'):
                        service_stats = service.get_stats()
                        metrics['services'][service_name] = service_stats
                    else:
                        metrics['services'][service_name] = {
                            'message': 'No stats method available'
                        }
                except Exception as e:
                    metrics['services'][service_name] = {
                        'error': str(e)
                    }
        
        return jsonify(metrics)
        
    except Exception as e:
        logger.error(f"Error getting system metrics: {e}")
        return jsonify({'error': str(e)}), 500

@monitoring_bp.route('/stats', methods=['GET'])
def get_service_stats():
    """Get statistics for all services"""
    try:
        stats = {
            'timestamp': time.time(),
            'services': {}
        }
        
        if services:
            for service_name, service in services.items():
                try:
                    if hasattr(service, 'get_stats'):
                        service_stats = service.get_stats()
                        stats['services'][service_name] = service_stats
                    else:
                        stats['services'][service_name] = {
                            'available': True,
                            'stats_method': False
                        }
                except Exception as e:
                    stats['services'][service_name] = {
                        'available': False,
                        'error': str(e)
                    }
        
        return jsonify(stats)
        
    except Exception as e:
        logger.error(f"Error getting service stats: {e}")
        return jsonify({'error': str(e)}), 500

@monitoring_bp.route('/performance', methods=['GET'])
def get_performance_metrics():
    """Get performance metrics over time"""
    try:
        # Get time range from query parameters
        hours = request.args.get('hours', 24, type=int)
        
        performance_data = {
            'timestamp': time.time(),
            'time_range_hours': hours,
            'metrics': {}
        }
        
        # Get database metrics if available
        if services and 'database' in services:
            try:
                db_metrics = services['database'].get_performance_metrics(hours=hours)
                performance_data['metrics']['database'] = db_metrics
            except Exception as e:
                performance_data['metrics']['database'] = {'error': str(e)}
        
        # Get AI service metrics
        if services and 'ai' in services:
            try:
                ai_stats = services['ai'].get_stats()
                performance_data['metrics']['ai'] = ai_stats
            except Exception as e:
                performance_data['metrics']['ai'] = {'error': str(e)}
        
        # Get cache metrics
        if services and 'cache' in services:
            try:
                cache_stats = services['cache'].get_stats()
                performance_data['metrics']['cache'] = cache_stats
            except Exception as e:
                performance_data['metrics']['cache'] = {'error': str(e)}
        
        return jsonify(performance_data)
        
    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        return jsonify({'error': str(e)}), 500

@monitoring_bp.route('/logs', methods=['GET'])
def get_recent_logs():
    """Get recent log entries"""
    try:
        # Get parameters
        lines = request.args.get('lines', 100, type=int)
        level = request.args.get('level', 'INFO')
        
        # This is a basic implementation
        # In production, you might want to use a proper log aggregation system
        
        log_data = {
            'timestamp': time.time(),
            'lines_requested': lines,
            'level_filter': level,
            'logs': []
        }
        
        # Try to read from log file
        try:
            log_file = services['config'].get('logging.file', 'logs/nova_ai.log') if services else 'logs/nova_ai.log'
            
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    all_lines = f.readlines()
                    recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                    
                    for line in recent_lines:
                        if level.upper() in line:
                            log_data['logs'].append(line.strip())
            else:
                log_data['message'] = 'Log file not found'
                
        except Exception as e:
            log_data['error'] = f'Error reading log file: {str(e)}'
        
        return jsonify(log_data)
        
    except Exception as e:
        logger.error(f"Error getting recent logs: {e}")
        return jsonify({'error': str(e)}), 500

@monitoring_bp.route('/alerts', methods=['GET'])
def get_system_alerts():
    """Get system alerts and warnings"""
    try:
        alerts = {
            'timestamp': time.time(),
            'alerts': [],
            'warnings': []
        }
        
        # Check system resources
        try:
            # CPU alert
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 90:
                alerts['alerts'].append({
                    'type': 'cpu',
                    'level': 'critical',
                    'message': f'CPU usage is {cpu_percent:.1f}%',
                    'threshold': 90
                })
            elif cpu_percent > 75:
                alerts['warnings'].append({
                    'type': 'cpu',
                    'level': 'warning',
                    'message': f'CPU usage is {cpu_percent:.1f}%',
                    'threshold': 75
                })
            
            # Memory alert
            memory = psutil.virtual_memory()
            if memory.percent > 90:
                alerts['alerts'].append({
                    'type': 'memory',
                    'level': 'critical',
                    'message': f'Memory usage is {memory.percent:.1f}%',
                    'threshold': 90
                })
            elif memory.percent > 75:
                alerts['warnings'].append({
                    'type': 'memory',
                    'level': 'warning',
                    'message': f'Memory usage is {memory.percent:.1f}%',
                    'threshold': 75
                })
            
            # Disk alert
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            if disk_percent > 90:
                alerts['alerts'].append({
                    'type': 'disk',
                    'level': 'critical',
                    'message': f'Disk usage is {disk_percent:.1f}%',
                    'threshold': 90
                })
            elif disk_percent > 80:
                alerts['warnings'].append({
                    'type': 'disk',
                    'level': 'warning',
                    'message': f'Disk usage is {disk_percent:.1f}%',
                    'threshold': 80
                })
            
        except Exception as e:
            alerts['system_check_error'] = str(e)
        
        # Check service health
        if services:
            for service_name, service in services.items():
                try:
                    if hasattr(service, 'health_check'):
                        health = service.health_check()
                        if health.get('status') == 'unhealthy':
                            alerts['alerts'].append({
                                'type': 'service',
                                'level': 'critical',
                                'message': f'Service {service_name} is unhealthy',
                                'service': service_name,
                                'details': health
                            })
                        elif health.get('status') == 'degraded':
                            alerts['warnings'].append({
                                'type': 'service',
                                'level': 'warning',
                                'message': f'Service {service_name} is degraded',
                                'service': service_name,
                                'details': health
                            })
                except Exception as e:
                    alerts['warnings'].append({
                        'type': 'service_check',
                        'level': 'warning',
                        'message': f'Could not check health of service {service_name}',
                        'error': str(e)
                    })
        
        return jsonify(alerts)
        
    except Exception as e:
        logger.error(f"Error getting system alerts: {e}")
        return jsonify({'error': str(e)}), 500

@monitoring_bp.route('/uptime', methods=['GET'])
def get_uptime():
    """Get system and service uptime information"""
    try:
        import datetime
        
        uptime_data = {
            'timestamp': time.time(),
            'system': {},
            'services': {}
        }
        
        # System uptime
        try:
            boot_time = psutil.boot_time()
            uptime_seconds = time.time() - boot_time
            uptime_data['system'] = {
                'boot_time': boot_time,
                'uptime_seconds': uptime_seconds,
                'uptime_formatted': str(datetime.timedelta(seconds=int(uptime_seconds)))
            }
        except Exception as e:
            uptime_data['system']['error'] = str(e)
        
        # Service uptime (if services track start time)
        if services:
            for service_name, service in services.items():
                try:
                    if hasattr(service, 'start_time'):
                        start_time = service.start_time
                        service_uptime = time.time() - start_time
                        uptime_data['services'][service_name] = {
                            'start_time': start_time,
                            'uptime_seconds': service_uptime,
                            'uptime_formatted': str(datetime.timedelta(seconds=int(service_uptime)))
                        }
                    else:
                        uptime_data['services'][service_name] = {
                            'message': 'Start time not tracked'
                        }
                except Exception as e:
                    uptime_data['services'][service_name] = {
                        'error': str(e)
                    }
        
        return jsonify(uptime_data)
        
    except Exception as e:
        logger.error(f"Error getting uptime: {e}")
        return jsonify({'error': str(e)}), 500
