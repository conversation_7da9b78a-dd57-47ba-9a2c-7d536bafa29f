"""
Widget API for Enhanced Nova AI Server
Handles integration with Astra AI widgets (Search, News, Notepad, Object ID, Camera)
"""

import logging
import time
import json
import os
from flask import Blueprint, request, jsonify
from typing import Dict, Any, List, Optional
import asyncio

# Create blueprint
widget_bp = Blueprint('widgets', __name__)

# Services will be injected by the main server
services = None

logger = logging.getLogger(__name__)

# Widget data storage paths
WIDGET_DATA_DIR = "data/widgets"
NOTEPAD_FILE = os.path.join(WIDGET_DATA_DIR, "notepad_notes.json")
AI_SUMMARIES_FILE = os.path.join(WIDGET_DATA_DIR, "ai_summaries.json")
SEARCH_HISTORY_FILE = os.path.join(WIDGET_DATA_DIR, "search_history.json")
NEWS_CACHE_FILE = os.path.join(WIDGET_DATA_DIR, "news_cache.json")

def ensure_widget_data_dir():
    """Ensure widget data directory exists"""
    os.makedirs(WIDGET_DATA_DIR, exist_ok=True)

@widget_bp.route('/notepad/save', methods=['POST'])
def save_notepad_notes():
    """Save notepad notes to file"""
    try:
        ensure_widget_data_dir()
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Save the data to file
        with open(NOTEPAD_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        logger.info(f"Saved {data.get('totalNotes', 0)} notepad notes")

        # Save to database if available
        if 'database' in services:
            try:
                # Store in database for backup and sync
                pass  # Implement database storage if needed
            except Exception as e:
                logger.warning(f"Failed to save notes to database: {e}")

        return jsonify({
            'success': True,
            'message': 'Notes saved successfully',
            'file_path': NOTEPAD_FILE,
            'notes_count': data.get('totalNotes', 0)
        })

    except Exception as e:
        logger.error(f"Error saving notepad notes: {e}")
        return jsonify({'error': str(e)}), 500

@widget_bp.route('/notepad/load', methods=['GET'])
def load_notepad_notes():
    """Load notepad notes from file"""
    try:
        ensure_widget_data_dir()

        # Check if file exists
        if not os.path.exists(NOTEPAD_FILE):
            logger.info("Notepad file not found, starting fresh")
            return jsonify({
                'success': True,
                'notes': [],
                'message': 'No notes file found, starting fresh'
            })

        # Load the data from file
        with open(NOTEPAD_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)

        notes = data.get('notes', [])
        logger.info(f"Loaded {len(notes)} notepad notes")

        return jsonify({
            'success': True,
            'notes': notes,
            'file_path': NOTEPAD_FILE,
            'notes_count': len(notes)
        })

    except Exception as e:
        logger.error(f"Error loading notepad notes: {e}")
        return jsonify({'error': str(e)}), 500

@widget_bp.route('/ai-summaries/save', methods=['POST'])
def save_ai_summaries():
    """Save AI summaries to file"""
    try:
        ensure_widget_data_dir()
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Save the data to file
        with open(AI_SUMMARIES_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        logger.info(f"Saved {data.get('totalSummaries', 0)} AI summaries")

        return jsonify({
            'success': True,
            'message': 'AI summaries saved successfully',
            'file_path': AI_SUMMARIES_FILE,
            'summaries_count': data.get('totalSummaries', 0)
        })

    except Exception as e:
        logger.error(f"Error saving AI summaries: {e}")
        return jsonify({'error': str(e)}), 500

@widget_bp.route('/ai-summaries/load', methods=['GET'])
def load_ai_summaries():
    """Load AI summaries from file"""
    try:
        ensure_widget_data_dir()

        # Check if file exists
        if not os.path.exists(AI_SUMMARIES_FILE):
            logger.info("AI summaries file not found, starting fresh")
            return jsonify({
                'success': True,
                'summaries': [],
                'message': 'No AI summaries file found, starting fresh'
            })

        # Load the data from file
        with open(AI_SUMMARIES_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)

        summaries = data.get('summaries', [])
        logger.info(f"Loaded {len(summaries)} AI summaries")

        return jsonify({
            'success': True,
            'summaries': summaries,
            'file_path': AI_SUMMARIES_FILE,
            'summaries_count': len(summaries)
        })

    except Exception as e:
        logger.error(f"Error loading AI summaries: {e}")
        return jsonify({'error': str(e)}), 500

@widget_bp.route('/search/history', methods=['GET', 'POST'])
def manage_search_history():
    """Get or save search history"""
    try:
        ensure_widget_data_dir()
        
        if request.method == 'GET':
            # Load search history
            if not os.path.exists(SEARCH_HISTORY_FILE):
                return jsonify({
                    'success': True,
                    'history': [],
                    'message': 'No search history found'
                })
            
            with open(SEARCH_HISTORY_FILE, 'r', encoding='utf-8') as f:
                history = json.load(f)
            
            return jsonify({
                'success': True,
                'history': history.get('searches', []),
                'count': len(history.get('searches', []))
            })
        
        elif request.method == 'POST':
            # Save search history
            data = request.get_json()
            if not data:
                return jsonify({'error': 'No data provided'}), 400
            
            with open(SEARCH_HISTORY_FILE, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved search history with {len(data.get('searches', []))} entries")
            
            return jsonify({
                'success': True,
                'message': 'Search history saved successfully',
                'count': len(data.get('searches', []))
            })

    except Exception as e:
        logger.error(f"Error managing search history: {e}")
        return jsonify({'error': str(e)}), 500

@widget_bp.route('/news/cache', methods=['GET', 'POST'])
def manage_news_cache():
    """Get or save news cache"""
    try:
        ensure_widget_data_dir()
        
        if request.method == 'GET':
            # Load news cache
            if not os.path.exists(NEWS_CACHE_FILE):
                return jsonify({
                    'success': True,
                    'articles': [],
                    'message': 'No news cache found'
                })
            
            with open(NEWS_CACHE_FILE, 'r', encoding='utf-8') as f:
                cache = json.load(f)
            
            # Check if cache is still valid
            cache_time = cache.get('timestamp', 0)
            cache_duration = services['config'].get('widgets.news.cache_duration', 1800) if services else 1800
            
            if time.time() - cache_time > cache_duration:
                return jsonify({
                    'success': True,
                    'articles': [],
                    'message': 'News cache expired'
                })
            
            return jsonify({
                'success': True,
                'articles': cache.get('articles', []),
                'timestamp': cache.get('timestamp'),
                'count': len(cache.get('articles', []))
            })
        
        elif request.method == 'POST':
            # Save news cache
            data = request.get_json()
            if not data:
                return jsonify({'error': 'No data provided'}), 400
            
            # Add timestamp
            data['timestamp'] = time.time()
            
            with open(NEWS_CACHE_FILE, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved news cache with {len(data.get('articles', []))} articles")
            
            return jsonify({
                'success': True,
                'message': 'News cache saved successfully',
                'count': len(data.get('articles', []))
            })

    except Exception as e:
        logger.error(f"Error managing news cache: {e}")
        return jsonify({'error': str(e)}), 500

@widget_bp.route('/object-identification/analyze', methods=['POST'])
def analyze_object():
    """Analyze object from image data"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        image_data = data.get('image_data')
        if not image_data:
            return jsonify({'error': 'No image data provided'}), 400
        
        # Use AI service for object identification
        if not services or 'ai' not in services:
            return jsonify({'error': 'AI service not available'}), 500
        
        # Create a message for object identification
        messages = [
            {
                "role": "system",
                "content": "You are an expert at identifying objects in images. Analyze the provided image and identify all objects you can see. Provide detailed descriptions and confidence levels."
            },
            {
                "role": "user", 
                "content": f"Please analyze this image and identify the objects: {image_data[:100]}..."
            }
        ]
        
        # Get AI response
        ai_service = services['ai']
        response = asyncio.run(ai_service.get_response(messages=messages, max_tokens=500))
        
        logger.info("Object identification completed")
        
        return jsonify({
            'success': True,
            'analysis': response.content,
            'provider': response.provider,
            'response_time': response.response_time
        })

    except Exception as e:
        logger.error(f"Error in object identification: {e}")
        return jsonify({'error': str(e)}), 500

@widget_bp.route('/camera/capture', methods=['POST'])
def capture_camera():
    """Handle camera capture requests"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        action = data.get('action', 'capture')
        
        if action == 'capture':
            # Handle camera capture
            image_data = data.get('image_data')
            if not image_data:
                return jsonify({'error': 'No image data provided'}), 400
            
            # Process the captured image
            # This could involve saving, analyzing, or forwarding to AI
            
            logger.info("Camera capture processed")
            
            return jsonify({
                'success': True,
                'message': 'Camera capture processed successfully',
                'timestamp': time.time()
            })
        
        elif action == 'analyze':
            # Analyze captured image with AI
            image_data = data.get('image_data')
            if not image_data:
                return jsonify({'error': 'No image data provided'}), 400

            analysis_type = data.get('analysis_type', 'scene')
            question = data.get('question', 'What do you see in this camera image?')

            # Import the Gemini vision service
            try:
                from astra_ai.services.gemini_vision import analyze_camera_capture

                # Analyze the image using Gemini Vision
                result = analyze_camera_capture(
                    image_data=image_data,
                    analysis_type=analysis_type,
                    question=question
                )

                if result['success']:
                    logger.info("Camera image analyzed successfully")
                    return jsonify({
                        'success': True,
                        'analysis': result['analysis'],
                        'analysis_type': analysis_type,
                        'timestamp': time.time()
                    })
                else:
                    logger.error(f"Camera analysis failed: {result.get('error', 'Unknown error')}")
                    return jsonify({
                        'success': False,
                        'error': result.get('error', 'Analysis failed'),
                        'timestamp': time.time()
                    }), 500

            except ImportError as e:
                logger.error(f"Gemini vision service not available: {e}")
                return jsonify({
                    'success': False,
                    'error': 'AI vision service not available',
                    'timestamp': time.time()
                }), 500
            except Exception as e:
                logger.error(f"Error during camera analysis: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e),
                    'timestamp': time.time()
                }), 500
        
        else:
            return jsonify({'error': f'Unknown action: {action}'}), 400

    except Exception as e:
        logger.error(f"Error in camera capture: {e}")
        return jsonify({'error': str(e)}), 500

@widget_bp.route('/camera/test', methods=['GET'])
def test_camera_ai():
    """Test camera AI availability"""
    try:
        # Test if Gemini vision service is available
        try:
            from astra_ai.services.gemini_vision import GEMINI_AVAILABLE, get_gemini_analyzer

            if GEMINI_AVAILABLE:
                # Try to get the analyzer to test connection
                analyzer = get_gemini_analyzer()

                # Test connection
                connection_test = analyzer.test_connection()

                return jsonify({
                    'success': True,
                    'gemini_available': True,
                    'connection_test': connection_test,
                    'message': 'Camera AI is ready',
                    'timestamp': time.time()
                })
            else:
                return jsonify({
                    'success': True,
                    'gemini_available': False,
                    'message': 'Gemini AI not available - package not installed',
                    'timestamp': time.time()
                })

        except Exception as e:
            logger.error(f"Error testing Gemini connection: {e}")
            return jsonify({
                'success': True,
                'gemini_available': False,
                'error': str(e),
                'message': 'Gemini AI connection failed',
                'timestamp': time.time()
            })

    except Exception as e:
        logger.error(f"Error in camera AI test: {e}")
        return jsonify({'error': str(e)}), 500

@widget_bp.route('/summarize', methods=['POST'])
def summarize_content():
    """Summarize content using AI"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        content = data.get('content', '')
        content_type = data.get('type', 'text')
        
        if not content:
            return jsonify({'error': 'No content provided'}), 400
        
        if not services or 'ai' not in services:
            return jsonify({'error': 'AI service not available'}), 500
        
        # Create summarization prompt
        messages = [
            {
                "role": "system",
                "content": f"You are an expert at summarizing {content_type} content. Provide a clear, concise summary that captures the key points and main ideas."
            },
            {
                "role": "user",
                "content": f"Please summarize the following {content_type}:\n\n{content}"
            }
        ]
        
        # Get AI response
        ai_service = services['ai']
        response = asyncio.run(ai_service.get_response(messages=messages, max_tokens=500))
        
        logger.info(f"Content summarization completed for {content_type}")
        
        return jsonify({
            'success': True,
            'summary': response.content,
            'original_length': len(content),
            'summary_length': len(response.content),
            'provider': response.provider,
            'response_time': response.response_time
        })

    except Exception as e:
        logger.error(f"Error in content summarization: {e}")
        return jsonify({'error': str(e)}), 500

@widget_bp.route('/config', methods=['GET'])
def get_widget_config():
    """Get widget configuration"""
    try:
        if not services or 'config' not in services:
            return jsonify({'error': 'Configuration service not available'}), 500
        
        config = services['config']
        widget_config = config.get('widgets', {})
        
        return jsonify({
            'success': True,
            'config': widget_config
        })

    except Exception as e:
        logger.error(f"Error getting widget config: {e}")
        return jsonify({'error': str(e)}), 500

@widget_bp.route('/stats', methods=['GET'])
def get_widget_stats():
    """Get widget usage statistics"""
    try:
        ensure_widget_data_dir()
        
        stats = {
            'notepad': {
                'notes_count': 0,
                'file_exists': os.path.exists(NOTEPAD_FILE)
            },
            'ai_summaries': {
                'summaries_count': 0,
                'file_exists': os.path.exists(AI_SUMMARIES_FILE)
            },
            'search_history': {
                'searches_count': 0,
                'file_exists': os.path.exists(SEARCH_HISTORY_FILE)
            },
            'news_cache': {
                'articles_count': 0,
                'file_exists': os.path.exists(NEWS_CACHE_FILE),
                'cache_valid': False
            }
        }
        
        # Count notepad notes
        if os.path.exists(NOTEPAD_FILE):
            try:
                with open(NOTEPAD_FILE, 'r', encoding='utf-8') as f:
                    notepad_data = json.load(f)
                stats['notepad']['notes_count'] = len(notepad_data.get('notes', []))
            except Exception:
                pass
        
        # Count AI summaries
        if os.path.exists(AI_SUMMARIES_FILE):
            try:
                with open(AI_SUMMARIES_FILE, 'r', encoding='utf-8') as f:
                    summaries_data = json.load(f)
                stats['ai_summaries']['summaries_count'] = len(summaries_data.get('summaries', []))
            except Exception:
                pass
        
        # Count search history
        if os.path.exists(SEARCH_HISTORY_FILE):
            try:
                with open(SEARCH_HISTORY_FILE, 'r', encoding='utf-8') as f:
                    search_data = json.load(f)
                stats['search_history']['searches_count'] = len(search_data.get('searches', []))
            except Exception:
                pass
        
        # Check news cache
        if os.path.exists(NEWS_CACHE_FILE):
            try:
                with open(NEWS_CACHE_FILE, 'r', encoding='utf-8') as f:
                    news_data = json.load(f)
                stats['news_cache']['articles_count'] = len(news_data.get('articles', []))
                
                # Check if cache is valid
                cache_time = news_data.get('timestamp', 0)
                cache_duration = services['config'].get('widgets.news.cache_duration', 1800) if services else 1800
                stats['news_cache']['cache_valid'] = time.time() - cache_time <= cache_duration
            except Exception:
                pass
        
        return jsonify({
            'success': True,
            'stats': stats
        })

    except Exception as e:
        logger.error(f"Error getting widget stats: {e}")
        return jsonify({'error': str(e)}), 500

@widget_bp.route('/health', methods=['GET'])
def widget_health_check():
    """Health check for widget services"""
    try:
        health_status = {
            'status': 'healthy',
            'timestamp': time.time(),
            'widgets': {}
        }
        
        # Check data directory
        ensure_widget_data_dir()
        health_status['widgets']['data_directory'] = {
            'status': 'healthy',
            'path': WIDGET_DATA_DIR,
            'writable': os.access(WIDGET_DATA_DIR, os.W_OK)
        }
        
        # Check AI service availability
        if services and 'ai' in services:
            health_status['widgets']['ai_service'] = {
                'status': 'healthy',
                'available': True
            }
        else:
            health_status['widgets']['ai_service'] = {
                'status': 'degraded',
                'available': False
            }
        
        # Check configuration service
        if services and 'config' in services:
            health_status['widgets']['config_service'] = {
                'status': 'healthy',
                'available': True
            }
        else:
            health_status['widgets']['config_service'] = {
                'status': 'degraded',
                'available': False
            }
        
        # Determine overall status
        widget_statuses = [w.get('status') for w in health_status['widgets'].values()]
        if 'unhealthy' in widget_statuses:
            health_status['status'] = 'unhealthy'
        elif 'degraded' in widget_statuses:
            health_status['status'] = 'degraded'
        
        return jsonify(health_status)

    except Exception as e:
        logger.error(f"Error in widget health check: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': time.time()
        }), 500
