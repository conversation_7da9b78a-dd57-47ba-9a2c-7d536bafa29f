# Enhanced Nova AI Server Configuration Template
# Copy this file to config.yaml and customize for your environment

# Server Configuration
server:
  host: "127.0.0.1"
  api_port: 8081
  ui_port: 8080
  debug: false
  workers: 4
  max_connections: 1000
  timeout: 30

# Database Configuration
database:
  type: "sqlite"  # Options: sqlite, postgresql, mysql
  path: "data/nova_ai.db"  # For SQLite
  # For PostgreSQL/MySQL:
  # host: "localhost"
  # port: 5432  # 5432 for PostgreSQL, 3306 for MySQL
  # name: "nova_ai"
  # username: "nova_user"
  # password: "your_password"
  pool_size: 10
  max_overflow: 20

# Cache Configuration
cache:
  type: "memory"  # Options: memory, redis
  ttl: 3600  # Default TTL in seconds
  max_size: 1000  # For memory cache
  # For Redis:
  # host: "localhost"
  # port: 6379
  # db: 0
  # password: ""

# Security Configuration
security:
  secret_key: ""  # Will be auto-generated if empty
  jwt_expiry: 3600
  bcrypt_rounds: 12
  rate_limit_enabled: true
  cors_origins: ["http://localhost:*", "http://127.0.0.1:*"]
  ssl_enabled: false
  # ssl_cert: "path/to/cert.pem"
  # ssl_key: "path/to/key.pem"

# AI Provider Configuration
ai:
  providers:
    openai:
      enabled: false
      api_key: ""  # Set via environment variable NOVA_AI_OPENAI_API_KEY
      model: "gpt-4"
      max_tokens: 4000
      
    anthropic:
      enabled: false
      api_key: ""  # Set via environment variable NOVA_AI_ANTHROPIC_API_KEY
      model: "claude-3-sonnet-20240229"
      max_tokens: 4000
      
    google:
      enabled: true
      api_key: ""  # Set via environment variable NOVA_AI_GOOGLE_API_KEY
      model: "gemini-pro"
      max_tokens: 4000
      
    groq:
      enabled: true
      api_key: ""  # Set via environment variable NOVA_AI_GROQ_API_KEY
      model: "mixtral-8x7b-32768"
      max_tokens: 4000
  
  # Load Balancing Configuration
  load_balancing:
    enabled: true
    strategy: "round_robin"  # Options: round_robin, random, weighted, fastest
    health_check_interval: 60
  
  # AI Response Caching
  caching:
    enabled: true
    ttl: 3600
    max_size: 1000

# Logging Configuration
logging:
  level: "INFO"  # Options: DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  file: "logs/nova_ai.log"
  error_file: "logs/nova_ai_errors.log"
  max_size: "10MB"
  backup_count: 5
  json_format: false
  console: true
  colored_console: true
  
  # Specific logger configurations
  loggers:
    "astra_ai.services.ai_service":
      level: "DEBUG"
    "astra_ai.api":
      level: "INFO"

# UI Configuration
ui:
  enable_webview: true
  window:
    width: 1400
    height: 900
    resizable: true
    min_width: 1200
    min_height: 800
  theme: "dark"
  auto_refresh: true

# Feature Flags
features:
  file_upload: true
  websockets: true
  background_tasks: true
  monitoring: true
  analytics: true
  health_checks: true
  performance_metrics: true

# Rate Limiting Configuration
rate_limits:
  default: ["200 per day", "50 per hour"]
  chat: ["100 per hour", "10 per minute"]
  ai_generate: ["50 per hour", "5 per minute"]
  file_upload: ["10 per hour", "2 per minute"]
  admin: ["50 per day", "10 per hour"]

# CORS Configuration
cors:
  origins: ["http://localhost:*", "http://127.0.0.1:*"]
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  allow_headers: ["Content-Type", "Authorization", "X-Requested-With"]
  expose_headers: ["X-Total-Count", "X-Rate-Limit-Remaining"]

# Flask Configuration
flask:
  TESTING: false
  JSON_SORT_KEYS: false
  MAX_CONTENT_LENGTH: 16777216  # 16MB
  UPLOAD_FOLDER: "uploads"
  SECRET_KEY: ""  # Will use security.secret_key

# WebSocket Configuration
websocket:
  enabled: true
  cors_allowed_origins: ["http://localhost:*", "http://127.0.0.1:*"]
  ping_timeout: 60
  ping_interval: 25

# Background Tasks Configuration
background_tasks:
  enabled: true
  broker_url: "redis://localhost:6379/1"  # For Celery
  result_backend: "redis://localhost:6379/1"
  task_routes:
    "astra_ai.tasks.ai_tasks": {"queue": "ai_queue"}
    "astra_ai.tasks.file_tasks": {"queue": "file_queue"}

# Monitoring Configuration
monitoring:
  enabled: true
  metrics_port: 9090
  health_check_interval: 30
  performance_collection_interval: 60
  retention_days: 30

# File Upload Configuration
file_upload:
  max_file_size: 16777216  # 16MB
  allowed_extensions: [".txt", ".pdf", ".doc", ".docx", ".jpg", ".jpeg", ".png", ".gif"]
  upload_folder: "uploads"
  virus_scan: false  # Enable if you have antivirus integration

# Widget Integration Configuration
widgets:
  search:
    enabled: true
    cache_results: true
    max_results: 50
  
  news:
    enabled: true
    cache_duration: 1800  # 30 minutes
    max_articles: 20
  
  notepad:
    enabled: true
    auto_save: true
    max_notes: 1000
  
  object_identification:
    enabled: true
    confidence_threshold: 0.7
    max_objects: 10
  
  camera:
    enabled: true
    default_resolution: [640, 480]
    max_fps: 30

# Integration Configuration
integration:
  nova_ai_core: true
  memory_system: true
  conversation_storage: true
  location_services: true
  time_services: true

# Performance Configuration
performance:
  enable_profiling: false
  memory_monitoring: true
  cpu_monitoring: true
  disk_monitoring: true
  network_monitoring: false

# Backup Configuration
backup:
  enabled: false
  interval: "daily"  # Options: hourly, daily, weekly
  retention_days: 30
  backup_folder: "backups"
  compress: true

# Development Configuration
development:
  auto_reload: false
  debug_toolbar: false
  mock_ai_responses: false
  test_mode: false

# Production Configuration
production:
  use_gunicorn: true
  workers: 4
  worker_class: "eventlet"
  max_requests: 1000
  max_requests_jitter: 100
  preload_app: true
  
# Environment Variables (these override config file values)
# NOVA_AI_DEBUG=true
# NOVA_AI_HOST=0.0.0.0
# NOVA_AI_API_PORT=8081
# NOVA_AI_UI_PORT=8080
# NOVA_AI_DATABASE_URL=postgresql://user:pass@localhost/nova_ai
# NOVA_AI_REDIS_URL=redis://localhost:6379/0
# NOVA_AI_SECRET_KEY=your-secret-key
# NOVA_AI_OPENAI_API_KEY=your-openai-key
# NOVA_AI_ANTHROPIC_API_KEY=your-anthropic-key
# NOVA_AI_GOOGLE_API_KEY=your-google-key
# NOVA_AI_GROQ_API_KEY=your-groq-key
