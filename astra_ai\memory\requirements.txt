# Nova Memory AI Requirements

# Core dependencies (minimal for basic functionality)
# No external dependencies required - uses only Python standard library

# LangChain and RAG Integration
langchain>=0.1.0
langchain-community>=0.0.10
langchain-groq>=0.0.1
chromadb>=0.4.0
sentence-transformers>=2.2.0

# Vector embeddings and similarity search
numpy>=1.21.0
scikit-learn>=1.1.0
faiss-cpu>=1.7.0

# For advanced text processing and NLP
spacy>=3.4.0
nltk>=3.7

# For database backends
sqlalchemy>=1.4.0

# For async processing
aiofiles>=0.8.0

# For web interface
fastapi>=0.85.0
uvicorn>=0.18.0

# For testing and development
pytest>=7.0.0
pytest-asyncio>=0.20.0
coverage>=6.0.0

# For logging and monitoring
structlog>=22.0.0

# Additional utilities
python-dotenv>=1.0.0
pydantic>=2.0.0

# Note: Install with: pip install -r requirements.txt
