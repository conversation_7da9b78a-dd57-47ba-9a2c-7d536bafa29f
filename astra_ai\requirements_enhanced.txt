# Enhanced Nova AI Server Requirements
# Production-ready AI service hub with comprehensive features

# Core Framework
Flask==3.0.0
Flask-CORS==4.0.0
Flask-Limiter==3.5.0
Werkzeug==3.0.1

# Web Interface
pywebview==4.4.1

# AI Providers
openai>=1.3.0
anthropic>=0.7.0
google-generativeai>=0.3.0
groq>=0.4.0

# Database Support
SQLAlchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9  # PostgreSQL
PyMySQL==1.1.0          # MySQL

# Caching
redis==5.0.1
hiredis==2.2.3

# Background Tasks
celery==5.3.4
kombu==5.3.4

# WebSocket Support
Flask-SocketIO==5.3.6
python-socketio==5.10.0
eventlet==0.33.3

# Security
cryptography==41.0.8
PyJWT==2.8.0
bcrypt==4.1.2
passlib==1.7.4

# Configuration Management
PyYAML==6.0.1
python-dotenv==1.0.0
configparser==6.0.0

# HTTP Requests
requests==2.31.0
httpx==0.25.2
aiohttp==3.9.1

# Data Processing
pandas==2.1.4
numpy==1.25.2
Pillow==10.1.0

# File Handling
python-multipart==0.0.6
aiofiles==23.2.1

# Monitoring & Metrics
prometheus-client==0.19.0
psutil==5.9.6

# Logging
structlog==23.2.0
colorlog==6.8.0

# Validation
pydantic==2.5.2
marshmallow==3.20.1

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0

# Development Tools
black==23.11.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# Async Support
asyncio==3.4.3
aioredis==2.0.1

# Time & Date
python-dateutil==2.8.2
pytz==2023.3

# Environment & System
python-decouple==3.8
click==8.1.7
rich==13.7.0

# Memory Management
memory-profiler==0.61.0
pympler==0.9

# Network & Communication
websockets==12.0
pyngrok==7.0.0  # For tunneling if needed

# Image Processing (for camera features)
opencv-python==********
imageio==2.33.1

# Audio Processing (if needed)
sounddevice==0.4.6
librosa==0.10.1

# Machine Learning (optional)
scikit-learn==1.3.2
transformers==4.36.2
torch==2.1.2

# API Documentation
flask-restx==1.3.0
flasgger==*******

# Rate Limiting & Throttling
slowapi==0.1.9
limits==3.7.0

# Serialization
msgpack==1.0.7
orjson==3.9.10

# Utilities
tqdm==4.66.1
tabulate==0.9.0
humanize==4.9.0

# Error Tracking
sentry-sdk==1.39.2

# Performance
uvloop==0.19.0  # For better async performance on Unix
gunicorn==21.2.0  # WSGI server for production

# Health Checks
healthcheck==1.3.3

# Process Management
supervisor==4.2.5

# Backup & Recovery
schedule==1.2.0

# Optional: Advanced Features
# langchain==0.0.350  # For advanced AI workflows
# faiss-cpu==1.7.4    # For vector similarity search
# chromadb==0.4.18    # Vector database
# pinecone-client==2.2.4  # Vector database service
