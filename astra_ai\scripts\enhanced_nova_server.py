#!/usr/bin/env python3
"""
Enhanced Nova AI Server - Production-Ready AI Service Hub
A comprehensive, modular AI server with advanced features and capabilities.
"""

import os
import sys
import asyncio
import logging
import signal
import threading
import time
from pathlib import Path
from typing import Dict, Any, Optional
import json
import uuid
from datetime import datetime

# Third-party imports
import webview as pywebview
from flask import Flask
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from werkzeug.middleware.proxy_fix import ProxyFix
import redis
from celery import Celery

# Local imports - Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

# Import service modules
from services.core_service import CoreService
from services.ai_service import AIService
from services.auth_service import AuthService
from services.file_service import FileService
from services.websocket_service import WebSocketService
from services.monitoring_service import MonitoringService
from services.config_service import ConfigService
from services.database_service import DatabaseService
from services.cache_service import CacheService

# Import API blueprints
from api.chat_api import chat_bp
from api.ai_api import ai_bp
from api.file_api import file_bp
from api.admin_api import admin_bp
from api.monitoring_api import monitoring_bp
from api.widget_api import widget_bp
from api.integration_api import integration_bp

# Import utilities
from utils.logger import setup_logging
from utils.security import SecurityManager
from utils.performance import PerformanceMonitor
from utils.health_check import HealthChecker

class EnhancedNovaServer:
    """
    Enhanced Nova AI Server - Production-ready AI service hub
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the enhanced server"""
        self.config = ConfigService(config_path)
        self.logger = setup_logging(self.config.get('logging', {}))
        
        # Core components
        self.app = None
        self.webview_window = None
        self.services = {}
        self.background_tasks = {}
        self.shutdown_event = threading.Event()
        
        # Performance monitoring
        self.performance_monitor = PerformanceMonitor()
        self.health_checker = HealthChecker()
        
        # Initialize services
        self._initialize_services()
        
        # Setup Flask app
        self._setup_flask_app()
        
        # Setup signal handlers
        self._setup_signal_handlers()
        
        self.logger.info("Enhanced Nova Server initialized successfully")
    
    def _initialize_services(self):
        """Initialize all service components"""
        try:
            # Core services
            self.services['config'] = self.config
            self.services['database'] = DatabaseService(self.config)
            self.services['cache'] = CacheService(self.config)
            self.services['auth'] = AuthService(self.config)
            
            # AI and processing services
            self.services['ai'] = AIService(self.config)
            self.services['core'] = CoreService(self.config)
            self.services['file'] = FileService(self.config)
            
            # Communication services
            self.services['websocket'] = WebSocketService(self.config)
            self.services['monitoring'] = MonitoringService(self.config)
            
            # Security and performance
            self.services['security'] = SecurityManager(self.config)
            
            self.logger.info("All services initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize services: {e}")
            raise
    
    def _setup_flask_app(self):
        """Setup Flask application with all configurations"""
        self.app = Flask(__name__)
        
        # Basic configuration
        self.app.config.update(self.config.get('flask', {}))
        self.app.config['SECRET_KEY'] = self.config.get('security.secret_key', os.urandom(32))
        
        # Middleware
        self.app.wsgi_app = ProxyFix(self.app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_prefix=1)
        
        # Extensions
        CORS(self.app, **self.config.get('cors', {}))
        
        # Rate limiting
        limiter = Limiter(
            app=self.app,
            key_func=get_remote_address,
            default_limits=self.config.get('rate_limits.default', ["200 per day", "50 per hour"])
        )
        
        # Register blueprints
        self._register_blueprints()
        
        # Setup error handlers
        self._setup_error_handlers()
        
        # Setup middleware
        self._setup_middleware()
        
        self.logger.info("Flask application configured successfully")
    
    def _register_blueprints(self):
        """Register all API blueprints"""
        blueprints = [
            (chat_bp, '/api/chat'),
            (ai_bp, '/api/ai'),
            (file_bp, '/api/files'),
            (admin_bp, '/api/admin'),
            (monitoring_bp, '/api/monitoring'),
            (widget_bp, '/api/widgets'),
            (integration_bp, '/api/integration')
        ]
        
        for blueprint, url_prefix in blueprints:
            # Inject services into blueprint
            blueprint.services = self.services
            self.app.register_blueprint(blueprint, url_prefix=url_prefix)
        
        self.logger.info(f"Registered {len(blueprints)} API blueprints")
    
    def _setup_error_handlers(self):
        """Setup global error handlers"""
        @self.app.errorhandler(404)
        def not_found(error):
            return {'error': 'Endpoint not found', 'status': 404}, 404
        
        @self.app.errorhandler(500)
        def internal_error(error):
            self.logger.error(f"Internal server error: {error}")
            return {'error': 'Internal server error', 'status': 500}, 500
        
        @self.app.errorhandler(429)
        def rate_limit_exceeded(error):
            return {'error': 'Rate limit exceeded', 'status': 429}, 429
    
    def _setup_middleware(self):
        """Setup custom middleware"""
        @self.app.before_request
        def before_request():
            # Performance monitoring
            self.performance_monitor.start_request()
            
            # Security checks
            if not self.services['security'].validate_request():
                return {'error': 'Security validation failed'}, 403
        
        @self.app.after_request
        def after_request(response):
            # Performance monitoring
            self.performance_monitor.end_request()
            
            # Add security headers
            response = self.services['security'].add_security_headers(response)
            
            return response
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(sig, frame):
            self.logger.info(f"Received signal {sig}. Initiating graceful shutdown...")
            self.shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def start_background_tasks(self):
        """Start background tasks and workers"""
        tasks = {
            'health_monitor': self._health_monitor_task,
            'performance_collector': self._performance_collector_task,
            'cache_cleanup': self._cache_cleanup_task,
            'log_rotation': self._log_rotation_task
        }
        
        for task_name, task_func in tasks.items():
            thread = threading.Thread(target=task_func, daemon=True, name=task_name)
            thread.start()
            self.background_tasks[task_name] = thread
            self.logger.info(f"Started background task: {task_name}")
    
    def _health_monitor_task(self):
        """Background task for health monitoring"""
        while not self.shutdown_event.is_set():
            try:
                health_status = self.health_checker.check_all_services(self.services)
                self.services['monitoring'].record_health_status(health_status)
                time.sleep(30)  # Check every 30 seconds
            except Exception as e:
                self.logger.error(f"Health monitor error: {e}")
                time.sleep(60)  # Wait longer on error
    
    def _performance_collector_task(self):
        """Background task for performance data collection"""
        while not self.shutdown_event.is_set():
            try:
                metrics = self.performance_monitor.collect_metrics()
                self.services['monitoring'].record_performance_metrics(metrics)
                time.sleep(60)  # Collect every minute
            except Exception as e:
                self.logger.error(f"Performance collector error: {e}")
                time.sleep(120)
    
    def _cache_cleanup_task(self):
        """Background task for cache cleanup"""
        while not self.shutdown_event.is_set():
            try:
                self.services['cache'].cleanup_expired()
                time.sleep(3600)  # Cleanup every hour
            except Exception as e:
                self.logger.error(f"Cache cleanup error: {e}")
                time.sleep(1800)  # Wait 30 minutes on error
    
    def _log_rotation_task(self):
        """Background task for log rotation"""
        while not self.shutdown_event.is_set():
            try:
                # Implement log rotation logic
                time.sleep(86400)  # Check daily
            except Exception as e:
                self.logger.error(f"Log rotation error: {e}")
                time.sleep(43200)  # Wait 12 hours on error
    
    def start_webview(self):
        """Start the desktop webview interface"""
        try:
            ui_port = self.config.get('server.ui_port', 8080)
            api_port = self.config.get('server.api_port', 8081)
            
            # Start UI server
            ui_thread = threading.Thread(
                target=self._start_ui_server,
                args=(ui_port,),
                daemon=True
            )
            ui_thread.start()
            
            # Wait for UI server to start
            time.sleep(2)
            
            # Create webview window
            self.webview_window = pywebview.create_window(
                'Enhanced Nova AI - Production Server',
                f'http://127.0.0.1:{ui_port}/splash_screen.html?api_port={api_port}',
                width=self.config.get('ui.window.width', 1400),
                height=self.config.get('ui.window.height', 900),
                min_size=(1200, 800),
                background_color='#000000'
            )
            
            self.logger.info("Webview interface started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start webview: {e}")
            raise
    
    def _start_ui_server(self, port):
        """Start the UI server"""
        from http.server import HTTPServer, SimpleHTTPRequestHandler
        import os
        
        ui_dir = Path(__file__).parent.parent / 'ui'
        os.chdir(ui_dir)
        
        httpd = HTTPServer(('127.0.0.1', port), SimpleHTTPRequestHandler)
        httpd.serve_forever()
    
    def run(self):
        """Run the enhanced server"""
        try:
            self.logger.info("Starting Enhanced Nova AI Server...")
            
            # Start background tasks
            self.start_background_tasks()
            
            # Start webview if configured
            if self.config.get('ui.enable_webview', True):
                self.start_webview()
            
            # Start Flask app
            api_port = self.config.get('server.api_port', 8081)
            host = self.config.get('server.host', '127.0.0.1')
            
            self.logger.info(f"Starting API server on {host}:{api_port}")
            
            if self.config.get('ui.enable_webview', True):
                # Run in separate thread if webview is enabled
                api_thread = threading.Thread(
                    target=lambda: self.app.run(host=host, port=api_port, debug=False),
                    daemon=True
                )
                api_thread.start()
                
                # Start webview (blocking)
                pywebview.start()
            else:
                # Run Flask app directly
                self.app.run(host=host, port=api_port, debug=self.config.get('debug', False))
                
        except Exception as e:
            self.logger.error(f"Server startup failed: {e}")
            raise
    
    def shutdown(self):
        """Graceful shutdown of the server"""
        self.logger.info("Initiating graceful shutdown...")
        
        # Signal shutdown to background tasks
        self.shutdown_event.set()
        
        # Stop services
        for service_name, service in self.services.items():
            try:
                if hasattr(service, 'shutdown'):
                    service.shutdown()
                self.logger.info(f"Shutdown service: {service_name}")
            except Exception as e:
                self.logger.error(f"Error shutting down {service_name}: {e}")
        
        # Wait for background tasks to complete
        for task_name, task_thread in self.background_tasks.items():
            try:
                task_thread.join(timeout=5)
                self.logger.info(f"Stopped background task: {task_name}")
            except Exception as e:
                self.logger.error(f"Error stopping {task_name}: {e}")
        
        self.logger.info("Graceful shutdown completed")

def main():
    """Main entry point"""
    try:
        # Initialize and run server
        server = EnhancedNovaServer()
        server.run()
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Server failed to start: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
