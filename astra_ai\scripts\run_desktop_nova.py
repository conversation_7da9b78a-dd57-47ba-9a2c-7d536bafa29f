#!/usr/bin/env python3
"""
Enhanced Nova AI Desktop Server - Production-Ready AI Service Hub
A comprehensive, modular AI server with advanced features and capabilities.

🔗 UNIFIED AI COMMUNICATION SYSTEM
==================================

This system integrates three key components for a seamless AI experience:

1. **Chat Interface UI** (splash_screen.html)
   - Modern chat interface with section-based messages
   - Sends all user messages to Nova AI backend via /api/chat
   - Displays AI responses in real-time

2. **Nova AI Backend** (nova_ai.py)
   - Processes all user messages with advanced AI capabilities
   - Provides consistent responses across all interfaces
   - Writes responses to unified_messages.json for voice synthesis

3. **Simple Unified Voice System** (simple_unified_voice.py)
   - Monitors unified_messages.json for new AI responses
   - Automatically speaks AI responses using Cartesia TTS
   - Runs as background service integrated with desktop app

🚀 INTEGRATION FEATURES:
- ✅ Bidirectional communication between UI and AI
- ✅ Synchronized responses across terminal and UI
- ✅ Automatic voice synthesis of AI responses
- ✅ Unified session management
- ✅ Real-time status monitoring

🎯 RESULT: One unified AI conversation experience where the terminal AI,
UI chat interface, and voice system all work together consistently.
"""

import webview as pywebview
import os
from http.server import HTTPServer, SimpleHTTPRequestHandler
import threading
import socket
from pathlib import Path
import sys
import asyncio
import json
from flask import Flask, request, jsonify, session, send_file, Blueprint
from flask_cors import CORS
import uuid
import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import importlib
import fnmatch
import logging
import signal
import hashlib
import secrets
import sqlite3
import pickle
import shutil
import mimetypes
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from collections import deque
from contextlib import contextmanager
import re
import html
import urllib.parse
import psutil
from dataclasses import dataclass, asdict

# Try to import YAML
try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    # Create a simple YAML replacement for basic functionality
    class SimpleYAML:
        @staticmethod
        def safe_load(stream):
            return {}

        @staticmethod
        def dump(data, stream, **kwargs):
            import json
            json.dump(data, stream, indent=2)

    yaml = SimpleYAML()

# Try to import optional dependencies
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False

try:
    import google.generativeai as genai
    GOOGLE_AVAILABLE = True
except ImportError:
    GOOGLE_AVAILABLE = False

try:
    from groq import Groq
    GROQ_AVAILABLE = True
except ImportError:
    GROQ_AVAILABLE = False

try:
    import bcrypt
    BCRYPT_AVAILABLE = True
except ImportError:
    BCRYPT_AVAILABLE = False

try:
    import jwt
    JWT_AVAILABLE = True
except ImportError:
    JWT_AVAILABLE = False

# ===================== ENHANCED CONFIGURATION =====================

# Default configuration
DEFAULT_CONFIG = {
    'server': {
        'host': '127.0.0.1',
        'api_port': 8081,
        'ui_port': 8080,
        'debug': False,
        'workers': 4,
        'max_connections': 1000,
        'timeout': 30
    },
    'database': {
        'type': 'sqlite',
        'path': 'data/nova_ai.db',
        'pool_size': 10,
        'max_overflow': 20
    },
    'cache': {
        'type': 'memory',
        'ttl': 3600,
        'max_size': 1000
    },
    'security': {
        'secret_key': '',
        'jwt_expiry': 3600,
        'bcrypt_rounds': 12,
        'rate_limit_enabled': True,
        'cors_origins': ['http://localhost:*', 'http://127.0.0.1:*']
    },
    'ai': {
        'providers': {
            'openai': {'enabled': False, 'api_key': '', 'model': 'gpt-4', 'max_tokens': 4000},
            'anthropic': {'enabled': False, 'api_key': '', 'model': 'claude-3-sonnet-20240229', 'max_tokens': 4000},
            'google': {'enabled': True, 'api_key': '', 'model': 'gemini-pro', 'max_tokens': 4000},
            'groq': {'enabled': True, 'api_key': '', 'model': 'mixtral-8x7b-32768', 'max_tokens': 4000}
        },
        'load_balancing': {'enabled': True, 'strategy': 'round_robin', 'health_check_interval': 60},
        'caching': {'enabled': True, 'ttl': 3600, 'max_size': 1000}
    },
    'logging': {
        'level': 'INFO',
        'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        'file': 'logs/nova_ai.log',
        'max_size': '10MB',
        'backup_count': 5,
        'console': True
    },
    'features': {
        'file_upload': True,
        'websockets': True,
        'background_tasks': True,
        'monitoring': True,
        'analytics': True,
        'health_checks': True
    },
    'widgets': {
        'search': {'enabled': True, 'cache_results': True, 'max_results': 50},
        'news': {'enabled': True, 'cache_duration': 1800, 'max_articles': 20},
        'notepad': {'enabled': True, 'auto_save': True, 'max_notes': 1000},
        'object_identification': {'enabled': True, 'confidence_threshold': 0.7},
        'camera': {'enabled': True, 'default_resolution': [640, 480]}
    }
}

# File watching configuration
WATCHED_EXTENSIONS = {'.py', '.html', '.css', '.js', '.json'}
IGNORE_PATTERNS = {
    '__pycache__', '.git', '.vscode', 'node_modules', '.pytest_cache',
    '*.pyc', '*.pyo', '*.log', '.DS_Store', 'Thumbs.db'
}
IGNORE_FILES = {
    'unified_messages.json', 'processed.json', 'transcription.json', 'transcription.txt',
    'output.json', 'process_info.json', 'saved_summaries.json',
    '*.db', '*.sqlite', '*.sqlite3', '*.bin', '*.dat', '*.tmp', '*.temp', '*.cache',
    '*.bak', '*.backup', '*.old', '*.orig'
}
WATCH_SPECIFIC_FILES = {
    'nova_ai.py', 'splash_screen.html', 'enhanced_nova_ai.py', 'personality.py',
    'response_manager.py', 'context_manager.py', 'emotional_intelligence.py',
    'learning.py', 'ml_personalization.py', 'search_decision.py', 'self_improving_ai.py',
    'sentiment_analyzer.py', 'action_tracker.py', 'config_manager.py',
    'enhanced_search.py', 'persistent_commands.py'
}

# ===================== ENHANCED LOGGING SETUP =====================

class EnhancedLogger:
    """Enhanced logging with structured output and performance tracking"""

    def __init__(self, config):
        self.config = config
        self.setup_logging()

    def setup_logging(self):
        """Setup comprehensive logging"""
        log_config = self.config.get('logging', {})

        # Create logs directory
        log_file = log_config.get('file', 'logs/nova_ai.log')
        log_dir = Path(log_file).parent
        log_dir.mkdir(parents=True, exist_ok=True)

        # Configure root logger
        level = getattr(logging, log_config.get('level', 'INFO').upper())
        format_str = log_config.get('format', '%(asctime)s [%(levelname)s] %(name)s: %(message)s')

        logging.basicConfig(
            level=level,
            format=format_str,
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler() if log_config.get('console', True) else logging.NullHandler()
            ]
        )

        # Suppress noisy loggers
        logging.getLogger('werkzeug').setLevel(logging.ERROR)
        logging.getLogger('urllib3').setLevel(logging.ERROR)
        logging.getLogger('watchdog').setLevel(logging.ERROR)
        logging.getLogger('requests').setLevel(logging.ERROR)

# ===================== ENHANCED SERVICE CLASSES =====================

@dataclass
class AIResponse:
    """AI response data structure"""
    content: str
    provider: str
    model: str
    tokens_used: int
    response_time: float
    cached: bool = False
    metadata: Dict[str, Any] = None

@dataclass
class ProviderStatus:
    """AI provider status tracking"""
    available: bool = True
    last_check: float = 0
    error_count: int = 0
    avg_response_time: float = 0.0
    last_error: str = ""

class ConfigService:
    """Enhanced configuration management"""

    def __init__(self, config_path='config.yaml'):
        self.config_path = config_path
        self.config = DEFAULT_CONFIG.copy()
        self.load_config()
        self.apply_env_overrides()

    def load_config(self):
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    file_config = yaml.safe_load(f) or {}
                self._deep_update(self.config, file_config)
        except Exception as e:
            print(f"Warning: Failed to load config from {self.config_path}: {e}")

    def apply_env_overrides(self):
        """Apply environment variable overrides"""
        env_mappings = {
            'NOVA_AI_DEBUG': 'server.debug',
            'NOVA_AI_HOST': 'server.host',
            'NOVA_AI_API_PORT': 'server.api_port',
            'NOVA_AI_UI_PORT': 'server.ui_port',
            'NOVA_AI_SECRET_KEY': 'security.secret_key',
            'NOVA_AI_OPENAI_API_KEY': 'ai.providers.openai.api_key',
            'NOVA_AI_ANTHROPIC_API_KEY': 'ai.providers.anthropic.api_key',
            'NOVA_AI_GOOGLE_API_KEY': 'ai.providers.google.api_key',
            'NOVA_AI_GROQ_API_KEY': 'ai.providers.groq.api_key'
        }

        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value:
                self.set(config_path, value)

    def get(self, path, default=None):
        """Get configuration value by dot notation path"""
        keys = path.split('.')
        value = self.config
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        return value

    def set(self, path, value):
        """Set configuration value by dot notation path"""
        keys = path.split('.')
        config = self.config
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]

        # Type conversion for specific values
        if keys[-1] in ['port', 'api_port', 'ui_port', 'max_tokens', 'ttl', 'max_size']:
            try:
                value = int(value)
            except (ValueError, TypeError):
                pass
        elif keys[-1] in ['debug', 'enabled']:
            if isinstance(value, str):
                value = value.lower() in ('true', '1', 'yes', 'on')

        config[keys[-1]] = value

    def _deep_update(self, base_dict, update_dict):
        """Deep update dictionary"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value

    def save(self, path=None):
        """Save configuration to file"""
        save_path = path or self.config_path
        try:
            with open(save_path, 'w') as f:
                yaml.dump(self.config, f, default_flow_style=False, indent=2)
        except Exception as e:
            print(f"Error saving config: {e}")

    def get_masked(self):
        """Get configuration with sensitive values masked"""
        masked = json.loads(json.dumps(self.config))  # Deep copy

        # Mask sensitive values
        sensitive_keys = ['api_key', 'secret_key', 'password', 'token']

        def mask_recursive(obj):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if any(sensitive in key.lower() for sensitive in sensitive_keys):
                        if value:
                            obj[key] = f"{str(value)[:4]}{'*' * (len(str(value)) - 4)}"
                    else:
                        mask_recursive(value)
            elif isinstance(obj, list):
                for item in obj:
                    mask_recursive(item)

        mask_recursive(masked)
        return masked

class CacheService:
    """Enhanced caching service with multiple backends"""

    def __init__(self, config):
        self.config = config
        cache_config = config.get('cache', {})
        self.cache_type = cache_config.get('type', 'memory')
        self.default_ttl = cache_config.get('ttl', 3600)
        self.max_size = cache_config.get('max_size', 1000)

        # Initialize cache backend
        if self.cache_type == 'redis' and REDIS_AVAILABLE:
            try:
                self.redis_client = redis.Redis(
                    host=cache_config.get('host', 'localhost'),
                    port=cache_config.get('port', 6379),
                    db=cache_config.get('db', 0),
                    decode_responses=False
                )
                self.redis_client.ping()
                self.cache = self._redis_cache
                print("✅ Redis cache initialized")
            except Exception as e:
                print(f"⚠️ Redis cache failed, falling back to memory: {e}")
                self.cache = self._memory_cache
        else:
            self.cache = self._memory_cache

        # Memory cache storage
        self.memory_store = {}
        self.access_times = {}

        # Statistics
        self.stats = {'hits': 0, 'misses': 0, 'sets': 0}

    def get(self, key):
        """Get value from cache"""
        try:
            value = self.cache('get', key)
            if value is not None:
                self.stats['hits'] += 1
                return value
            else:
                self.stats['misses'] += 1
                return None
        except Exception as e:
            print(f"Cache get error: {e}")
            self.stats['misses'] += 1
            return None

    def set(self, key, value, ttl=None):
        """Set value in cache"""
        try:
            ttl = ttl or self.default_ttl
            self.cache('set', key, value, ttl)
            self.stats['sets'] += 1
            return True
        except Exception as e:
            print(f"Cache set error: {e}")
            return False

    def _memory_cache(self, operation, key, value=None, ttl=None):
        """Memory cache implementation"""
        current_time = time.time()

        if operation == 'get':
            if key in self.memory_store:
                stored_value, expiry = self.memory_store[key]
                if expiry and current_time > expiry:
                    del self.memory_store[key]
                    if key in self.access_times:
                        del self.access_times[key]
                    return None
                self.access_times[key] = current_time
                return stored_value
            return None

        elif operation == 'set':
            # Evict if cache is full
            if len(self.memory_store) >= self.max_size and key not in self.memory_store:
                self._evict_lru()

            expiry = current_time + ttl if ttl else None
            self.memory_store[key] = (value, expiry)
            self.access_times[key] = current_time

    def _redis_cache(self, operation, key, value=None, ttl=None):
        """Redis cache implementation"""
        if operation == 'get':
            data = self.redis_client.get(key)
            return pickle.loads(data) if data else None

        elif operation == 'set':
            data = pickle.dumps(value)
            if ttl:
                self.redis_client.setex(key, ttl, data)
            else:
                self.redis_client.set(key, data)

    def _evict_lru(self):
        """Evict least recently used item"""
        if self.access_times:
            lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
            del self.memory_store[lru_key]
            del self.access_times[lru_key]

    def clear(self):
        """Clear all cache entries"""
        if self.cache_type == 'redis' and hasattr(self, 'redis_client'):
            self.redis_client.flushdb()
        else:
            self.memory_store.clear()
            self.access_times.clear()

    def get_stats(self):
        """Get cache statistics"""
        stats = self.stats.copy()
        stats['type'] = self.cache_type
        stats['size'] = len(self.memory_store) if self.cache_type == 'memory' else 'unknown'
        total_requests = stats['hits'] + stats['misses']
        stats['hit_rate'] = stats['hits'] / max(total_requests, 1)
        return stats

class AIService:
    """Enhanced AI service with multiple providers and load balancing"""

    def __init__(self, config, cache_service=None):
        self.config = config
        self.cache_service = cache_service
        self.providers = {}
        self.provider_status = {}
        self.current_provider_index = 0

        # Initialize providers
        self._initialize_providers()

        # Load balancing
        self.load_balancer = LoadBalancer(config.get('ai.load_balancing', {}))

        print(f"✅ AI Service initialized with {len(self.providers)} providers")

    def _initialize_providers(self):
        """Initialize AI providers"""
        providers_config = self.config.get('ai.providers', {})

        for provider_name, provider_config in providers_config.items():
            if not provider_config.get('enabled', False):
                continue

            api_key = provider_config.get('api_key') or os.getenv(f'NOVA_AI_{provider_name.upper()}_API_KEY')
            if not api_key:
                print(f"⚠️ No API key for {provider_name}, skipping")
                continue

            try:
                if provider_name == 'openai' and OPENAI_AVAILABLE:
                    self.providers[provider_name] = openai.OpenAI(api_key=api_key)
                elif provider_name == 'anthropic' and ANTHROPIC_AVAILABLE:
                    self.providers[provider_name] = anthropic.Anthropic(api_key=api_key)
                elif provider_name == 'google' and GOOGLE_AVAILABLE:
                    genai.configure(api_key=api_key)
                    self.providers[provider_name] = genai.GenerativeModel(provider_config.get('model', 'gemini-pro'))
                elif provider_name == 'groq' and GROQ_AVAILABLE:
                    self.providers[provider_name] = Groq(api_key=api_key)

                if provider_name in self.providers:
                    self.provider_status[provider_name] = ProviderStatus()

            except Exception as e:
                pass  # Silently handle provider initialization failures

    async def get_response(self, messages, provider=None, **kwargs):
        """Get AI response with load balancing and caching"""
        # Generate cache key
        cache_key = self._generate_cache_key(messages, kwargs)

        # Check cache first
        if self.cache_service and self.config.get('ai.caching.enabled', True):
            cached_response = self.cache_service.get(cache_key)
            if cached_response:
                cached_response.cached = True
                return cached_response

        # Select provider
        if not provider:
            provider = self.load_balancer.select_provider(list(self.providers.keys()))

        if provider not in self.providers:
            raise Exception(f"Provider {provider} not available")

        # Get response
        start_time = time.time()
        try:
            response = await self._call_provider(provider, messages, **kwargs)
            response_time = time.time() - start_time

            # Update provider status
            status = self.provider_status[provider]
            status.last_check = time.time()
            status.avg_response_time = (status.avg_response_time + response_time) / 2

            # Cache response
            if self.cache_service and self.config.get('ai.caching.enabled', True):
                self.cache_service.set(cache_key, response, self.config.get('ai.caching.ttl', 3600))

            return response

        except Exception as e:
            # Update error status
            if provider in self.provider_status:
                self.provider_status[provider].error_count += 1
                self.provider_status[provider].last_error = str(e)
            raise

    async def _call_provider(self, provider, messages, **kwargs):
        """Call specific AI provider"""
        provider_config = self.config.get(f'ai.providers.{provider}', {})
        model = kwargs.get('model') or provider_config.get('model')
        max_tokens = kwargs.get('max_tokens') or provider_config.get('max_tokens', 4000)

        start_time = time.time()

        if provider == 'openai':
            response = self.providers[provider].chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=max_tokens
            )
            content = response.choices[0].message.content
            tokens_used = response.usage.total_tokens

        elif provider == 'anthropic':
            response = self.providers[provider].messages.create(
                model=model,
                messages=messages,
                max_tokens=max_tokens
            )
            content = response.content[0].text
            tokens_used = response.usage.input_tokens + response.usage.output_tokens

        elif provider == 'google':
            # Convert messages to Google format
            prompt = self._convert_messages_to_prompt(messages)
            response = self.providers[provider].generate_content(prompt)
            content = response.text
            tokens_used = response.usage_metadata.total_token_count if hasattr(response, 'usage_metadata') else 0

        elif provider == 'groq':
            response = self.providers[provider].chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=max_tokens
            )
            content = response.choices[0].message.content
            tokens_used = response.usage.total_tokens

        else:
            raise Exception(f"Unknown provider: {provider}")

        response_time = time.time() - start_time

        return AIResponse(
            content=content,
            provider=provider,
            model=model,
            tokens_used=tokens_used,
            response_time=response_time,
            cached=False
        )

    def _convert_messages_to_prompt(self, messages):
        """Convert messages to prompt format for Google"""
        prompt_parts = []
        for msg in messages:
            role = msg['role']
            content = msg['content']
            if role == 'system':
                prompt_parts.append(f"System: {content}")
            elif role == 'user':
                prompt_parts.append(f"User: {content}")
            elif role == 'assistant':
                prompt_parts.append(f"Assistant: {content}")
        return "\n\n".join(prompt_parts)

    def _generate_cache_key(self, messages, kwargs):
        """Generate cache key for request"""
        key_data = {
            'messages': messages,
            'kwargs': sorted(kwargs.items())
        }
        key_string = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_string.encode()).hexdigest()

    def get_stats(self):
        """Get AI service statistics"""
        return {
            'providers': list(self.providers.keys()),
            'provider_status': {name: asdict(status) for name, status in self.provider_status.items()},
            'cache_stats': self.cache_service.get_stats() if self.cache_service else None
        }

class LoadBalancer:
    """Load balancer for AI providers"""

    def __init__(self, config):
        self.strategy = config.get('strategy', 'round_robin')
        self.current_index = 0
        self.provider_weights = config.get('provider_weights', {})
        self.provider_stats = {}

    def select_provider(self, providers):
        """Select provider based on strategy"""
        if not providers:
            raise Exception("No providers available")

        if self.strategy == 'round_robin':
            provider = providers[self.current_index % len(providers)]
            self.current_index += 1
            return provider

        elif self.strategy == 'random':
            import random
            return random.choice(providers)

        elif self.strategy == 'weighted':
            # Implement weighted selection
            weights = [self.provider_weights.get(p, 1.0) for p in providers]
            import random
            return random.choices(providers, weights=weights)[0]

        else:
            return providers[0]  # Default to first provider

logger = logging.getLogger("NovaAI")

# Add the parent directory to the path to import Nova AI
sys.path.append(str(Path(__file__).parent.parent))
from core.nova_ai import AleChatBot

# Old voice system import removed - now using Simple Unified Voice System only

# Old AI Voice System removed - now using Simple Unified Voice System only

# Old Cartesia direct voice synthesis removed - now using Simple Unified Voice System only

# Import Simple Unified Voice System
try:
    sys.path.append(str(Path(__file__).parent.parent / 'voice'))
    from simple_unified_voice import (
        initialize_simple_voice_system,
        add_chat_message,
        add_camera_message,
        stop_voice_system,
        get_voice_status
    )
    SIMPLE_VOICE_AVAILABLE = True
    print("✅ Simple Unified Voice System available")
except ImportError as e:
    SIMPLE_VOICE_AVAILABLE = False
    print(f"⚠️ Simple Unified Voice System not available: {e}")

# ===================== OLD VOICE SYSTEM REMOVED =====================
# DirectVoiceSynthesis class removed - now using Simple Unified Voice System only




# ===================== ENHANCED GLOBAL VARIABLES =====================

# Store chat histories in memory (per session)
chat_histories = {}

# Store user locations per session
user_locations = {}

# Enhanced services
config_service = None
cache_service = None
ai_service = None
enhanced_logger = None
nova_ai = None
voice_system = None
direct_voice = None  # Direct voice synthesis

# Global deduplication cache to prevent duplicate messages
recent_messages_cache = {}
CACHE_TIMEOUT = 30  # seconds

def is_duplicate_message(message_type: str, ai_response: str, session_id: str) -> bool:
    """Check if this message is a recent duplicate"""
    import time
    current_time = time.time()

    # Clean up old cache entries
    expired_keys = [k for k, v in recent_messages_cache.items() if current_time - v['timestamp'] > CACHE_TIMEOUT]
    for key in expired_keys:
        del recent_messages_cache[key]

    # Create cache key
    cache_key = f"{message_type}_{hash(ai_response.strip())}"

    # Check if this message was recently processed
    if cache_key in recent_messages_cache:
        print(f"🔄 Duplicate message detected in cache: {ai_response[:30]}...")
        return True

    # Add to cache
    recent_messages_cache[cache_key] = {
        'timestamp': current_time,
        'ai_response': ai_response,
        'session_id': session_id
    }

    return False

# Performance tracking
request_stats = {
    'total_requests': 0,
    'successful_requests': 0,
    'failed_requests': 0,
    'avg_response_time': 0.0,
    'total_response_time': 0.0
}

# Session management
user_sessions = {}

# Widget data storage
WIDGET_DATA_DIR = "data/widgets"
NOTEPAD_FILE = os.path.join(WIDGET_DATA_DIR, "notepad_notes.json")
AI_SUMMARIES_FILE = os.path.join(WIDGET_DATA_DIR, "ai_summaries.json")
SEARCH_HISTORY_FILE = os.path.join(WIDGET_DATA_DIR, "search_history.json")
NEWS_CACHE_FILE = os.path.join(WIDGET_DATA_DIR, "news_cache.json")

def ensure_widget_data_dir():
    """Ensure widget data directory exists"""
    os.makedirs(WIDGET_DATA_DIR, exist_ok=True)

# Global variables for auto-refresh
webview_window = None
file_watcher = None
last_refresh_time = 0
refresh_cooldown = 1  # Reduced cooldown for faster response
is_refreshing = False  # Prevent multiple simultaneous refreshes

class CodeChangeHandler(FileSystemEventHandler):
    """Handle file system events for code changes"""

    def __init__(self, webview_window):
        super().__init__()
        self.webview_window = webview_window
        self.watched_extensions = WATCHED_EXTENSIONS
        self.ignore_patterns = IGNORE_PATTERNS
        self.ignore_files = IGNORE_FILES
        self.watch_specific_files = WATCH_SPECIFIC_FILES
        self.last_modified_files = {}

    def should_ignore(self, file_path):
        """
        Check if file should be ignored based on patterns, extensions, and specific files.
        Uses glob-style matching for patterns and filenames.
        """
        path_str = str(file_path)
        file_name = Path(file_path).name

        # Check ignore patterns (glob)
        for pattern in self.ignore_patterns:
            if fnmatch.fnmatch(path_str, pattern) or fnmatch.fnmatch(file_name, pattern):
                logger.debug(f"Ignoring by pattern: {pattern} -> {file_path}")
                return True

        # Check ignore files (glob)
        for ignore_file in self.ignore_files:
            if fnmatch.fnmatch(file_name, ignore_file):
                logger.debug(f"Ignoring by file: {ignore_file} -> {file_path}")
                return True

        # Check file extension
        if Path(file_path).suffix not in self.watched_extensions:
            logger.debug(f"Ignoring by extension: {file_path}")
            return True

        # Only watch specific important files
        if file_name not in self.watch_specific_files:
            logger.debug(f"Ignoring by not in watch list: {file_path}")
            return True

        return False

    def on_modified(self, event):
        """Handle file modification events"""
        if event.is_directory:
            return
            
        if self.should_ignore(event.src_path):
            return
            
        # Prevent duplicate events for the same file
        current_time = time.time()
        if event.src_path in self.last_modified_files:
            if current_time - self.last_modified_files[event.src_path] < 0.5:
                return  # Skip if modified too recently
        
        self.last_modified_files[event.src_path] = current_time
        self.handle_file_change(event.src_path, "modified")
    
    def on_created(self, event):
        """Handle file creation events"""
        if event.is_directory:
            return
            
        if self.should_ignore(event.src_path):
            return
            
        self.handle_file_change(event.src_path, "created")
    
    def handle_file_change(self, file_path, change_type):
        """Handle file changes with cooldown"""
        global last_refresh_time, is_refreshing
        
        current_time = time.time()
        if current_time - last_refresh_time < refresh_cooldown:
            print(f"⏳ Skipping refresh (cooldown active): {Path(file_path).name}")
            return
        
        if is_refreshing:
            print(f"⏳ Skipping refresh (already refreshing): {Path(file_path).name}")
            return
            
        last_refresh_time = current_time
        is_refreshing = True
        
        file_name = Path(file_path).name
        file_ext = Path(file_path).suffix
        print(f"\n🔄 Code file {change_type}: {file_name} ({file_ext})")
        
        try:
            # If it's a Python file, try to reload Nova AI
            if file_path.endswith('.py') and ('nova_ai' in file_path or 'core' in file_path):
                print("🐍 Nova AI code file detected - reloading module...")
                self.reload_nova_ai()
            
            # If it's a UI file, mention it
            if file_ext in ['.html', '.css', '.js']:
                print("🎨 UI code file detected - refreshing interface...")
            
            # Refresh the webview
            self.refresh_webview()
            
        except Exception as e:
            print(f"⚠️ Error handling file change: {e}")
        finally:
            is_refreshing = False
    
    def reload_nova_ai(self):
        """Attempt to reload Nova AI module"""
        try:
            print("🔄 Reloading Nova AI module...")
            
            # Clear the module from cache
            modules_to_reload = []
            for module_name in list(sys.modules.keys()):
                if 'nova_ai' in module_name or 'core' in module_name:
                    modules_to_reload.append(module_name)
            
            for module_name in modules_to_reload:
                if module_name in sys.modules:
                    del sys.modules[module_name]
            
            # Reimport and reinitialize
            global nova_ai
            from core.nova_ai import AleChatBot
            nova_ai = AleChatBot()
            print("✅ Nova AI reloaded successfully")
            
        except Exception as e:
            print(f"⚠️ Failed to reload Nova AI: {e}")
            print("💡 You may need to restart the application for changes to take effect")
    
    def refresh_webview(self):
        """Refresh the webview window"""
        if self.webview_window:
            try:
                # Use a small delay to ensure file changes are complete
                threading.Timer(0.3, self._do_refresh).start()
            except Exception as e:
                print(f"⚠️ Failed to refresh webview: {e}")
    
    def _do_refresh(self):
        """Actually perform the refresh"""
        try:
            # Get the actual webview window instance
            if len(pywebview.windows) > 0:
                window = pywebview.windows[0]
                
                # Try different methods to refresh the webview
                if hasattr(window, 'reload'):
                    window.reload()
                    print("✅ UI refreshed using reload()")
                elif hasattr(window, 'evaluate_js'):
                    # Use JavaScript to reload the page
                    window.evaluate_js('window.location.reload()')
                    print("✅ UI refreshed using JavaScript")
                elif hasattr(window, 'load_url'):
                    # Get current URL and reload it
                    current_url = window.get_current_url() if hasattr(window, 'get_current_url') else None
                    if current_url:
                        window.load_url(current_url)
                        print("✅ UI refreshed by reloading URL")
                    else:
                        print("⚠️ Cannot refresh: No current URL available")
                else:
                    print("⚠️ Cannot refresh: No suitable refresh method found")
                    print(f"Available methods: {[attr for attr in dir(window) if not attr.startswith('_')]}")
            else:
                print("⚠️ Cannot refresh: No webview windows available")
                
        except Exception as e:
            print(f"⚠️ Error during refresh: {e}")
            # Try alternative approach using pywebview API
            try:
                if len(pywebview.windows) > 0:
                    pywebview.windows[0].evaluate_js('location.reload()')
                    print("✅ UI refreshed (alternative method)")
            except Exception as e2:
                print(f"⚠️ Alternative refresh also failed: {e2}")
                print("💡 Auto-refresh disabled due to errors. Manual refresh required.")

def setup_file_watcher(webview_window):
    """Set up file system watcher"""
    global file_watcher
    
    try:
        # Get paths to watch - only watch specific directories
        current_dir = Path(__file__).parent.parent
        paths_to_watch = [
            current_dir / 'core',  # Nova AI core files
            current_dir / 'ui',    # UI files
        ]
        
        # Create handler
        handler = CodeChangeHandler(webview_window)
        
        # Create observer
        observer = Observer()
        
        # Add watchers for each path
        for path in paths_to_watch:
            if path.exists():
                observer.schedule(handler, str(path), recursive=True)
            else:
                print(f"⚠️ Path not found: {path}")
        
        # Start observer
        observer.start()
        file_watcher = observer

        return observer
        
    except Exception as e:
        print(f"⚠️ Failed to setup file watcher: {e}")
        print(f"Error details: {type(e).__name__}: {str(e)}")
        print("💡 Make sure 'watchdog' is installed: pip install watchdog")
        return None

def get_free_port():
    """Get a free port on the system"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(('', 0))
        s.listen(1)
        port = s.getsockname()[1]
    return port

def run_server(port, directory):
    """Run the HTTP server"""
    os.chdir(directory)
    httpd = HTTPServer(('127.0.0.1', port), SimpleHTTPRequestHandler)
    httpd.serve_forever()

# Initialize Nova AI chatbot
nova_ai = None

def initialize_nova_ai():
    """Initialize Nova AI chatbot"""
    global nova_ai
    try:
        nova_ai = AleChatBot()
        print("Nova AI initialized successfully")
        return True
    except Exception as e:
        print(f"Failed to initialize Nova AI: {e}")
        return False

def initialize_voice_system():
    """Initialize Simple Unified Voice System"""
    global voice_system, direct_voice
    try:
        if SIMPLE_VOICE_AVAILABLE:
            # Initialize Simple Unified Voice System
            print("🎤 Initializing Simple Unified Voice System...")
            ui_dir = str(Path(__file__).parent.parent / 'ui')

            success = initialize_simple_voice_system(ui_dir)
            if success:
                print("✅ Simple Unified Voice System initialized successfully")
                print(f"   📄 Messages: {ui_dir}/unified_messages.json")
                print(f"   📋 Processed: {ui_dir}/processed.json")
                print("   🎤 No duplicate processing")
                return True
            else:
                print("❌ Failed to initialize Simple Unified Voice System")
                return False
        else:
            print("⚠️ Simple Unified Voice System not available")
            return False

    except Exception as e:
        print(f"❌ Failed to initialize voice system: {e}")
        return False

# ===================== ENHANCED FLASK APPLICATION =====================

# Create basic Flask app first
app = Flask(__name__)
CORS(app)

# Register API blueprints
try:
    # Import and register widget API blueprint
    sys.path.append(str(Path(__file__).parent.parent))
    from api.widget_api import widget_bp
    app.register_blueprint(widget_bp, url_prefix='/api/widget')
    print("✅ Widget API blueprint registered")
except ImportError as e:
    print(f"⚠️ Widget API blueprint not available: {e}")
except Exception as e:
    print(f"⚠️ Error registering widget API blueprint: {e}")

def initialize_enhanced_services():
    """Initialize enhanced services when needed"""
    global config_service, cache_service, ai_service, enhanced_logger

    try:
        # Initialize configuration
        config_service = ConfigService()

        # Initialize enhanced logging
        enhanced_logger = EnhancedLogger(config_service.config)

        # Initialize cache service
        cache_service = CacheService(config_service.config)

        # Initialize AI service
        ai_service = AIService(config_service.config, cache_service)

        # Configure Flask with enhanced settings
        cors_origins = config_service.get('security.cors_origins', ['http://localhost:*', 'http://127.0.0.1:*'])
        app.config['SECRET_KEY'] = config_service.get('security.secret_key') or secrets.token_hex(32)
        app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

        return True

    except Exception as e:
        print(f"⚠️ Enhanced services initialization failed: {e}")
        print("   Continuing with basic functionality...")
        return False

@app.route('/api/chat', methods=['POST'])
def chat():
    """Handle chat messages from the frontend"""
    try:
        data = request.get_json()
        user_message = data.get('message', '')
        session_id = data.get('session_id', 'default')
        user_location = data.get('user_location', '')
        
        if not user_message:
            return jsonify({'error': 'No message provided'}), 400
        
        if not nova_ai:
            return jsonify({'error': 'Nova AI not initialized'}), 500
        
        # Get or create session chat history
        if session_id not in chat_histories:
            chat_histories[session_id] = []
        
        # Save user location if provided
        if user_location:
            user_locations[session_id] = user_location
            print(f"[LOCATION] Saved location for session {session_id}: {user_location}")
        
        # Use saved location if no location provided but we have one stored
        if not user_location and session_id in user_locations:
            user_location = user_locations[session_id]
            print(f"[LOCATION] Using saved location for session {session_id}: {user_location}")
        
        # For testing: Set Italy as default location if no location is set
        if not user_location:
            user_location = "Italy"
            user_locations[session_id] = user_location
            print(f"[LOCATION] Setting default location to Italy for session {session_id}")
        
        # Debug: Show current location status
        print(f"[DEBUG] Location status for session {session_id}:")
        print(f"  - Provided location: {user_location}")
        print(f"  - Saved locations: {user_locations}")
        print(f"  - Final location to use: {user_location}")
        
        # Skip heavy memory processing for faster responses
        relevant_memories = ""
        print(f"[SPEED] Skipping memory search for faster response")
        if False:  # Disable memory processing
            try:
                print(f"🔍 Searching memories for: '{user_message}'")
                memories = nova_ai.memory.retrieve_memories(user_message, limit=5)
                print(f"📚 Found {len(memories)} memories")
                
                if memories:
                    memory_texts = []
                    for memory in memories:
                        content = memory.get('content', 'No content')
                        memory_type = memory.get('memory_type', 'unknown').replace('_', ' ').title()
                        relevance = memory.get('relevance_score', 0.0)
                        print(f"  💭 Memory: {content[:50]}... (relevance: {relevance:.2f})")
                        
                        # Only include high-relevance memories
                        if relevance > 0.3:
                            memory_texts.append(f"[{memory_type}] {content}")
                    
                    if memory_texts:
                        relevant_memories = "🧠 Relevant past memories:\n" + "\n".join(f"• {mem}" for mem in memory_texts[:3])
                        print(f"✅ Using {len(memory_texts)} relevant memories in context")
                    else:
                        print("⚠️ No high-relevance memories found")
                else:
                    print("ℹ️ No memories found for this query")
            except Exception as e:
                print(f"Warning: Failed to retrieve memories: {e}")
        else:
            # Fallback: Use local conversation history as memory context
            try:
                print(f"💾 Memory system disabled, using local conversation history")
                # Load recent conversations from local storage
                if hasattr(nova_ai.files, 'ai_output_file') and nova_ai.files.ai_output_file.exists():
                    import json
                    with open(nova_ai.files.ai_output_file, 'r', encoding='utf-8') as f:
                        stored_conversations = json.load(f)
                    
                    # Simple keyword matching for relevant conversations
                    user_keywords = set(user_message.lower().split())
                    relevant_conversations = []
                    
                    for conv in stored_conversations[-20:]:  # Check last 20 conversations
                        if 'user' in conv and 'assistant' in conv:
                            conv_keywords = set(conv['user'].lower().split())
                            # Check for keyword overlap
                            if user_keywords.intersection(conv_keywords):
                                relevant_conversations.append(conv)
                    
                    if relevant_conversations:
                        memory_texts = []
                        for conv in relevant_conversations[-3:]:  # Use last 3 relevant
                            user_msg = conv.get('user', '')
                            ai_response = conv.get('assistant', '')
                            if user_msg and ai_response:
                                memory_texts.append(f"Previous: {user_msg} → {ai_response[:100]}...")
                        
                        if memory_texts:
                            relevant_memories = "🧠 Relevant past conversations:\n" + "\n".join(f"• {mem}" for mem in memory_texts[:2])
                            print(f"✅ Using {len(memory_texts)} past conversations as context")
                        
            except Exception as e:
                print(f"Warning: Failed to retrieve local conversation history: {e}")
        
        # Build messages with memory context (same as terminal mode)
        messages = nova_ai.chat_history + chat_histories[session_id]
        
        # Add system message about time display capabilities
        messages.append({
            "role": "system",
            "content": "You are running in a desktop UI with visual time display capabilities. When users ask for time, you have REAL-TIME access to current time information. Always provide actual current time, never say you don't have real-time access. The UI will automatically show a visual time display widget when you provide time information."
        })
        
        # Add memory context as a system message if we have relevant memories
        if relevant_memories:
            messages.append({
                "role": "system", 
                "content": f"Context from past conversations:\n{relevant_memories}\n\nUse this context naturally in your response if relevant to the current question."
            })
        
        # Add location context if available
        if user_location:
            messages.append({
                "role": "system",
                "content": f"User's current location: {user_location}. You have REAL-TIME access to current time information for any location. When users ask for time, provide the actual current time using your time functions. Use this location for time requests and location-based queries."
            })
        
        # Add the current user message
        messages.append({"role": "user", "content": user_message})
        
        # Get response from Nova AI (run async function in sync context)
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            response = loop.run_until_complete(nova_ai.get_response(messages, stream_to_terminal=False))
        finally:
            loop.close()

        # Update session chat history (without system prompt, just like terminal mode)
        if response:
            chat_histories[session_id].extend([
                {"role": "user", "content": user_message},
                {"role": "assistant", "content": response}
            ])

            # Keep session history at reasonable size (same as terminal mode)
            if len(chat_histories[session_id]) > 20:  # 10 exchanges
                chat_histories[session_id] = chat_histories[session_id][-20:]

            # Store conversation locally only (skip heavy memory processing)
            try:
                nova_ai.files.store_conversation(user_message, response)
                # Skip memory storage for faster responses
                print(f"[SPEED] Conversation stored locally, skipping memory processing")
            except Exception as e:
                print(f"Warning: Failed to store conversation: {e}")

            # Add to Simple Unified Voice System with duplicate prevention
            try:
                if SIMPLE_VOICE_AVAILABLE:
                    # Check for duplicates before adding
                    if not is_duplicate_message('chat', response, session_id):
                        message_id = add_chat_message(user_message, response, session_id)
                        if message_id:
                            print(f"[UNIFIED-VOICE] Chat message added: {message_id}")
                        else:
                            print(f"[UNIFIED-VOICE] Failed to add chat message")
                    else:
                        print(f"[UNIFIED-VOICE] Skipping duplicate chat message")
                else:
                    # Fallback to direct voice synthesis
                    print(f"[VOICE] Simple voice not available, using fallback")
                    if direct_voice:
                        response_id = f"{session_id}_{time.time()}"
                        threading.Thread(
                            target=direct_voice.synthesize_and_play,
                            args=(response, response_id),
                            daemon=True
                        ).start()

            except Exception as e:
                print(f"Warning: Failed to add message to voice system: {e}")
                # Fallback to direct voice synthesis
                try:
                    if direct_voice:
                        response_id = f"{session_id}_{time.time()}"
                        threading.Thread(
                            target=direct_voice.synthesize_and_play,
                            args=(response, response_id),
                            daemon=True
                        ).start()
                except Exception as fallback_error:
                    print(f"Warning: Fallback voice synthesis also failed: {fallback_error}")

        return jsonify({'response': response})
        
    except Exception as e:
        import traceback
        print(f"Error in chat endpoint: {e}")
        print(f"Full traceback: {traceback.format_exc()}")
        return jsonify({'error': str(e)}), 500

# ===================== ENHANCED API ENDPOINTS =====================

# Performance tracking decorator
def track_performance(f):
    """Decorator to track API performance"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        request_stats['total_requests'] += 1

        try:
            result = f(*args, **kwargs)
            request_stats['successful_requests'] += 1
            return result
        except Exception as e:
            request_stats['failed_requests'] += 1
            raise
        finally:
            response_time = time.time() - start_time
            request_stats['total_response_time'] += response_time
            request_stats['avg_response_time'] = request_stats['total_response_time'] / request_stats['total_requests']

    wrapper.__name__ = f.__name__
    return wrapper

@app.route('/api/memory/status', methods=['GET'])
def memory_status():
    """Get memory system status and statistics"""
    try:
        if not nova_ai:
            return jsonify({'error': 'Nova AI not initialized'}), 500

        if not nova_ai.memory_enabled or not nova_ai.memory:
            return jsonify({
                'enabled': False,
                'status': 'Memory system disabled'
            })

        stats = nova_ai.memory.get_memory_stats()
        return jsonify({
            'enabled': True,
            'status': 'Memory system active',
            'stats': stats
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500



@app.route('/api/test/integration', methods=['POST'])
def test_integration():
    """Test the integration between UI, Nova AI, and Voice System"""
    try:
        data = request.get_json()
        test_message = data.get('message', 'Hello, this is a test message.')

        # Test Nova AI response
        if not nova_ai:
            return jsonify({'error': 'Nova AI not initialized'}), 500

        # Create test messages
        messages = [{"role": "user", "content": test_message}]

        # Get response from Nova AI
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            response = loop.run_until_complete(nova_ai.get_response(messages, stream_to_terminal=False))
        finally:
            loop.close()

        # Test voice system integration
        voice_status = "not available"
        if voice_system and hasattr(voice_system, 'is_running'):
            voice_status = "running" if voice_system.is_running else "stopped"

            # Old ai_responses.json test removed - now using Simple Unified Voice System only

        return jsonify({
            'success': True,
            'test_message': test_message,
            'nova_ai_response': response,
            'voice_system_status': voice_status,
            'integration_status': 'All systems operational'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# ===================== AI SERVICE API =====================

@app.route('/api/ai/providers', methods=['GET'])
@track_performance
def get_ai_providers():
    """Get available AI providers and their status"""
    try:
        if not ai_service:
            return jsonify({'error': 'AI service not available'}), 500

        provider_status = {}
        for provider_name, status in ai_service.provider_status.items():
            provider_status[provider_name] = {
                'available': status.available,
                'last_check': status.last_check,
                'error_count': status.error_count,
                'avg_response_time': status.avg_response_time,
                'last_error': status.last_error
            }

        return jsonify({
            'providers': provider_status,
            'available_count': len([p for p in provider_status.values() if p['available']])
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/ai/generate', methods=['POST'])
@track_performance
def ai_generate():
    """Generate AI response using specified provider"""
    try:
        if not ai_service:
            return jsonify({'error': 'AI service not available'}), 500

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        messages = data.get('messages', [])
        provider = data.get('provider')
        model = data.get('model')
        max_tokens = data.get('max_tokens', 4000)

        if not messages:
            return jsonify({'error': 'No messages provided'}), 400

        # Generate response
        response = asyncio.run(ai_service.get_response(
            messages=messages,
            provider=provider,
            model=model,
            max_tokens=max_tokens
        ))

        return jsonify({
            'content': response.content,
            'provider': response.provider,
            'model': response.model,
            'tokens_used': response.tokens_used,
            'response_time': response.response_time,
            'cached': response.cached
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/ai/health', methods=['GET'])
@track_performance
def ai_health_check():
    """Perform health check on AI providers"""
    try:
        if not ai_service:
            return jsonify({'error': 'AI service not available'}), 500

        health_status = {}
        for provider_name in ai_service.providers.keys():
            try:
                # Simple test message
                test_messages = [{"role": "user", "content": "Hello"}]
                start_time = time.time()
                response = asyncio.run(ai_service.get_response(
                    messages=test_messages,
                    provider=provider_name,
                    max_tokens=10
                ))
                test_time = time.time() - start_time

                health_status[provider_name] = {
                    'status': 'healthy',
                    'response_time': test_time,
                    'test_response': response.content[:50] + '...' if len(response.content) > 50 else response.content
                }

            except Exception as e:
                health_status[provider_name] = {
                    'status': 'unhealthy',
                    'error': str(e)
                }

        return jsonify({
            'timestamp': time.time(),
            'providers': health_status,
            'overall_status': 'healthy' if any(p.get('status') == 'healthy' for p in health_status.values()) else 'unhealthy'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/ai/stats', methods=['GET'])
@track_performance
def get_ai_stats():
    """Get AI service statistics"""
    try:
        if not ai_service:
            return jsonify({'error': 'AI service not available'}), 500

        stats = ai_service.get_stats()
        return jsonify(stats)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# ===================== WIDGET API =====================

@app.route('/api/widgets/notepad/save', methods=['POST'])
@track_performance
def enhanced_save_notepad_notes():
    """Save notepad notes to file"""
    try:
        ensure_widget_data_dir()

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Save the data to file
        with open(NOTEPAD_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        print(f"✅ Saved {data.get('totalNotes', 0)} notepad notes")

        return jsonify({
            'success': True,
            'message': 'Notes saved successfully',
            'file_path': NOTEPAD_FILE,
            'notes_count': data.get('totalNotes', 0)
        })

    except Exception as e:
        print(f"❌ Error saving notepad notes: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/widgets/notepad/load', methods=['GET'])
@track_performance
def enhanced_load_notepad_notes():
    """Load notepad notes from file"""
    try:
        ensure_widget_data_dir()

        # Check if file exists
        if not os.path.exists(NOTEPAD_FILE):
            print("ℹ️ Notepad file not found, starting fresh")
            return jsonify({
                'success': True,
                'notes': [],
                'message': 'No notes file found, starting fresh'
            })

        # Load the data from file
        with open(NOTEPAD_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)

        notes = data.get('notes', [])
        print(f"✅ Loaded {len(notes)} notepad notes")

        return jsonify({
            'success': True,
            'notes': notes,
            'file_path': NOTEPAD_FILE,
            'notes_count': len(notes)
        })

    except Exception as e:
        print(f"❌ Error loading notepad notes: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/widgets/ai-summaries/save', methods=['POST'])
@track_performance
def enhanced_save_ai_summaries():
    """Save AI summaries to file"""
    try:
        ensure_widget_data_dir()

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Save the data to file
        with open(AI_SUMMARIES_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        print(f"✅ Saved {data.get('totalSummaries', 0)} AI summaries")

        return jsonify({
            'success': True,
            'message': 'AI summaries saved successfully',
            'file_path': AI_SUMMARIES_FILE,
            'summaries_count': data.get('totalSummaries', 0)
        })

    except Exception as e:
        print(f"❌ Error saving AI summaries: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/widgets/ai-summaries/load', methods=['GET'])
@track_performance
def enhanced_load_ai_summaries():
    """Load AI summaries from file"""
    try:
        ensure_widget_data_dir()

        # Check if file exists
        if not os.path.exists(AI_SUMMARIES_FILE):
            print("ℹ️ AI summaries file not found, starting fresh")
            return jsonify({
                'success': True,
                'summaries': [],
                'message': 'No AI summaries file found, starting fresh'
            })

        # Load the data from file
        with open(AI_SUMMARIES_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)

        summaries = data.get('summaries', [])
        print(f"✅ Loaded {len(summaries)} AI summaries")

        return jsonify({
            'success': True,
            'summaries': summaries,
            'file_path': AI_SUMMARIES_FILE,
            'summaries_count': len(summaries)
        })

    except Exception as e:
        print(f"❌ Error loading AI summaries: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/widgets/summarize', methods=['POST'])
@track_performance
def enhanced_summarize_content():
    """Summarize content using AI"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        content = data.get('content', '')
        content_type = data.get('type', 'text')

        if not content:
            return jsonify({'error': 'No content provided'}), 400

        # Try AI service first
        if ai_service:
            try:
                messages = [
                    {
                        "role": "system",
                        "content": f"You are an expert at summarizing {content_type} content. Provide a clear, concise summary that captures the key points and main ideas."
                    },
                    {
                        "role": "user",
                        "content": f"Please summarize the following {content_type}:\n\n{content}"
                    }
                ]

                response = asyncio.run(ai_service.get_response(messages=messages, max_tokens=500))

                print(f"✅ Content summarization completed using {response.provider}")

                return jsonify({
                    'success': True,
                    'summary': response.content,
                    'original_length': len(content),
                    'summary_length': len(response.content),
                    'provider': response.provider,
                    'response_time': response.response_time,
                    'source': 'ai_service'
                })

            except Exception as e:
                print(f"AI Service summarization failed, falling back to Nova AI: {e}")

        # Fallback to Nova AI
        if nova_ai:
            try:
                messages = [
                    {
                        "role": "system",
                        "content": f"You are an expert at summarizing {content_type} content. Provide a clear, concise summary that captures the key points and main ideas."
                    },
                    {
                        "role": "user",
                        "content": f"Please summarize the following {content_type}:\n\n{content}"
                    }
                ]

                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    response = loop.run_until_complete(nova_ai.get_response(messages, stream_to_terminal=False))
                finally:
                    loop.close()

                print(f"✅ Content summarization completed using Nova AI")

                return jsonify({
                    'success': True,
                    'summary': response,
                    'original_length': len(content),
                    'summary_length': len(response),
                    'source': 'nova_ai'
                })

            except Exception as e:
                print(f"Nova AI summarization failed: {e}")
                return jsonify({'error': f'Summarization failed: {str(e)}'}), 500

        return jsonify({'error': 'No AI service available for summarization'}), 500

    except Exception as e:
        print(f"❌ Error in content summarization: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/memory/search', methods=['POST'])
def memory_search():
    """Search memories for debugging"""
    try:
        data = request.get_json()
        query = data.get('query', '')
        
        if not query:
            return jsonify({'error': 'No query provided'}), 400
            
        if not nova_ai or not nova_ai.memory_enabled or not nova_ai.memory:
            return jsonify({'error': 'Memory system not available'}), 500
        
        memories = nova_ai.memory.retrieve_memories(query, limit=10)
        return jsonify({
            'query': query,
            'memories': memories,
            'count': len(memories)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# ===================== MONITORING API =====================

@app.route('/api/monitoring/health', methods=['GET'])
@track_performance
def system_health_check():
    """Comprehensive system health check"""
    try:
        health_status = {
            'status': 'healthy',
            'timestamp': time.time(),
            'services': {},
            'system': {}
        }

        # Check Nova AI
        if nova_ai:
            health_status['services']['nova_ai'] = {
                'status': 'healthy',
                'initialized': True,
                'memory_enabled': getattr(nova_ai, 'memory_enabled', False)
            }
        else:
            health_status['services']['nova_ai'] = {
                'status': 'unhealthy',
                'initialized': False
            }

        # Check AI service
        if ai_service:
            try:
                ai_stats = ai_service.get_stats()
                health_status['services']['ai_service'] = {
                    'status': 'healthy',
                    'providers_count': len(ai_stats.get('providers', [])),
                    'cache_enabled': ai_stats.get('cache_stats') is not None
                }
            except Exception as e:
                health_status['services']['ai_service'] = {
                    'status': 'degraded',
                    'error': str(e)
                }
        else:
            health_status['services']['ai_service'] = {
                'status': 'unavailable'
            }

        # Check cache service
        if cache_service:
            try:
                cache_stats = cache_service.get_stats()
                health_status['services']['cache'] = {
                    'status': 'healthy',
                    'type': cache_stats.get('type'),
                    'hit_rate': cache_stats.get('hit_rate', 0)
                }
            except Exception as e:
                health_status['services']['cache'] = {
                    'status': 'degraded',
                    'error': str(e)
                }
        else:
            health_status['services']['cache'] = {
                'status': 'unavailable'
            }

        # System health checks
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            health_status['system']['cpu_percent'] = cpu_percent

            # Memory usage
            memory = psutil.virtual_memory()
            health_status['system']['memory'] = {
                'total': memory.total,
                'available': memory.available,
                'percent': memory.percent,
                'used': memory.used
            }

            # Disk usage
            disk = psutil.disk_usage('/')
            health_status['system']['disk'] = {
                'total': disk.total,
                'used': disk.used,
                'free': disk.free,
                'percent': (disk.used / disk.total) * 100
            }

        except Exception as e:
            health_status['system']['error'] = str(e)

        # Determine overall status
        service_statuses = [s.get('status') for s in health_status['services'].values()]
        if 'unhealthy' in service_statuses:
            health_status['status'] = 'degraded'

        return jsonify(health_status)

    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': time.time()
        }), 500

@app.route('/api/monitoring/stats', methods=['GET'])
@track_performance
def get_system_stats():
    """Get comprehensive system statistics"""
    try:
        stats = {
            'timestamp': time.time(),
            'request_stats': request_stats.copy(),
            'services': {},
            'system': {}
        }

        # Service stats
        if ai_service:
            try:
                stats['services']['ai_service'] = ai_service.get_stats()
            except Exception as e:
                stats['services']['ai_service'] = {'error': str(e)}

        if cache_service:
            try:
                stats['services']['cache'] = cache_service.get_stats()
            except Exception as e:
                stats['services']['cache'] = {'error': str(e)}

        # System stats
        try:
            stats['system'] = {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': (psutil.disk_usage('/').used / psutil.disk_usage('/').total) * 100,
                'process_count': len(psutil.pids()),
                'uptime': time.time() - psutil.boot_time()
            }
        except Exception as e:
            stats['system']['error'] = str(e)

        # Session stats
        stats['sessions'] = {
            'active_sessions': len(chat_histories),
            'total_messages': sum(len(history) for history in chat_histories.values()),
            'locations_tracked': len(user_locations)
        }

        return jsonify(stats)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/monitoring/performance', methods=['GET'])
@track_performance
def get_performance_metrics():
    """Get performance metrics"""
    try:
        metrics = {
            'timestamp': time.time(),
            'request_performance': request_stats.copy(),
            'system_performance': {}
        }

        # System performance
        try:
            metrics['system_performance'] = {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_usage': psutil.virtual_memory()._asdict(),
                'disk_io': psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else None,
                'network_io': psutil.net_io_counters()._asdict() if psutil.net_io_counters() else None
            }
        except Exception as e:
            metrics['system_performance']['error'] = str(e)

        return jsonify(metrics)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# ===================== ADMIN API =====================

@app.route('/api/admin/config', methods=['GET'])
@track_performance
def get_configuration():
    """Get current configuration (masked sensitive values)"""
    try:
        if not config_service:
            return jsonify({'error': 'Configuration service not available'}), 500

        masked_config = config_service.get_masked()

        return jsonify({
            'success': True,
            'config': masked_config,
            'timestamp': time.time()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/reload', methods=['POST'])
@track_performance
def reload_services():
    """Reload services and configuration"""
    try:
        data = request.get_json() or {}
        service_name = data.get('service', 'all')

        reload_results = {}

        if service_name == 'all' or service_name == 'config':
            # Reload configuration
            if config_service:
                try:
                    config_service.load_config()
                    reload_results['config'] = {'status': 'success', 'message': 'Configuration reloaded'}
                    print("✅ Configuration reloaded")
                except Exception as e:
                    reload_results['config'] = {'status': 'error', 'message': str(e)}

        if service_name == 'all' or service_name == 'nova_ai':
            # Reload Nova AI
            try:
                if file_watcher:
                    file_watcher.reload_nova_ai()
                    reload_results['nova_ai'] = {'status': 'success', 'message': 'Nova AI reloaded'}
                    print("✅ Nova AI reloaded")
                else:
                    reload_results['nova_ai'] = {'status': 'error', 'message': 'File watcher not available'}
            except Exception as e:
                reload_results['nova_ai'] = {'status': 'error', 'message': str(e)}

        if service_name == 'all' or service_name == 'cache':
            # Clear cache
            if cache_service:
                try:
                    cache_service.clear()
                    reload_results['cache'] = {'status': 'success', 'message': 'Cache cleared'}
                    print("✅ Cache cleared")
                except Exception as e:
                    reload_results['cache'] = {'status': 'error', 'message': str(e)}

        return jsonify({
            'success': True,
            'service': service_name,
            'results': reload_results,
            'timestamp': time.time()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/time', methods=['POST'])
def get_time():
    try:
        data = request.get_json()
        location = data.get('location', '')
        if not location:
            # Return server's local time
            from datetime import datetime
            now = datetime.now()
            return jsonify({'time': now.strftime('%I:%M %p')})
        # Use your get_time_in_location function from nova_ai.py
        from core.nova_ai import get_time_in_location
        time_str = get_time_in_location(location)
        # Extract just the time (e.g., "The current time in X is HH:MM")
        import re
        match = re.search(r'is (\d{1,2}:\d{2})', time_str)
        if match:
            return jsonify({'time': match.group(1)})
        else:
            return jsonify({'time': '--:--'})
    except Exception as e:
        return jsonify({'time': '--:--'})

@app.route('/api/refresh', methods=['POST'])
def manual_refresh():
    """Manual refresh endpoint for testing"""
    try:
        global webview_window
        if webview_window:
            webview_window.reload()
            return jsonify({'status': 'refreshed'})
        else:
            return jsonify({'error': 'No webview window available'}), 500
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Duplicate routes removed - using enhanced widget API routes instead

@app.route('/api/voice/status', methods=['GET'])
def voice_status_api():
    """Get voice system status"""
    try:
        if nova_ai and hasattr(nova_ai, 'voice_service') and nova_ai.voice_service:
            return jsonify({
                'is_active': nova_ai.voice_enabled,
                'is_speaking': False,  # Could be enhanced to check actual speaking status
                'is_available': True,
                'status_text': 'Voice Available',
                'web_voice_enabled': nova_ai.web_voice_enabled
            })
        else:
            return jsonify({
                'is_active': False,
                'is_speaking': False,
                'is_available': False,
                'status_text': 'Voice Not Available'
            })
    except Exception as e:
        return jsonify({
            'is_active': False,
            'is_speaking': False,
            'is_available': False,
            'status_text': f'Voice Error: {str(e)}'
        })

@app.route('/api/voice/synthesize', methods=['POST'])
def voice_synthesize():
    """Synthesize voice for web UI"""
    try:
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({'success': False, 'error': 'Missing text parameter'}), 400

        text = data.get('text', '').strip()
        if not text:
            return jsonify({'success': False, 'error': 'Empty text provided'}), 400

        if nova_ai and hasattr(nova_ai, 'handle_voice_api_request'):
            result = nova_ai.handle_voice_api_request('/api/voice/synthesize', 'POST', data)
            return jsonify(result)
        else:
            return jsonify({'success': False, 'error': 'Voice service not available'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/voice/enable', methods=['POST'])
def voice_enable():
    """Enable web voice functionality"""
    try:
        if nova_ai and hasattr(nova_ai, 'handle_voice_api_request'):
            result = nova_ai.handle_voice_api_request('/api/voice/enable', 'POST')
            return jsonify(result)
        else:
            return jsonify({'success': False, 'error': 'Voice service not available'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/voice/disable', methods=['POST'])
def voice_disable():
    """Disable web voice functionality"""
    try:
        if nova_ai and hasattr(nova_ai, 'handle_voice_api_request'):
            result = nova_ai.handle_voice_api_request('/api/voice/disable', 'POST')
            return jsonify(result)
        else:
            return jsonify({'success': False, 'error': 'Voice service not available'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/voice/test', methods=['POST'])
def voice_test():
    """Test audio system with a simple tone"""
    try:
        global direct_voice
        if direct_voice:
            success = direct_voice.test_audio_system()
            if success:
                return jsonify({'success': True, 'message': 'Audio test tone played'})
            else:
                return jsonify({'success': False, 'error': 'Audio test failed'})
        else:
            return jsonify({'success': False, 'error': 'Direct voice not available'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/voice/test-speech', methods=['POST'])
def voice_test_speech():
    """Test voice synthesis with sample text"""
    try:
        global direct_voice
        if direct_voice:
            test_text = "Hello, this is a test of the voice synthesis system. Can you hear me?"
            success = direct_voice.synthesize_and_play(test_text, "test_speech_123")
            if success:
                return jsonify({'success': True, 'message': 'Test speech synthesis started'})
            else:
                return jsonify({'success': False, 'error': 'Speech synthesis failed'})
        else:
            return jsonify({'success': False, 'error': 'Direct voice not available'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500



@app.route('/api/camera/analysis', methods=['POST'])
def camera_analysis():
    """Handle camera analysis results and send to chat with voice synthesis"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        analysis_text = data.get('analysis', '')
        session_id = data.get('session_id', 'camera_session')

        if not analysis_text:
            return jsonify({'error': 'No analysis text provided'}), 400

        # Clean up the analysis text to make it more natural
        # Remove any remaining "📹 Camera shows:" prefixes
        clean_text = analysis_text.replace('📹 Camera shows:', '').strip()

        # Make it more conversational
        if not clean_text.lower().startswith(('i', 'right now', 'looking at', 'i can see', 'i\'m looking')):
            clean_text = f"I'm looking at {clean_text}"

        print(f"[CAMERA] Analysis received: {clean_text}")

        # Old ai_responses.json processing removed - now using Simple Unified Voice System only

        return jsonify({
            'success': True,
            'message': 'Camera analysis processed successfully',
            'analysis': clean_text,
            'timestamp': time.time()
        })

    except Exception as e:
        print(f"Error in camera analysis endpoint: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/camera/send-to-chat', methods=['POST'])
def camera_send_to_chat():
    """Send camera analysis result as a chat message with voice synthesis"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        analysis_text = data.get('analysis', '')
        session_id = data.get('session_id', 'camera_chat')
        voice_only = data.get('voice_only', False)  # For voice-only messages

        if not analysis_text:
            return jsonify({'error': 'No analysis text provided'}), 400

        # Simple deduplication check - prevent processing identical messages within 2 seconds
        current_time = time.time()
        message_key = f"{session_id}_{hash(analysis_text)}"

        if not hasattr(camera_send_to_chat, 'recent_messages'):
            camera_send_to_chat.recent_messages = {}

        if message_key in camera_send_to_chat.recent_messages:
            time_diff = current_time - camera_send_to_chat.recent_messages[message_key]
            if time_diff < 2.0:  # Skip if same message within 2 seconds
                print(f"[CAMERA-CHAT] 🔄 Skipping duplicate message within 2 seconds")
                return jsonify({'success': True, 'skipped': True})

        camera_send_to_chat.recent_messages[message_key] = current_time

        # Clean up the analysis text and ensure natural first-person format
        clean_text = analysis_text.replace(' Camera shows:', '').replace('📹 Camera shows:', '').replace('🔍 Object identified:', '').strip()

        # Convert to natural first-person language for camera responses
        if not voice_only and not clean_text.lower().startswith(('i\'m seeing', 'i can see', 'i\'m looking', 'right now i see', 'i see', 'i\'m opening')):
            # Convert to natural first-person format
            if 'person' in clean_text.lower() or 'someone' in clean_text.lower():
                clean_text = f"I'm seeing {clean_text.lower()}"
            else:
                clean_text = f"I can see {clean_text.lower()}"

        print(f"[CAMERA-CHAT] Processing camera message: {clean_text}")

        # Add to Simple Unified Voice System with duplicate prevention
        try:
            if SIMPLE_VOICE_AVAILABLE:
                # Check for duplicates before adding
                if not is_duplicate_message('camera', clean_text, session_id):
                    message_id = add_camera_message(clean_text, session_id)
                    if message_id:
                        print(f"[UNIFIED-VOICE] Camera message added: {message_id}")
                        print(f"[UNIFIED-VOICE] 📹 Camera analysis: '{clean_text[:50]}...'")
                    else:
                        print(f"[UNIFIED-VOICE] Failed to add camera message")
                else:
                    print(f"[UNIFIED-VOICE] Skipping duplicate camera message")
            else:
                # Fallback to direct voice synthesis
                print(f"[CAMERA-CHAT] Simple voice not available, using direct voice synthesis")
                if direct_voice:
                    response_id = f"camera_analysis_{session_id}_{time.time()}"
                    threading.Thread(
                        target=direct_voice.synthesize_and_play,
                        args=(clean_text, response_id),
                        daemon=True
                    ).start()
                    print(f"[CAMERA-CHAT] 🎤 Direct voice synthesis started: '{clean_text[:50]}...'")
                else:
                    print(f"[CAMERA-CHAT] ⚠️ No voice synthesis available")

        except Exception as e:
            print(f"Warning: Failed to add camera message to voice system: {e}")
            # Fallback to direct voice synthesis
            try:
                if direct_voice:
                    response_id = f"camera_analysis_{session_id}_{time.time()}"
                    threading.Thread(
                        target=direct_voice.synthesize_and_play,
                        args=(clean_text, response_id),
                        daemon=True
                    ).start()
                    print(f"[CAMERA-CHAT] 🎤 Fallback voice synthesis started")
            except Exception as fallback_error:
                print(f"Warning: Fallback camera voice synthesis also failed: {fallback_error}")

        return jsonify({
            'success': True,
            'response': clean_text,
            'session_id': session_id,
            'timestamp': time.time(),
            'voice_synthesis': True,
            'natural_format': True
        })

    except Exception as e:
        print(f"Error in camera send-to-chat endpoint: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/camera/get-analysis', methods=['GET'])
def get_camera_analysis():
    """Get latest camera analysis from camera.json"""
    try:
        camera_json_file = Path(__file__).parent.parent / 'ui' / 'camera.json'

        if not camera_json_file.exists():
            return jsonify({
                'success': False,
                'message': 'No camera analysis available',
                'analyses': []
            })

        with open(camera_json_file, 'r', encoding='utf-8') as f:
            camera_data = json.load(f)

        # Get latest analysis or all analyses based on query parameter
        limit = request.args.get('limit', 1, type=int)
        latest_analyses = camera_data[-limit:] if camera_data else []

        return jsonify({
            'success': True,
            'total_analyses': len(camera_data),
            'latest_analyses': latest_analyses,
            'timestamp': time.time()
        })

    except Exception as e:
        print(f"Error in get camera analysis endpoint: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/camera/voice-status', methods=['GET'])
def camera_voice_status():
    """Get camera voice synthesis status"""
    try:
        camera_json_file = Path(__file__).parent.parent / 'ui' / 'camera.json'

        # Check if camera.json exists and has recent entries
        camera_available = camera_json_file.exists()
        recent_analyses = 0

        if camera_available:
            with open(camera_json_file, 'r', encoding='utf-8') as f:
                camera_data = json.load(f)

            # Count analyses from last 5 minutes
            current_time = time.time()
            recent_analyses = sum(1 for analysis in camera_data
                                if current_time - time.mktime(time.strptime(
                                    analysis['timestamp'][:19], '%Y-%m-%dT%H:%M:%S'
                                )) < 300)

        return jsonify({
            'success': True,
            'camera_json_available': camera_available,
            'recent_analyses': recent_analyses,
            'voice_system_active': direct_voice is not None,
            'timestamp': time.time()
        })

    except Exception as e:
        print(f"Error in camera voice status endpoint: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/status', methods=['GET'])
def get_status():
    """Get comprehensive system status for settings panel"""
    try:
        global direct_voice, start_time

        # Check voice system status
        voice_status = {
            'available': direct_voice is not None,
            'enabled': direct_voice is not None and hasattr(direct_voice, 'voice_config'),
            'model': 'Cartesia Sonic' if direct_voice else None
        }

        # Check server status
        server_status = {
            'running': True,
            'uptime': time.time() - start_time if 'start_time' in globals() else 0,
            'port': 54016,
            'nova_ai_initialized': nova_ai is not None,
            'file_watcher_active': file_watcher is not None and file_watcher.is_alive() if file_watcher else False,
            'webview_windows': len(pywebview.windows) if hasattr(pywebview, 'windows') else 0
        }

        # Check camera status
        camera_status = {
            'available': True,  # Camera widget is always available
            'ai_vision': True   # Gemini Vision is integrated
        }

        return jsonify({
            'success': True,
            'status': 'running',
            'voice': voice_status,
            'server': server_status,
            'camera': camera_status,
            'timestamp': time.time()
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

def run_api_server(port):
    """Run the Flask API server"""
    # Suppress Flask development server messages
    import logging
    log = logging.getLogger('werkzeug')
    log.setLevel(logging.ERROR)

    app.run(host='127.0.0.1', port=port, debug=False, use_reloader=False)

def cleanup_on_exit():
    """Clean up resources on exit"""
    global file_watcher, voice_system
    logger.info("Cleaning up resources...")

    # Stop Simple Unified Voice System
    if SIMPLE_VOICE_AVAILABLE:
        logger.info("Stopping Simple Unified Voice System...")
        try:
            stop_voice_system()
            logger.info("Simple Unified Voice System stopped")
        except Exception as e:
            logger.error(f"Error stopping Simple Unified Voice System: {e}")

    # Stop legacy voice system
    if voice_system:
        logger.info("Stopping legacy voice system...")
        try:
            voice_system.stop_service()
            logger.info("Legacy voice system stopped")
        except Exception as e:
            logger.error(f"Error stopping legacy voice system: {e}")

    if file_watcher:
        logger.info("Stopping file watcher...")
        try:
            file_watcher.stop()
            file_watcher.join(timeout=2)
            logger.info("File watcher stopped")
        except Exception as e:
            logger.error(f"Error stopping file watcher: {e}")

def signal_handler(sig, frame):
    """Handle termination signals for graceful shutdown"""
    logger.info("Received termination signal. Shutting down gracefully...")
    cleanup_on_exit()
    sys.exit(0)

# Register signal handlers for graceful shutdown
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# ===================== ENHANCED STARTUP FUNCTIONS =====================

def create_default_config():
    """Create default configuration file if it doesn't exist"""
    config_path = 'config.yaml'
    if not os.path.exists(config_path):
        try:
            with open(config_path, 'w') as f:
                yaml.dump(DEFAULT_CONFIG, f, default_flow_style=False, indent=2)
            print(f"✅ Created default configuration file: {config_path}")
            print("   Edit this file to customize your settings")
        except Exception as e:
            print(f"⚠️ Failed to create config file: {e}")

def check_api_keys():
    """Check for API keys and provide guidance"""
    api_keys_found = []
    api_keys_missing = []

    key_mappings = {
        'NOVA_AI_OPENAI_API_KEY': 'OpenAI',
        'NOVA_AI_ANTHROPIC_API_KEY': 'Anthropic',
        'NOVA_AI_GOOGLE_API_KEY': 'Google Gemini',
        'NOVA_AI_GROQ_API_KEY': 'Groq',
        'GROQ_API_KEY': 'Groq (legacy)'
    }

    for env_var, provider in key_mappings.items():
        if os.getenv(env_var):
            api_keys_found.append(provider)
        else:
            api_keys_missing.append(f"{env_var} ({provider})")

    if api_keys_found:
        print(f"✅ API keys found for: {', '.join(api_keys_found)}")

    if api_keys_missing:
        print(f"⚠️ Missing API keys: {', '.join(api_keys_missing)}")
        print("   Set environment variables or edit config.yaml to add API keys")

    return len(api_keys_found) > 0

def validate_dependencies():
    """Validate optional dependencies and show status"""
    dependencies = {
        'Redis': REDIS_AVAILABLE,
        'OpenAI': OPENAI_AVAILABLE,
        'Anthropic': ANTHROPIC_AVAILABLE,
        'Google AI': GOOGLE_AVAILABLE,
        'Groq': GROQ_AVAILABLE,
        'bcrypt': BCRYPT_AVAILABLE,
        'JWT': JWT_AVAILABLE
    }

    print("\n📦 Optional Dependencies Status:")
    for dep, available in dependencies.items():
        status = "✅" if available else "⚠️"
        print(f"   {status} {dep}: {'Available' if available else 'Not installed'}")

    return dependencies

def main():
    """Enhanced main function with comprehensive service initialization"""

    # Record start time for status endpoint
    global start_time
    start_time = time.time()

    # Display startup banner
    print("\n" + "=" * 60)
    print("🤖 NOVA AI DESKTOP SERVER")
    print("=" * 60)

    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return

    # Check for required dependencies
    try:
        import watchdog
    except ImportError:
        print("❌ watchdog library not found. Installing...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "watchdog"])
        except Exception as e:
            print(f"❌ Failed to install watchdog: {e}")
            print("💡 Please install manually: pip install watchdog")
            return

    # Create default configuration if needed
    create_default_config()

    # Validate dependencies
    validate_dependencies()

    # Check API keys
    print("\n🔑 API Keys Status:")
    has_api_keys = check_api_keys()
    if not has_api_keys:
        print("   💡 You can still use Nova AI core functionality without external API keys")

    # Initialize enhanced services
    enhanced_services_available = initialize_enhanced_services()

    if enhanced_services_available:
        print("✅ Services initialized")

        # Display configuration summary
        if config_service and ai_service:
            api_port = config_service.get('server.api_port', 8081)
            ui_port = config_service.get('server.ui_port', 8080)

            # Check AI providers
            ai_providers = config_service.get('ai.providers', {})
            enabled_providers = [name for name, cfg in ai_providers.items() if cfg.get('enabled')]

            print(f"   • Ports: API {api_port}, UI {ui_port}")
            print(f"   • AI Providers: {', '.join(enabled_providers) if enabled_providers else 'none'}")
    else:
        print("⚠️ Enhanced services not available - using basic functionality")

    # Create necessary directories
    directories = ['data', 'logs', 'data/widgets', 'backups']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

    print("🤖 Initializing Nova AI...")

    # Initialize Nova AI first
    if not initialize_nova_ai():
        print("❌ Failed to start Nova AI. Please check your GROQ_API_KEY environment variable.")
        return

    # Initialize Voice System
    print("🎤 Initializing AI Voice System...")
    voice_initialized = initialize_voice_system()
    if voice_initialized:
        if SIMPLE_VOICE_AVAILABLE:
            print("✅ Simple Unified Voice System ready - responses will be spoken aloud")
            print("   📄 Unified messages: ui/unified_messages.json")
            print("   📋 Processed tracking: ui/processed.json")
            print("   🔊 Voice synthesis: Cartesia API")
            print("   🎤 No duplicate processing")
            print("   🔗 Integration: UI ↔ Nova AI ↔ Simple Voice System")
        else:
            print("✅ Direct Voice System ready - responses will be spoken aloud")
            print("   🔊 Voice synthesis: Direct Cartesia API")
    else:
        print("⚠️ Voice System not available - text-only mode")

    # Print integration summary
    print("\n" + "=" * 50)
    print("🔗 UNIFIED AI COMMUNICATION SYSTEM")
    print("=" * 50)
    print("✅ Chat Interface → Nova AI Backend")
    print("✅ Nova AI → Voice Synthesis")
    print("✅ Synchronized Responses")
    print("✅ Bidirectional Communication")
    if voice_initialized:
        print("✅ Voice Output Active")
    else:
        print("⚠️ Voice Output Disabled")
    print("=" * 50)
    
    # Get the path to the UI directory containing splash_screen.html
    current_dir = Path(__file__).parent.parent
    ui_dir = current_dir / 'ui'
    
    if not ui_dir.exists():
        print(f"❌ UI directory not found: {ui_dir}")
        return
    
    # Get free ports for both servers
    ui_port = get_free_port()
    api_port = get_free_port()
    
    # Start the UI server in a separate thread
    ui_server_thread = threading.Thread(
        target=run_server, 
        args=(ui_port, ui_dir),
        daemon=True
    )
    ui_server_thread.start()
    
    # Start the API server in a separate thread
    api_server_thread = threading.Thread(
        target=run_api_server,
        args=(api_port,),
        daemon=True
    )
    api_server_thread.start()
    
    # Wait a moment for servers to start
    time.sleep(2)

    # Display startup summary
    print("\n" + "=" * 60)
    print("🎉 Nova AI Server Started Successfully!")
    print("=" * 60)
    print(f"🌐 API Server: http://127.0.0.1:{api_port}")
    print(f"🖥️ UI Server: http://127.0.0.1:{ui_port}")
    print("=" * 60)

    print("\n✅ Opening Nova AI interface...")
    
    # Create and show the window with API port passed as a parameter
    global webview_window
    webview_window = pywebview.create_window(
        'Nova AI UI - Auto-Refresh Enabled',
        f'http://127.0.0.1:{ui_port}/splash_screen.html?api_port={api_port}',
        width=1200,
        height=800,
        min_size=(1200, 800),
        background_color='#000000'
    )
    
    # Set up file watcher after window is created (with delay to ensure window is ready)
    def setup_watcher_delayed():
        time.sleep(3)  # Wait for webview to fully initialize
        watcher = setup_file_watcher(webview_window)
        if watcher:
            print("✅ Auto-refresh enabled")
        else:
            print("⚠️ Auto-refresh disabled")
    
    watcher_thread = threading.Thread(target=setup_watcher_delayed, daemon=True)
    watcher_thread.start()
    
    # Register cleanup function
    import atexit
    atexit.register(cleanup_on_exit)
    
    print("\n" + "="*60)
    print("🚀 Nova AI Desktop Interface Ready!")
    print("="*60 + "\n")
    
    try:
        pywebview.start()
    except KeyboardInterrupt:
        print("\n🛑 Shutting down...")
    except Exception as e:
        print(f"\n❌ Error in main loop: {e}")
    finally:
        cleanup_on_exit()

# ===================== ENHANCED SHUTDOWN HANDLING =====================

def enhanced_cleanup():
    """Enhanced cleanup with service shutdown"""
    print("\n🛑 Enhanced shutdown initiated...")

    try:
        # Stop file watcher
        global file_watcher
        if file_watcher and hasattr(file_watcher, 'observer'):
            file_watcher.observer.stop()
            file_watcher.observer.join()
            print("✅ File watcher stopped")

        # Clear cache
        if cache_service:
            cache_service.clear()
            print("✅ Cache cleared")

        # Save configuration
        if config_service:
            config_service.save()
            print("✅ Configuration saved")

        # Original cleanup
        cleanup_on_exit()

        print("✅ Enhanced cleanup completed")

    except Exception as e:
        print(f"⚠️ Error during enhanced cleanup: {e}")

def enhanced_main():
    """Enhanced main with better error handling"""
    try:
        print("🚀 Starting Enhanced Nova AI...")

        # Register signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, lambda _signum, _frame: enhanced_cleanup())
        signal.signal(signal.SIGTERM, lambda _signum, _frame: enhanced_cleanup())

        print("🔧 Signal handlers registered")

        # Run main function
        print("🎯 Calling main function...")
        main()

    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
        enhanced_cleanup()
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()
        enhanced_cleanup()

if __name__ == '__main__':
    enhanced_main()