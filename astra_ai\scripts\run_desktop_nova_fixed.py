#!/usr/bin/env python3
"""
Enhanced Nova AI Desktop Server - Fixed Version
Resolves pywebview hanging issues and provides fallback functionality
"""

import sys
import os
import json
import time
import threading
import signal
import secrets
from pathlib import Path
from flask import Flask, request, jsonify
from flask_cors import CORS

# Add the parent directory to the path to import Nova AI
sys.path.append(str(Path(__file__).parent.parent))

print("🚀 Starting Enhanced Nova AI Server (Fixed Version)...")

# Try to import Nova AI
try:
    from core.nova_ai import AleChatBot
    print("✅ Nova AI core imported successfully")
    NOVA_AI_AVAILABLE = True
except Exception as e:
    print(f"❌ Nova AI import failed: {e}")
    NOVA_AI_AVAILABLE = False

# Skip pywebview import to avoid hanging - use browser fallback
PYWEBVIEW_AVAILABLE = False
print("ℹ️ PyWebView disabled to avoid import issues - using browser interface")

# Global variables
nova_ai = None
chat_histories = {}
user_locations = {}

# Create Flask app
app = Flask(__name__)
CORS(app)

def initialize_nova_ai():
    """Initialize Nova AI"""
    global nova_ai
    if not NOVA_AI_AVAILABLE:
        print("❌ Nova AI not available")
        return False
    
    try:
        print("🤖 Initializing Nova AI...")
        nova_ai = AleChatBot()
        print("✅ Nova AI initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Nova AI initialization failed: {e}")
        return False

# ===================== API ENDPOINTS =====================

@app.route('/api/chat', methods=['POST'])
def chat():
    """Enhanced chat endpoint"""
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()
        session_id = data.get('session_id', 'default')
        user_location = data.get('user_location', '')
        
        if not user_message:
            return jsonify({'error': 'No message provided'}), 400
        
        if not nova_ai:
            return jsonify({'error': 'Nova AI not initialized'}), 500
        
        # Get or create session chat history
        if session_id not in chat_histories:
            chat_histories[session_id] = []
        
        # Handle user location
        if user_location:
            user_locations[session_id] = user_location
        elif session_id in user_locations:
            user_location = user_locations[session_id]
        else:
            user_location = "Italy"
            user_locations[session_id] = user_location
        
        print(f"💬 Chat request: {user_message[:50]}...")
        
        # Build messages for Nova AI
        messages = nova_ai.chat_history + chat_histories[session_id]
        
        # Add system context
        messages.append({
            "role": "system",
            "content": f"You are running in a desktop UI. User location: {user_location}. Provide helpful responses."
        })
        
        # Add user message
        messages.append({"role": "user", "content": user_message})
        
        # Get response from Nova AI
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            response = loop.run_until_complete(
                nova_ai.get_response(messages, stream_to_terminal=False)
            )
        finally:
            loop.close()
        
        if response:
            # Update session history
            chat_histories[session_id].extend([
                {"role": "user", "content": user_message},
                {"role": "assistant", "content": response}
            ])
            
            # Keep session history manageable
            if len(chat_histories[session_id]) > 20:
                chat_histories[session_id] = chat_histories[session_id][-20:]
            
            print(f"✅ Response generated successfully")
            return jsonify({
                'response': response,
                'session_id': session_id,
                'location': user_location,
                'source': 'nova_ai'
            })
        else:
            return jsonify({'error': 'No response generated'}), 500
            
    except Exception as e:
        print(f"❌ Error in chat endpoint: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """System health check"""
    return jsonify({
        'status': 'healthy',
        'nova_ai_available': NOVA_AI_AVAILABLE,
        'nova_ai_initialized': nova_ai is not None,
        'pywebview_available': PYWEBVIEW_AVAILABLE,
        'active_sessions': len(chat_histories),
        'timestamp': time.time()
    })

@app.route('/api/status', methods=['GET'])
def get_status():
    """Get system status"""
    return jsonify({
        'status': 'running',
        'nova_ai_initialized': nova_ai is not None,
        'sessions': len(chat_histories),
        'pywebview_available': PYWEBVIEW_AVAILABLE
    })

@app.route('/api/widgets/notepad/save', methods=['POST'])
def save_notepad_notes():
    """Save notepad notes"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Create data directory
        data_dir = Path('data/widgets')
        data_dir.mkdir(parents=True, exist_ok=True)
        
        # Save notes
        notepad_file = data_dir / 'notepad_notes.json'
        with open(notepad_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        print(f"📝 Saved {data.get('totalNotes', 0)} notepad notes")
        return jsonify({
            'success': True,
            'message': 'Notes saved successfully',
            'notes_count': data.get('totalNotes', 0)
        })

    except Exception as e:
        print(f"❌ Error saving notepad notes: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/widgets/notepad/load', methods=['GET'])
def load_notepad_notes():
    """Load notepad notes"""
    try:
        notepad_file = Path('data/widgets/notepad_notes.json')
        
        if not notepad_file.exists():
            return jsonify({
                'success': True,
                'notes': [],
                'message': 'No notes file found'
            })

        with open(notepad_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        notes = data.get('notes', [])
        print(f"📝 Loaded {len(notes)} notepad notes")
        
        return jsonify({
            'success': True,
            'notes': notes,
            'notes_count': len(notes)
        })

    except Exception as e:
        print(f"❌ Error loading notepad notes: {e}")
        return jsonify({'error': str(e)}), 500

def run_flask_server(port):
    """Run the Flask server"""
    print(f"🌐 Starting API server on port {port}")
    app.run(host='127.0.0.1', port=port, debug=False, use_reloader=False)

def open_in_browser(url):
    """Open URL in default browser"""
    import webbrowser
    print(f"🌐 Opening {url} in browser...")
    webbrowser.open(url)

def main():
    """Main function"""
    print("\n" + "=" * 60)
    print("🤖 ENHANCED NOVA AI SERVER (FIXED)")
    print("   Resolves PyWebView hanging issues")
    print("=" * 60)
    
    # Initialize Nova AI
    if not initialize_nova_ai():
        print("⚠️ Nova AI initialization failed - limited functionality")
    
    # Get free port
    import socket
    def get_free_port():
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', 0))
            return s.getsockname()[1]
    
    api_port = get_free_port()
    ui_port = get_free_port()
    
    # Start Flask server
    flask_thread = threading.Thread(target=run_flask_server, args=(api_port,), daemon=True)
    flask_thread.start()
    
    # Wait for server to start
    time.sleep(2)
    
    print("\n" + "=" * 60)
    print("🎉 Enhanced Nova AI Server Started!")
    print("=" * 60)
    print(f"🌐 API Server: http://127.0.0.1:{api_port}")
    print(f"🔍 Health Check: http://127.0.0.1:{api_port}/api/health")
    print(f"📊 Status: http://127.0.0.1:{api_port}/api/status")
    print("\n📡 Available Endpoints:")
    print("   • /api/chat - Chat with Nova AI")
    print("   • /api/health - Health check")
    print("   • /api/status - System status")
    print("   • /api/widgets/* - Widget APIs")
    print("=" * 60)
    
    # Try to open UI
    ui_url = f"http://127.0.0.1:{api_port}/api/health"
    
    if PYWEBVIEW_AVAILABLE:
        try:
            print("🖥️ Starting PyWebView interface...")
            pywebview.create_window(
                'Enhanced Nova AI Server',
                ui_url,
                width=1200,
                height=800
            )
            pywebview.start()
        except Exception as e:
            print(f"⚠️ PyWebView failed: {e}")
            print("🌐 Opening in browser instead...")
            open_in_browser(ui_url)
    else:
        print("🌐 Opening health check in browser...")
        open_in_browser(ui_url)
    
    # Keep server running
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Shutting down...")

if __name__ == '__main__':
    main()
