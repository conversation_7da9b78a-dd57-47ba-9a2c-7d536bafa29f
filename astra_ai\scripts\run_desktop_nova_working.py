#!/usr/bin/env python3
"""
Enhanced Nova AI Desktop Server - Working Version
Based on your original script with enhanced API endpoints
"""

import sys
import os
import json
import time
import threading
import asyncio
import signal
import socket
import webbrowser
from pathlib import Path
from flask import Flask, request, jsonify
from flask_cors import CORS

print("🚀 Starting Enhanced Nova AI Desktop Server...")

# Add the parent directory to the path to import Nova AI
sys.path.append(str(Path(__file__).parent.parent))

# Global variables
nova_ai = None
chat_histories = {}
user_locations = {}
request_stats = {
    'total_requests': 0,
    'successful_requests': 0,
    'failed_requests': 0
}

def initialize_nova_ai():
    """Initialize Nova AI"""
    global nova_ai
    try:
        print("🤖 Initializing Nova AI...")
        from core.nova_ai import AleChatBot
        nova_ai = AleChatBot()
        print("✅ Nova AI initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Nova AI initialization failed: {e}")
        return False

def get_free_port():
    """Get a free port"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(('', 0))
        return s.getsockname()[1]

# Create Flask app
app = Flask(__name__)
CORS(app)

# Performance tracking decorator
def track_performance(f):
    """Decorator to track API performance"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        request_stats['total_requests'] += 1
        
        try:
            result = f(*args, **kwargs)
            request_stats['successful_requests'] += 1
            return result
        except Exception as e:
            request_stats['failed_requests'] += 1
            raise
    
    wrapper.__name__ = f.__name__
    return wrapper

# ===================== ENHANCED API ENDPOINTS =====================

@app.route('/api/chat', methods=['POST'])
@track_performance
def chat():
    """Enhanced chat endpoint"""
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()
        session_id = data.get('session_id', 'default')
        user_location = data.get('user_location', '')
        
        if not user_message:
            return jsonify({'error': 'No message provided'}), 400
        
        if not nova_ai:
            return jsonify({'error': 'Nova AI not initialized'}), 500
        
        # Get or create session chat history
        if session_id not in chat_histories:
            chat_histories[session_id] = []
        
        # Handle user location
        if user_location:
            user_locations[session_id] = user_location
        elif session_id in user_locations:
            user_location = user_locations[session_id]
        else:
            user_location = "Italy"
            user_locations[session_id] = user_location
        
        print(f"💬 Chat request: {user_message[:50]}...")
        
        # Build messages for Nova AI
        messages = nova_ai.chat_history + chat_histories[session_id]
        
        # Add system context
        messages.append({
            "role": "system",
            "content": f"You are running in a desktop UI. User location: {user_location}. Provide helpful responses."
        })
        
        # Add user message
        messages.append({"role": "user", "content": user_message})
        
        # Get response from Nova AI
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            response = loop.run_until_complete(
                nova_ai.get_response(messages, stream_to_terminal=False)
            )
        finally:
            loop.close()
        
        if response:
            # Update session history
            chat_histories[session_id].extend([
                {"role": "user", "content": user_message},
                {"role": "assistant", "content": response}
            ])
            
            # Keep session history manageable
            if len(chat_histories[session_id]) > 20:
                chat_histories[session_id] = chat_histories[session_id][-20:]
            
            print(f"✅ Response generated successfully")
            return jsonify({
                'response': response,
                'session_id': session_id,
                'location': user_location,
                'source': 'nova_ai'
            })
        else:
            return jsonify({'error': 'No response generated'}), 500
            
    except Exception as e:
        print(f"❌ Error in chat endpoint: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/health', methods=['GET'])
@track_performance
def health_check():
    """System health check"""
    return jsonify({
        'status': 'healthy',
        'nova_ai_initialized': nova_ai is not None,
        'active_sessions': len(chat_histories),
        'request_stats': request_stats,
        'timestamp': time.time()
    })

@app.route('/api/status', methods=['GET'])
@track_performance
def get_status():
    """Get system status"""
    return jsonify({
        'status': 'running',
        'nova_ai_initialized': nova_ai is not None,
        'sessions': len(chat_histories),
        'total_requests': request_stats['total_requests'],
        'success_rate': request_stats['successful_requests'] / max(request_stats['total_requests'], 1)
    })

@app.route('/api/widgets/notepad/save', methods=['POST'])
@track_performance
def save_notepad_notes():
    """Save notepad notes"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Create data directory
        data_dir = Path('data/widgets')
        data_dir.mkdir(parents=True, exist_ok=True)
        
        # Save notes
        notepad_file = data_dir / 'notepad_notes.json'
        with open(notepad_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        print(f"📝 Saved {data.get('totalNotes', 0)} notepad notes")
        return jsonify({
            'success': True,
            'message': 'Notes saved successfully',
            'notes_count': data.get('totalNotes', 0)
        })

    except Exception as e:
        print(f"❌ Error saving notepad notes: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/widgets/notepad/load', methods=['GET'])
@track_performance
def load_notepad_notes():
    """Load notepad notes"""
    try:
        notepad_file = Path('data/widgets/notepad_notes.json')
        
        if not notepad_file.exists():
            return jsonify({
                'success': True,
                'notes': [],
                'message': 'No notes file found'
            })

        with open(notepad_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        notes = data.get('notes', [])
        print(f"📝 Loaded {len(notes)} notepad notes")
        
        return jsonify({
            'success': True,
            'notes': notes,
            'notes_count': len(notes)
        })

    except Exception as e:
        print(f"❌ Error loading notepad notes: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/widgets/ai-summaries/save', methods=['POST'])
@track_performance
def save_ai_summaries():
    """Save AI summaries"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Create data directory
        data_dir = Path('data/widgets')
        data_dir.mkdir(parents=True, exist_ok=True)
        
        # Save summaries
        summaries_file = data_dir / 'ai_summaries.json'
        with open(summaries_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        print(f"🤖 Saved {data.get('totalSummaries', 0)} AI summaries")
        return jsonify({
            'success': True,
            'message': 'AI summaries saved successfully',
            'summaries_count': data.get('totalSummaries', 0)
        })

    except Exception as e:
        print(f"❌ Error saving AI summaries: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/widgets/ai-summaries/load', methods=['GET'])
@track_performance
def load_ai_summaries():
    """Load AI summaries"""
    try:
        summaries_file = Path('data/widgets/ai_summaries.json')
        
        if not summaries_file.exists():
            return jsonify({
                'success': True,
                'summaries': [],
                'message': 'No summaries file found'
            })

        with open(summaries_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        summaries = data.get('summaries', [])
        print(f"🤖 Loaded {len(summaries)} AI summaries")
        
        return jsonify({
            'success': True,
            'summaries': summaries,
            'summaries_count': len(summaries)
        })

    except Exception as e:
        print(f"❌ Error loading AI summaries: {e}")
        return jsonify({'error': str(e)}), 500

def run_flask_server(port):
    """Run the Flask server"""
    print(f"🌐 Starting API server on port {port}")
    app.run(host='127.0.0.1', port=port, debug=False, use_reloader=False)

def main():
    """Main function"""
    print("\n" + "=" * 60)
    print("🤖 ENHANCED NOVA AI DESKTOP SERVER")
    print("   Working Version with Enhanced APIs")
    print("=" * 60)
    
    # Initialize Nova AI
    if not initialize_nova_ai():
        print("⚠️ Nova AI initialization failed - limited functionality")
    
    # Get free port
    api_port = get_free_port()
    
    # Start Flask server
    flask_thread = threading.Thread(target=run_flask_server, args=(api_port,), daemon=True)
    flask_thread.start()
    
    # Wait for server to start
    time.sleep(2)
    
    print("\n" + "=" * 60)
    print("🎉 Enhanced Nova AI Server Started!")
    print("=" * 60)
    print(f"🌐 API Server: http://127.0.0.1:{api_port}")
    print(f"🔍 Health Check: http://127.0.0.1:{api_port}/api/health")
    print(f"📊 Status: http://127.0.0.1:{api_port}/api/status")
    print("\n📡 Available Endpoints:")
    print("   • /api/chat - Chat with Nova AI")
    print("   • /api/health - Health check")
    print("   • /api/status - System status")
    print("   • /api/widgets/notepad/* - Notepad APIs")
    print("   • /api/widgets/ai-summaries/* - AI Summary APIs")
    print("=" * 60)
    
    # Open health check in browser
    health_url = f"http://127.0.0.1:{api_port}/api/health"
    print(f"\n🌐 Opening health check: {health_url}")
    webbrowser.open(health_url)
    
    # Keep server running
    try:
        print("\n✅ Server is running. Press Ctrl+C to stop.")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Shutting down...")

if __name__ == '__main__':
    main()
