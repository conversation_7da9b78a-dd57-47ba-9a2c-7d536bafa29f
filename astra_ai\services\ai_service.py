"""
AI Service for Enhanced Nova AI Server
Handles multiple AI providers, load balancing, and AI model management
"""

import asyncio
import logging
import time
import random
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import json
import hashlib

# AI Provider imports
import openai
import anthropic
import google.generativeai as genai
from groq import Groq

class AIProvider(Enum):
    """Supported AI providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    GROQ = "groq"

@dataclass
class AIResponse:
    """AI response data structure"""
    content: str
    provider: str
    model: str
    tokens_used: int
    response_time: float
    cached: bool = False
    metadata: Dict[str, Any] = None

@dataclass
class ProviderStatus:
    """Provider health status"""
    name: str
    available: bool
    last_check: float
    error_count: int
    avg_response_time: float

class LoadBalancer:
    """Load balancer for AI providers"""
    
    def __init__(self, strategy: str = "round_robin"):
        self.strategy = strategy
        self.current_index = 0
        self.provider_weights = {}
        self.provider_stats = {}
    
    def select_provider(self, available_providers: List[str]) -> str:
        """Select provider based on load balancing strategy"""
        if not available_providers:
            raise ValueError("No available providers")
        
        if self.strategy == "round_robin":
            provider = available_providers[self.current_index % len(available_providers)]
            self.current_index += 1
            return provider
        
        elif self.strategy == "random":
            return random.choice(available_providers)
        
        elif self.strategy == "weighted":
            return self._weighted_selection(available_providers)
        
        elif self.strategy == "fastest":
            return self._fastest_provider(available_providers)
        
        else:
            return available_providers[0]
    
    def _weighted_selection(self, providers: List[str]) -> str:
        """Select provider based on weights"""
        weights = [self.provider_weights.get(p, 1.0) for p in providers]
        return random.choices(providers, weights=weights)[0]
    
    def _fastest_provider(self, providers: List[str]) -> str:
        """Select fastest responding provider"""
        fastest = min(providers, key=lambda p: self.provider_stats.get(p, {}).get('avg_response_time', float('inf')))
        return fastest

class AIService:
    """
    AI Service for managing multiple AI providers and models
    """
    
    def __init__(self, config):
        """Initialize AI service"""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Provider clients
        self.providers = {}
        self.provider_status = {}
        
        # Load balancing
        self.load_balancer = LoadBalancer(
            config.get('ai.load_balancing.strategy', 'round_robin')
        )
        
        # Caching
        self.cache_enabled = config.get('ai.caching.enabled', True)
        self.cache_ttl = config.get('ai.caching.ttl', 3600)
        self.response_cache = {}
        
        # Statistics
        self.stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'provider_usage': {},
            'error_count': 0
        }
        
        self._initialize_providers()
        self.logger.info("AI Service initialized")
    
    def _initialize_providers(self):
        """Initialize AI provider clients"""
        providers_config = self.config.get('ai.providers', {})
        
        for provider_name, provider_config in providers_config.items():
            if not provider_config.get('enabled', False):
                continue
            
            try:
                if provider_name == AIProvider.OPENAI.value:
                    self._init_openai(provider_config)
                elif provider_name == AIProvider.ANTHROPIC.value:
                    self._init_anthropic(provider_config)
                elif provider_name == AIProvider.GOOGLE.value:
                    self._init_google(provider_config)
                elif provider_name == AIProvider.GROQ.value:
                    self._init_groq(provider_config)
                
                self.provider_status[provider_name] = ProviderStatus(
                    name=provider_name,
                    available=True,
                    last_check=time.time(),
                    error_count=0,
                    avg_response_time=0.0
                )
                
                self.logger.info(f"Initialized AI provider: {provider_name}")
                
            except Exception as e:
                self.logger.error(f"Failed to initialize {provider_name}: {e}")
                self.provider_status[provider_name] = ProviderStatus(
                    name=provider_name,
                    available=False,
                    last_check=time.time(),
                    error_count=1,
                    avg_response_time=0.0
                )
    
    def _init_openai(self, config):
        """Initialize OpenAI client"""
        api_key = config.get('api_key')
        if not api_key:
            raise ValueError("OpenAI API key not provided")
        
        openai.api_key = api_key
        self.providers[AIProvider.OPENAI.value] = {
            'client': openai,
            'config': config
        }
    
    def _init_anthropic(self, config):
        """Initialize Anthropic client"""
        api_key = config.get('api_key')
        if not api_key:
            raise ValueError("Anthropic API key not provided")
        
        client = anthropic.Anthropic(api_key=api_key)
        self.providers[AIProvider.ANTHROPIC.value] = {
            'client': client,
            'config': config
        }
    
    def _init_google(self, config):
        """Initialize Google Gemini client"""
        api_key = config.get('api_key')
        if not api_key:
            raise ValueError("Google API key not provided")
        
        genai.configure(api_key=api_key)
        self.providers[AIProvider.GOOGLE.value] = {
            'client': genai,
            'config': config
        }
    
    def _init_groq(self, config):
        """Initialize Groq client"""
        api_key = config.get('api_key')
        if not api_key:
            raise ValueError("Groq API key not provided")
        
        client = Groq(api_key=api_key)
        self.providers[AIProvider.GROQ.value] = {
            'client': client,
            'config': config
        }
    
    async def get_response(self, messages: List[Dict], provider: Optional[str] = None, **kwargs) -> AIResponse:
        """Get AI response with load balancing and caching"""
        self.stats['total_requests'] += 1
        
        # Check cache first
        if self.cache_enabled:
            cache_key = self._generate_cache_key(messages, kwargs)
            cached_response = self._get_cached_response(cache_key)
            if cached_response:
                self.stats['cache_hits'] += 1
                return cached_response
        
        # Select provider
        if provider and provider in self.providers:
            selected_provider = provider
        else:
            available_providers = self._get_available_providers()
            if not available_providers:
                raise RuntimeError("No AI providers available")
            selected_provider = self.load_balancer.select_provider(available_providers)
        
        # Get response from provider
        start_time = time.time()
        try:
            response = await self._call_provider(selected_provider, messages, **kwargs)
            response_time = time.time() - start_time
            
            # Update statistics
            self._update_provider_stats(selected_provider, response_time, success=True)
            
            # Cache response
            if self.cache_enabled:
                self._cache_response(cache_key, response)
            
            return response
            
        except Exception as e:
            response_time = time.time() - start_time
            self._update_provider_stats(selected_provider, response_time, success=False)
            self.stats['error_count'] += 1
            
            # Try fallback provider
            if not provider:  # Only fallback if provider wasn't specifically requested
                fallback_providers = [p for p in self._get_available_providers() if p != selected_provider]
                if fallback_providers:
                    self.logger.warning(f"Provider {selected_provider} failed, trying fallback")
                    return await self.get_response(messages, fallback_providers[0], **kwargs)
            
            raise e
    
    async def _call_provider(self, provider_name: str, messages: List[Dict], **kwargs) -> AIResponse:
        """Call specific AI provider"""
        provider_info = self.providers[provider_name]
        client = provider_info['client']
        config = provider_info['config']
        
        model = kwargs.get('model', config.get('model'))
        max_tokens = kwargs.get('max_tokens', config.get('max_tokens', 4000))
        temperature = kwargs.get('temperature', 0.7)
        
        start_time = time.time()
        
        try:
            if provider_name == AIProvider.OPENAI.value:
                response = await self._call_openai(client, messages, model, max_tokens, temperature)
            elif provider_name == AIProvider.ANTHROPIC.value:
                response = await self._call_anthropic(client, messages, model, max_tokens, temperature)
            elif provider_name == AIProvider.GOOGLE.value:
                response = await self._call_google(client, messages, model, max_tokens, temperature)
            elif provider_name == AIProvider.GROQ.value:
                response = await self._call_groq(client, messages, model, max_tokens, temperature)
            else:
                raise ValueError(f"Unknown provider: {provider_name}")
            
            response_time = time.time() - start_time
            
            return AIResponse(
                content=response['content'],
                provider=provider_name,
                model=model,
                tokens_used=response.get('tokens_used', 0),
                response_time=response_time,
                metadata=response.get('metadata', {})
            )
            
        except Exception as e:
            self.logger.error(f"Error calling {provider_name}: {e}")
            raise
    
    async def _call_openai(self, client, messages, model, max_tokens, temperature):
        """Call OpenAI API"""
        # Use the new OpenAI client format
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature
        )

        return {
            'content': response.choices[0].message.content,
            'tokens_used': response.usage.total_tokens,
            'metadata': {'finish_reason': response.choices[0].finish_reason}
        }
    
    async def _call_anthropic(self, client, messages, model, max_tokens, temperature):
        """Call Anthropic API"""
        # Convert messages format for Anthropic
        system_message = ""
        user_messages = []

        for msg in messages:
            if msg['role'] == 'system':
                system_message += msg['content'] + "\n"
            else:
                user_messages.append(msg)

        response = client.messages.create(
            model=model,
            max_tokens=max_tokens,
            temperature=temperature,
            system=system_message,
            messages=user_messages
        )

        return {
            'content': response.content[0].text,
            'tokens_used': response.usage.input_tokens + response.usage.output_tokens,
            'metadata': {'stop_reason': response.stop_reason}
        }
    
    async def _call_google(self, client, messages, model, max_tokens, temperature):
        """Call Google Gemini API"""
        model_instance = client.GenerativeModel(model)

        # Convert messages to Gemini format
        prompt = self._convert_messages_to_prompt(messages)

        # Use synchronous call for now (async support varies)
        response = model_instance.generate_content(
            prompt,
            generation_config=genai.types.GenerationConfig(
                max_output_tokens=max_tokens,
                temperature=temperature
            )
        )

        return {
            'content': response.text,
            'tokens_used': response.usage_metadata.total_token_count if hasattr(response, 'usage_metadata') else 0,
            'metadata': {'finish_reason': response.candidates[0].finish_reason.name if response.candidates else None}
        }
    
    async def _call_groq(self, client, messages, model, max_tokens, temperature):
        """Call Groq API"""
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature
        )
        
        return {
            'content': response.choices[0].message.content,
            'tokens_used': response.usage.total_tokens,
            'metadata': {'finish_reason': response.choices[0].finish_reason}
        }
    
    def _convert_messages_to_prompt(self, messages: List[Dict]) -> str:
        """Convert messages to a single prompt for models that need it"""
        prompt_parts = []
        for msg in messages:
            role = msg['role']
            content = msg['content']
            if role == 'system':
                prompt_parts.append(f"System: {content}")
            elif role == 'user':
                prompt_parts.append(f"User: {content}")
            elif role == 'assistant':
                prompt_parts.append(f"Assistant: {content}")
        
        return "\n\n".join(prompt_parts)
    
    def _get_available_providers(self) -> List[str]:
        """Get list of available providers"""
        return [
            name for name, status in self.provider_status.items()
            if status.available and name in self.providers
        ]
    
    def _generate_cache_key(self, messages: List[Dict], kwargs: Dict) -> str:
        """Generate cache key for request"""
        cache_data = {
            'messages': messages,
            'kwargs': {k: v for k, v in kwargs.items() if k in ['model', 'temperature', 'max_tokens']}
        }
        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    def _get_cached_response(self, cache_key: str) -> Optional[AIResponse]:
        """Get cached response if available and not expired"""
        if cache_key in self.response_cache:
            cached_data, timestamp = self.response_cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                cached_data.cached = True
                return cached_data
            else:
                del self.response_cache[cache_key]
        return None
    
    def _cache_response(self, cache_key: str, response: AIResponse):
        """Cache AI response"""
        self.response_cache[cache_key] = (response, time.time())
        
        # Clean old cache entries
        if len(self.response_cache) > self.config.get('ai.caching.max_size', 1000):
            oldest_key = min(self.response_cache.keys(), 
                           key=lambda k: self.response_cache[k][1])
            del self.response_cache[oldest_key]
    
    def _update_provider_stats(self, provider: str, response_time: float, success: bool):
        """Update provider statistics"""
        if provider not in self.stats['provider_usage']:
            self.stats['provider_usage'][provider] = {
                'requests': 0,
                'errors': 0,
                'total_response_time': 0.0
            }
        
        stats = self.stats['provider_usage'][provider]
        stats['requests'] += 1
        stats['total_response_time'] += response_time
        
        if not success:
            stats['errors'] += 1
            self.provider_status[provider].error_count += 1
        
        # Update average response time
        avg_time = stats['total_response_time'] / stats['requests']
        self.provider_status[provider].avg_response_time = avg_time
        self.load_balancer.provider_stats[provider] = {'avg_response_time': avg_time}
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all providers"""
        health_status = {}
        
        for provider_name in self.providers.keys():
            try:
                # Simple test message
                test_messages = [{"role": "user", "content": "Hello"}]
                start_time = time.time()
                
                await self._call_provider(provider_name, test_messages, max_tokens=10)
                
                response_time = time.time() - start_time
                health_status[provider_name] = {
                    'status': 'healthy',
                    'response_time': response_time
                }
                
                self.provider_status[provider_name].available = True
                self.provider_status[provider_name].last_check = time.time()
                
            except Exception as e:
                health_status[provider_name] = {
                    'status': 'unhealthy',
                    'error': str(e)
                }
                
                self.provider_status[provider_name].available = False
                self.provider_status[provider_name].last_check = time.time()
        
        return health_status
    
    def get_stats(self) -> Dict[str, Any]:
        """Get service statistics"""
        return {
            'total_requests': self.stats['total_requests'],
            'cache_hits': self.stats['cache_hits'],
            'cache_hit_rate': self.stats['cache_hits'] / max(self.stats['total_requests'], 1),
            'error_count': self.stats['error_count'],
            'error_rate': self.stats['error_count'] / max(self.stats['total_requests'], 1),
            'provider_usage': self.stats['provider_usage'],
            'provider_status': {name: {
                'available': status.available,
                'error_count': status.error_count,
                'avg_response_time': status.avg_response_time
            } for name, status in self.provider_status.items()},
            'cache_size': len(self.response_cache)
        }
    
    def clear_cache(self):
        """Clear response cache"""
        self.response_cache.clear()
        self.logger.info("AI response cache cleared")
    
    def shutdown(self):
        """Shutdown AI service"""
        self.clear_cache()
        self.logger.info("AI Service shutdown completed")
