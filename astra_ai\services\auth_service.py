"""
Authentication Service for Enhanced Nova AI Server
Handles user authentication, authorization, and session management
"""

import logging
import time
import jwt
import bcrypt
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import secrets
import hashlib

class AuthService:
    """
    Authentication and authorization service
    """
    
    def __init__(self, config):
        """Initialize authentication service"""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Security configuration
        security_config = config.get('security', {})
        self.secret_key = security_config.get('secret_key', secrets.token_hex(32))
        self.jwt_expiry = security_config.get('jwt_expiry', 3600)
        self.bcrypt_rounds = security_config.get('bcrypt_rounds', 12)
        
        # Session storage (in production, use Redis or database)
        self.active_sessions = {}
        self.api_keys = {}
        
        # Rate limiting storage
        self.rate_limit_storage = {}
        
        # Initialize default admin user if configured
        self._initialize_default_users()
        
        self.logger.info("Authentication Service initialized")
    
    def _initialize_default_users(self):
        """Initialize default users from configuration"""
        default_users = self.config.get('auth.default_users', [])
        
        for user_config in default_users:
            username = user_config.get('username')
            password = user_config.get('password')
            roles = user_config.get('roles', ['user'])
            
            if username and password:
                self.create_user(username, password, roles)
                self.logger.info(f"Created default user: {username}")
    
    def create_user(self, username: str, password: str, roles: List[str] = None) -> bool:
        """Create a new user"""
        try:
            if roles is None:
                roles = ['user']
            
            # Hash password
            password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt(rounds=self.bcrypt_rounds))
            
            # Store user (in production, use database)
            user_data = {
                'username': username,
                'password_hash': password_hash.decode('utf-8'),
                'roles': roles,
                'created_at': datetime.utcnow().isoformat(),
                'active': True
            }
            
            # For now, store in memory (replace with database in production)
            if not hasattr(self, 'users'):
                self.users = {}
            
            self.users[username] = user_data
            
            self.logger.info(f"User created: {username}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating user {username}: {e}")
            return False
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """Authenticate user with username and password"""
        try:
            if not hasattr(self, 'users') or username not in self.users:
                return None
            
            user_data = self.users[username]
            
            # Check if user is active
            if not user_data.get('active', True):
                return None
            
            # Verify password
            stored_hash = user_data['password_hash'].encode('utf-8')
            if bcrypt.checkpw(password.encode('utf-8'), stored_hash):
                # Return user info without password hash
                return {
                    'username': user_data['username'],
                    'roles': user_data['roles'],
                    'created_at': user_data['created_at']
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error authenticating user {username}: {e}")
            return None
    
    def generate_jwt_token(self, user_data: Dict[str, Any]) -> str:
        """Generate JWT token for authenticated user"""
        try:
            payload = {
                'username': user_data['username'],
                'roles': user_data['roles'],
                'iat': datetime.utcnow(),
                'exp': datetime.utcnow() + timedelta(seconds=self.jwt_expiry)
            }
            
            token = jwt.encode(payload, self.secret_key, algorithm='HS256')
            
            # Store session
            session_id = secrets.token_hex(16)
            self.active_sessions[session_id] = {
                'user_data': user_data,
                'token': token,
                'created_at': time.time(),
                'last_activity': time.time()
            }
            
            return token
            
        except Exception as e:
            self.logger.error(f"Error generating JWT token: {e}")
            return None
    
    def verify_jwt_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify JWT token and return user data"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            
            # Check if session exists
            for session_id, session_data in self.active_sessions.items():
                if session_data['token'] == token:
                    # Update last activity
                    session_data['last_activity'] = time.time()
                    return payload
            
            return None
            
        except jwt.ExpiredSignatureError:
            self.logger.warning("JWT token expired")
            return None
        except jwt.InvalidTokenError:
            self.logger.warning("Invalid JWT token")
            return None
        except Exception as e:
            self.logger.error(f"Error verifying JWT token: {e}")
            return None
    
    def generate_api_key(self, username: str, description: str = "") -> Optional[str]:
        """Generate API key for a user"""
        try:
            api_key = f"nova_ai_{secrets.token_urlsafe(32)}"
            
            self.api_keys[api_key] = {
                'username': username,
                'description': description,
                'created_at': datetime.utcnow().isoformat(),
                'last_used': None,
                'active': True
            }
            
            self.logger.info(f"API key generated for user: {username}")
            return api_key
            
        except Exception as e:
            self.logger.error(f"Error generating API key for {username}: {e}")
            return None
    
    def verify_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """Verify API key and return associated user data"""
        try:
            if api_key not in self.api_keys:
                return None
            
            key_data = self.api_keys[api_key]
            
            # Check if key is active
            if not key_data.get('active', True):
                return None
            
            # Update last used timestamp
            key_data['last_used'] = datetime.utcnow().isoformat()
            
            # Get user data
            username = key_data['username']
            if hasattr(self, 'users') and username in self.users:
                user_data = self.users[username]
                return {
                    'username': user_data['username'],
                    'roles': user_data['roles'],
                    'auth_method': 'api_key'
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error verifying API key: {e}")
            return None
    
    def check_permission(self, user_data: Dict[str, Any], required_role: str) -> bool:
        """Check if user has required permission"""
        try:
            user_roles = user_data.get('roles', [])
            
            # Admin role has all permissions
            if 'admin' in user_roles:
                return True
            
            # Check specific role
            return required_role in user_roles
            
        except Exception as e:
            self.logger.error(f"Error checking permission: {e}")
            return False
    
    def logout_user(self, token: str) -> bool:
        """Logout user by invalidating token"""
        try:
            # Find and remove session
            for session_id, session_data in list(self.active_sessions.items()):
                if session_data['token'] == token:
                    del self.active_sessions[session_id]
                    self.logger.info("User logged out successfully")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error logging out user: {e}")
            return False
    
    def revoke_api_key(self, api_key: str) -> bool:
        """Revoke an API key"""
        try:
            if api_key in self.api_keys:
                self.api_keys[api_key]['active'] = False
                self.logger.info("API key revoked")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error revoking API key: {e}")
            return False
    
    def cleanup_expired_sessions(self):
        """Clean up expired sessions"""
        try:
            current_time = time.time()
            expired_sessions = []
            
            for session_id, session_data in self.active_sessions.items():
                # Check if session is expired
                if current_time - session_data['last_activity'] > self.jwt_expiry:
                    expired_sessions.append(session_id)
            
            # Remove expired sessions
            for session_id in expired_sessions:
                del self.active_sessions[session_id]
            
            if expired_sessions:
                self.logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up expired sessions: {e}")
    
    def get_user_sessions(self, username: str) -> List[Dict[str, Any]]:
        """Get active sessions for a user"""
        try:
            user_sessions = []
            
            for session_id, session_data in self.active_sessions.items():
                if session_data['user_data']['username'] == username:
                    user_sessions.append({
                        'session_id': session_id,
                        'created_at': session_data['created_at'],
                        'last_activity': session_data['last_activity']
                    })
            
            return user_sessions
            
        except Exception as e:
            self.logger.error(f"Error getting user sessions: {e}")
            return []
    
    def get_user_api_keys(self, username: str) -> List[Dict[str, Any]]:
        """Get API keys for a user"""
        try:
            user_keys = []
            
            for api_key, key_data in self.api_keys.items():
                if key_data['username'] == username:
                    user_keys.append({
                        'api_key': api_key[:12] + '...',  # Masked key
                        'description': key_data['description'],
                        'created_at': key_data['created_at'],
                        'last_used': key_data['last_used'],
                        'active': key_data['active']
                    })
            
            return user_keys
            
        except Exception as e:
            self.logger.error(f"Error getting user API keys: {e}")
            return []
    
    def get_auth_stats(self) -> Dict[str, Any]:
        """Get authentication statistics"""
        try:
            return {
                'active_sessions': len(self.active_sessions),
                'total_users': len(getattr(self, 'users', {})),
                'total_api_keys': len(self.api_keys),
                'active_api_keys': len([k for k in self.api_keys.values() if k.get('active', True)])
            }
            
        except Exception as e:
            self.logger.error(f"Error getting auth stats: {e}")
            return {}
    
    def health_check(self) -> Dict[str, Any]:
        """Perform authentication service health check"""
        try:
            # Test JWT operations
            test_user = {'username': 'test', 'roles': ['user']}
            test_token = self.generate_jwt_token(test_user)
            
            if not test_token:
                return {'status': 'unhealthy', 'error': 'Failed to generate JWT token'}
            
            verified_user = self.verify_jwt_token(test_token)
            if not verified_user:
                return {'status': 'unhealthy', 'error': 'Failed to verify JWT token'}
            
            # Clean up test session
            self.logout_user(test_token)
            
            return {
                'status': 'healthy',
                'stats': self.get_auth_stats()
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }
    
    def shutdown(self):
        """Shutdown authentication service"""
        try:
            # Clear all sessions
            self.active_sessions.clear()
            
            # In production, save persistent data to database
            self.logger.info("Authentication Service shutdown completed")
            
        except Exception as e:
            self.logger.error(f"Error during auth service shutdown: {e}")
