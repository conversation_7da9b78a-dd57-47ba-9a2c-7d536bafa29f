"""
Cache Service for Enhanced Nova AI Server
Handles caching for improved performance and reduced API calls
"""

import logging
import time
import json
import threading
from typing import Any, Optional, Dict, List
from datetime import datetime, timedelta
import hashlib
import pickle

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

class MemoryCache:
    """In-memory cache implementation"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache = {}
        self.access_times = {}
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        with self.lock:
            if key not in self.cache:
                return None
            
            value, expiry = self.cache[key]
            
            # Check if expired
            if expiry and time.time() > expiry:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
                return None
            
            # Update access time
            self.access_times[key] = time.time()
            return value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache"""
        with self.lock:
            # Calculate expiry
            expiry = None
            if ttl is not None:
                expiry = time.time() + ttl
            elif self.default_ttl > 0:
                expiry = time.time() + self.default_ttl
            
            # Evict if cache is full
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict_lru()
            
            self.cache[key] = (value, expiry)
            self.access_times[key] = time.time()
            return True
    
    def delete(self, key: str) -> bool:
        """Delete value from cache"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
                return True
            return False
    
    def clear(self):
        """Clear all cache entries"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
    
    def _evict_lru(self):
        """Evict least recently used item"""
        if not self.access_times:
            return
        
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        del self.cache[lru_key]
        del self.access_times[lru_key]
    
    def cleanup_expired(self):
        """Remove expired entries"""
        with self.lock:
            current_time = time.time()
            expired_keys = []
            
            for key, (value, expiry) in self.cache.items():
                if expiry and current_time > expiry:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self.lock:
            return {
                'type': 'memory',
                'size': len(self.cache),
                'max_size': self.max_size,
                'default_ttl': self.default_ttl
            }

class RedisCache:
    """Redis cache implementation"""
    
    def __init__(self, host: str = 'localhost', port: int = 6379, db: int = 0, 
                 password: Optional[str] = None, default_ttl: int = 3600):
        self.default_ttl = default_ttl
        self.redis_client = redis.Redis(
            host=host,
            port=port,
            db=db,
            password=password,
            decode_responses=False  # We'll handle encoding ourselves
        )
        
        # Test connection
        self.redis_client.ping()
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from Redis cache"""
        try:
            data = self.redis_client.get(key)
            if data is None:
                return None
            
            # Deserialize
            return pickle.loads(data)
        except Exception:
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in Redis cache"""
        try:
            # Serialize
            data = pickle.dumps(value)
            
            # Set with TTL
            if ttl is not None:
                return self.redis_client.setex(key, ttl, data)
            elif self.default_ttl > 0:
                return self.redis_client.setex(key, self.default_ttl, data)
            else:
                return self.redis_client.set(key, data)
        except Exception:
            return False
    
    def delete(self, key: str) -> bool:
        """Delete value from Redis cache"""
        try:
            return bool(self.redis_client.delete(key))
        except Exception:
            return False
    
    def clear(self):
        """Clear all cache entries"""
        try:
            self.redis_client.flushdb()
        except Exception:
            pass
    
    def cleanup_expired(self):
        """Redis handles expiration automatically"""
        pass
    
    def get_stats(self) -> Dict[str, Any]:
        """Get Redis cache statistics"""
        try:
            info = self.redis_client.info()
            return {
                'type': 'redis',
                'used_memory': info.get('used_memory', 0),
                'connected_clients': info.get('connected_clients', 0),
                'total_commands_processed': info.get('total_commands_processed', 0),
                'default_ttl': self.default_ttl
            }
        except Exception:
            return {'type': 'redis', 'error': 'Unable to get stats'}

class CacheService:
    """
    Cache service for managing different cache backends
    """
    
    def __init__(self, config):
        """Initialize cache service"""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Cache configuration
        cache_config = config.get('cache', {})
        self.cache_type = cache_config.get('type', 'memory')
        self.default_ttl = cache_config.get('ttl', 3600)
        
        # Initialize cache backend
        self.cache = None
        self._initialize_cache(cache_config)
        
        # Statistics
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'errors': 0
        }
        
        self.logger.info(f"Cache Service initialized with {self.cache_type} backend")
    
    def _initialize_cache(self, cache_config: Dict):
        """Initialize cache backend"""
        try:
            if self.cache_type == 'redis' and REDIS_AVAILABLE:
                self.cache = RedisCache(
                    host=cache_config.get('host', 'localhost'),
                    port=cache_config.get('port', 6379),
                    db=cache_config.get('db', 0),
                    password=cache_config.get('password'),
                    default_ttl=self.default_ttl
                )
                self.logger.info("Redis cache initialized")
            else:
                if self.cache_type == 'redis':
                    self.logger.warning("Redis not available, falling back to memory cache")
                
                self.cache = MemoryCache(
                    max_size=cache_config.get('max_size', 1000),
                    default_ttl=self.default_ttl
                )
                self.logger.info("Memory cache initialized")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize {self.cache_type} cache: {e}")
            # Fallback to memory cache
            self.cache = MemoryCache(
                max_size=cache_config.get('max_size', 1000),
                default_ttl=self.default_ttl
            )
            self.logger.info("Fallback to memory cache")
    
    def get(self, key: str, namespace: str = 'default') -> Optional[Any]:
        """Get value from cache"""
        try:
            cache_key = self._build_key(key, namespace)
            value = self.cache.get(cache_key)
            
            if value is not None:
                self.stats['hits'] += 1
                self.logger.debug(f"Cache hit for key: {cache_key}")
            else:
                self.stats['misses'] += 1
                self.logger.debug(f"Cache miss for key: {cache_key}")
            
            return value
            
        except Exception as e:
            self.stats['errors'] += 1
            self.logger.error(f"Cache get error for key {key}: {e}")
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None, namespace: str = 'default') -> bool:
        """Set value in cache"""
        try:
            cache_key = self._build_key(key, namespace)
            success = self.cache.set(cache_key, value, ttl)
            
            if success:
                self.stats['sets'] += 1
                self.logger.debug(f"Cache set for key: {cache_key}")
            
            return success
            
        except Exception as e:
            self.stats['errors'] += 1
            self.logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    def delete(self, key: str, namespace: str = 'default') -> bool:
        """Delete value from cache"""
        try:
            cache_key = self._build_key(key, namespace)
            success = self.cache.delete(cache_key)
            
            if success:
                self.stats['deletes'] += 1
                self.logger.debug(f"Cache delete for key: {cache_key}")
            
            return success
            
        except Exception as e:
            self.stats['errors'] += 1
            self.logger.error(f"Cache delete error for key {key}: {e}")
            return False
    
    def clear(self, namespace: Optional[str] = None):
        """Clear cache entries"""
        try:
            if namespace is None:
                # Clear all
                self.cache.clear()
                self.logger.info("Cache cleared completely")
            else:
                # Clear specific namespace (not implemented for all backends)
                self.logger.warning(f"Namespace-specific clear not implemented for {self.cache_type}")
                
        except Exception as e:
            self.stats['errors'] += 1
            self.logger.error(f"Cache clear error: {e}")
    
    def cleanup_expired(self):
        """Clean up expired cache entries"""
        try:
            self.cache.cleanup_expired()
            self.logger.debug("Cache cleanup completed")
        except Exception as e:
            self.logger.error(f"Cache cleanup error: {e}")
    
    def _build_key(self, key: str, namespace: str) -> str:
        """Build cache key with namespace"""
        return f"{namespace}:{key}"
    
    def generate_key(self, *args, **kwargs) -> str:
        """Generate cache key from arguments"""
        # Create a deterministic key from arguments
        key_data = {
            'args': args,
            'kwargs': sorted(kwargs.items()) if kwargs else {}
        }
        key_string = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def cached(self, key: str = None, ttl: Optional[int] = None, namespace: str = 'default'):
        """Decorator for caching function results"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                # Generate cache key
                if key:
                    cache_key = key
                else:
                    cache_key = f"{func.__name__}:{self.generate_key(*args, **kwargs)}"
                
                # Try to get from cache
                cached_result = self.get(cache_key, namespace)
                if cached_result is not None:
                    return cached_result
                
                # Execute function and cache result
                result = func(*args, **kwargs)
                self.set(cache_key, result, ttl, namespace)
                
                return result
            
            return wrapper
        return decorator
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache service statistics"""
        stats = self.stats.copy()
        
        # Add cache backend stats
        try:
            backend_stats = self.cache.get_stats()
            stats.update(backend_stats)
        except Exception as e:
            stats['backend_error'] = str(e)
        
        # Calculate hit rate
        total_requests = stats['hits'] + stats['misses']
        stats['hit_rate'] = stats['hits'] / max(total_requests, 1)
        stats['total_requests'] = total_requests
        
        return stats
    
    def health_check(self) -> Dict[str, Any]:
        """Perform cache health check"""
        try:
            # Test basic operations
            test_key = 'health_check_test'
            test_value = {'timestamp': time.time(), 'test': True}
            
            # Test set
            set_success = self.set(test_key, test_value, ttl=60)
            if not set_success:
                return {'status': 'unhealthy', 'error': 'Failed to set test value'}
            
            # Test get
            retrieved_value = self.get(test_key)
            if retrieved_value != test_value:
                return {'status': 'unhealthy', 'error': 'Failed to retrieve test value'}
            
            # Test delete
            delete_success = self.delete(test_key)
            if not delete_success:
                return {'status': 'unhealthy', 'error': 'Failed to delete test value'}
            
            return {
                'status': 'healthy',
                'backend': self.cache_type,
                'stats': self.get_stats()
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'backend': self.cache_type
            }
    
    def shutdown(self):
        """Shutdown cache service"""
        try:
            if hasattr(self.cache, 'redis_client'):
                self.cache.redis_client.close()
            self.logger.info("Cache Service shutdown completed")
        except Exception as e:
            self.logger.error(f"Error during cache shutdown: {e}")
