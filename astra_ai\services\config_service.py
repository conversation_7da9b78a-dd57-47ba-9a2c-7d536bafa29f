"""
Configuration Service for Enhanced Nova AI Server
Handles all configuration management, environment variables, and settings
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union
import logging
from dataclasses import dataclass, asdict
from datetime import datetime

@dataclass
class ServerConfig:
    """Server configuration dataclass"""
    host: str = "127.0.0.1"
    api_port: int = 8081
    ui_port: int = 8080
    debug: bool = False
    workers: int = 4
    max_connections: int = 1000

@dataclass
class DatabaseConfig:
    """Database configuration dataclass"""
    type: str = "sqlite"
    host: str = "localhost"
    port: int = 5432
    name: str = "nova_ai"
    username: str = ""
    password: str = ""
    pool_size: int = 10
    max_overflow: int = 20

@dataclass
class CacheConfig:
    """Cache configuration dataclass"""
    type: str = "memory"
    host: str = "localhost"
    port: int = 6379
    db: int = 0
    password: str = ""
    ttl: int = 3600
    max_size: int = 1000

@dataclass
class SecurityConfig:
    """Security configuration dataclass"""
    secret_key: str = ""
    jwt_expiry: int = 3600
    bcrypt_rounds: int = 12
    rate_limit_enabled: bool = True
    cors_origins: list = None
    ssl_enabled: bool = False

class ConfigService:
    """
    Configuration service for managing all application settings
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize configuration service"""
        self.logger = logging.getLogger(__name__)
        self.config_path = config_path or self._find_config_file()
        self.config = {}
        self.env_prefix = "NOVA_AI_"
        
        # Default configurations
        self.defaults = {
            'server': asdict(ServerConfig()),
            'database': asdict(DatabaseConfig()),
            'cache': asdict(CacheConfig()),
            'security': asdict(SecurityConfig()),
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
                'file': 'logs/nova_ai.log',
                'max_size': '10MB',
                'backup_count': 5
            },
            'ai': {
                'providers': {
                    'openai': {
                        'enabled': False,
                        'api_key': '',
                        'model': 'gpt-4',
                        'max_tokens': 4000
                    },
                    'anthropic': {
                        'enabled': False,
                        'api_key': '',
                        'model': 'claude-3-sonnet-20240229',
                        'max_tokens': 4000
                    },
                    'google': {
                        'enabled': True,
                        'api_key': '',
                        'model': 'gemini-pro',
                        'max_tokens': 4000
                    },
                    'groq': {
                        'enabled': True,
                        'api_key': '',
                        'model': 'mixtral-8x7b-32768',
                        'max_tokens': 4000
                    }
                },
                'load_balancing': {
                    'enabled': True,
                    'strategy': 'round_robin',
                    'health_check_interval': 60
                },
                'caching': {
                    'enabled': True,
                    'ttl': 3600,
                    'max_size': 1000
                }
            },
            'ui': {
                'enable_webview': True,
                'window': {
                    'width': 1400,
                    'height': 900,
                    'resizable': True
                },
                'theme': 'dark',
                'auto_refresh': True
            },
            'features': {
                'file_upload': True,
                'websockets': True,
                'background_tasks': True,
                'monitoring': True,
                'analytics': True
            },
            'rate_limits': {
                'default': ["200 per day", "50 per hour"],
                'chat': ["100 per hour"],
                'file_upload': ["10 per hour"],
                'admin': ["50 per day"]
            },
            'cors': {
                'origins': ["http://localhost:*", "http://127.0.0.1:*"],
                'methods': ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
                'allow_headers': ["Content-Type", "Authorization"]
            },
            'flask': {
                'TESTING': False,
                'JSON_SORT_KEYS': False,
                'MAX_CONTENT_LENGTH': 16 * 1024 * 1024  # 16MB
            }
        }
        
        self._load_config()
        self._load_environment_variables()
        self._validate_config()
        
        self.logger.info("Configuration service initialized")
    
    def _find_config_file(self) -> Optional[str]:
        """Find configuration file in standard locations"""
        possible_paths = [
            'config.yaml',
            'config.yml',
            'config.json',
            'astra_ai/config.yaml',
            'astra_ai/config.yml',
            'astra_ai/config.json',
            os.path.expanduser('~/.nova_ai/config.yaml'),
            '/etc/nova_ai/config.yaml'
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        return None
    
    def _load_config(self):
        """Load configuration from file"""
        self.config = self.defaults.copy()
        
        if not self.config_path:
            self.logger.info("No config file found, using defaults")
            return
        
        try:
            with open(self.config_path, 'r') as f:
                if self.config_path.endswith(('.yaml', '.yml')):
                    file_config = yaml.safe_load(f)
                else:
                    file_config = json.load(f)
            
            # Deep merge configuration
            self._deep_merge(self.config, file_config)
            self.logger.info(f"Configuration loaded from {self.config_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to load config from {self.config_path}: {e}")
    
    def _load_environment_variables(self):
        """Load configuration from environment variables"""
        env_mappings = {
            f'{self.env_prefix}DEBUG': 'server.debug',
            f'{self.env_prefix}HOST': 'server.host',
            f'{self.env_prefix}API_PORT': 'server.api_port',
            f'{self.env_prefix}UI_PORT': 'server.ui_port',
            f'{self.env_prefix}DATABASE_URL': 'database.url',
            f'{self.env_prefix}REDIS_URL': 'cache.url',
            f'{self.env_prefix}SECRET_KEY': 'security.secret_key',
            f'{self.env_prefix}OPENAI_API_KEY': 'ai.providers.openai.api_key',
            f'{self.env_prefix}ANTHROPIC_API_KEY': 'ai.providers.anthropic.api_key',
            f'{self.env_prefix}GOOGLE_API_KEY': 'ai.providers.google.api_key',
            f'{self.env_prefix}GROQ_API_KEY': 'ai.providers.groq.api_key',
        }
        
        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value:
                self._set_nested_value(self.config, config_path, self._convert_env_value(value))
        
        # Also check for GROQ_API_KEY (legacy)
        groq_key = os.getenv('GROQ_API_KEY')
        if groq_key:
            self._set_nested_value(self.config, 'ai.providers.groq.api_key', groq_key)
    
    def _convert_env_value(self, value: str) -> Union[str, int, bool]:
        """Convert environment variable string to appropriate type"""
        # Boolean conversion
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # Integer conversion
        try:
            return int(value)
        except ValueError:
            pass
        
        # Return as string
        return value
    
    def _deep_merge(self, base: Dict, update: Dict):
        """Deep merge two dictionaries"""
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._deep_merge(base[key], value)
            else:
                base[key] = value
    
    def _set_nested_value(self, config: Dict, path: str, value: Any):
        """Set a nested configuration value using dot notation"""
        keys = path.split('.')
        current = config
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value
    
    def _validate_config(self):
        """Validate configuration values"""
        # Validate required API keys
        ai_providers = self.config.get('ai', {}).get('providers', {})
        enabled_providers = [name for name, config in ai_providers.items() if config.get('enabled')]
        
        if not enabled_providers:
            self.logger.warning("No AI providers enabled")
        
        for provider in enabled_providers:
            if not ai_providers[provider].get('api_key'):
                self.logger.warning(f"No API key configured for enabled provider: {provider}")
        
        # Validate ports
        api_port = self.config.get('server', {}).get('api_port')
        ui_port = self.config.get('server', {}).get('ui_port')
        
        if api_port == ui_port:
            self.logger.error("API and UI ports cannot be the same")
            raise ValueError("Port conflict: API and UI ports must be different")
        
        # Generate secret key if not provided
        if not self.config.get('security', {}).get('secret_key'):
            import secrets
            self.config['security']['secret_key'] = secrets.token_hex(32)
            self.logger.info("Generated new secret key")
    
    def get(self, path: str, default: Any = None) -> Any:
        """Get configuration value using dot notation"""
        keys = path.split('.')
        current = self.config
        
        try:
            for key in keys:
                current = current[key]
            return current
        except (KeyError, TypeError):
            return default
    
    def set(self, path: str, value: Any):
        """Set configuration value using dot notation"""
        self._set_nested_value(self.config, path, value)
    
    def save(self, path: Optional[str] = None):
        """Save current configuration to file"""
        save_path = path or self.config_path or 'config.yaml'
        
        try:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            with open(save_path, 'w') as f:
                if save_path.endswith(('.yaml', '.yml')):
                    yaml.dump(self.config, f, default_flow_style=False, indent=2)
                else:
                    json.dump(self.config, f, indent=2)
            
            self.logger.info(f"Configuration saved to {save_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")
            raise
    
    def reload(self):
        """Reload configuration from file and environment"""
        self._load_config()
        self._load_environment_variables()
        self._validate_config()
        self.logger.info("Configuration reloaded")
    
    def get_all(self) -> Dict[str, Any]:
        """Get all configuration as dictionary"""
        return self.config.copy()
    
    def get_masked(self) -> Dict[str, Any]:
        """Get configuration with sensitive values masked"""
        config = self.config.copy()
        
        # Mask sensitive values
        sensitive_keys = ['password', 'secret', 'key', 'token']
        
        def mask_sensitive(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    if any(sensitive in key.lower() for sensitive in sensitive_keys):
                        obj[key] = "***MASKED***" if value else ""
                    else:
                        mask_sensitive(value, current_path)
        
        mask_sensitive(config)
        return config
