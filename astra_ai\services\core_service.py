"""
Core Service for Enhanced Nova AI Server
Handles core AI functionality and integration with existing Nova AI
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import json
import time

# Add parent directory to path for Nova AI imports
sys.path.append(str(Path(__file__).parent.parent))

class CoreService:
    """
    Core service that integrates with the existing Nova AI system
    """
    
    def __init__(self, config):
        """Initialize core service"""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Nova AI instance
        self.nova_ai = None
        
        # Session management
        self.chat_histories = {}
        self.user_locations = {}
        self.active_sessions = {}
        
        # Performance tracking
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_response_time': 0.0,
            'total_response_time': 0.0
        }
        
        self._initialize_nova_ai()
        self.logger.info("Core Service initialized")
    
    def _initialize_nova_ai(self):
        """Initialize the Nova AI chatbot"""
        try:
            from core.nova_ai import AleChatBot
            self.nova_ai = AleChatBot()
            self.logger.info("Nova AI chatbot initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize Nova AI: {e}")
            raise
    
    async def process_chat_message(self, 
                                 message: str, 
                                 session_id: str = 'default',
                                 user_location: str = '',
                                 context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Process a chat message through Nova AI"""
        start_time = time.time()
        self.stats['total_requests'] += 1
        
        try:
            # Validate inputs
            if not message:
                raise ValueError("No message provided")
            
            if not self.nova_ai:
                raise RuntimeError("Nova AI not initialized")
            
            # Get or create session chat history
            if session_id not in self.chat_histories:
                self.chat_histories[session_id] = []
            
            # Handle user location
            if user_location:
                self.user_locations[session_id] = user_location
                self.logger.debug(f"Saved location for session {session_id}: {user_location}")
            elif session_id in self.user_locations:
                user_location = self.user_locations[session_id]
                self.logger.debug(f"Using saved location for session {session_id}: {user_location}")
            else:
                # Default location
                user_location = "Italy"
                self.user_locations[session_id] = user_location
                self.logger.debug(f"Setting default location to Italy for session {session_id}")
            
            # Build context for Nova AI
            messages = self._build_message_context(session_id, message, user_location, context)
            
            # Get response from Nova AI
            response = await self._get_nova_ai_response(messages)
            
            # Update session history
            if response:
                self.chat_histories[session_id].extend([
                    {"role": "user", "content": message},
                    {"role": "assistant", "content": response}
                ])
                
                # Keep session history at reasonable size
                if len(self.chat_histories[session_id]) > 20:
                    self.chat_histories[session_id] = self.chat_histories[session_id][-20:]
                
                # Store conversation
                try:
                    self.nova_ai.files.store_conversation(message, response)
                except Exception as e:
                    self.logger.warning(f"Failed to store conversation: {e}")
            
            # Update statistics
            response_time = time.time() - start_time
            self.stats['successful_requests'] += 1
            self.stats['total_response_time'] += response_time
            self.stats['avg_response_time'] = self.stats['total_response_time'] / self.stats['total_requests']
            
            return {
                'response': response,
                'session_id': session_id,
                'response_time': response_time,
                'location': user_location,
                'success': True
            }
            
        except Exception as e:
            response_time = time.time() - start_time
            self.stats['failed_requests'] += 1
            self.logger.error(f"Error processing chat message: {e}")
            
            return {
                'error': str(e),
                'session_id': session_id,
                'response_time': response_time,
                'success': False
            }
    
    def _build_message_context(self, session_id: str, message: str, user_location: str, context: Dict[str, Any] = None) -> List[Dict]:
        """Build message context for Nova AI"""
        messages = self.nova_ai.chat_history + self.chat_histories[session_id]
        
        # Add system message about capabilities
        messages.append({
            "role": "system",
            "content": "You are running in a desktop UI with visual capabilities. When users ask for time, you have REAL-TIME access to current time information. Always provide actual current time, never say you don't have real-time access. The UI will automatically show visual widgets when you provide relevant information."
        })
        
        # Add location context
        if user_location:
            messages.append({
                "role": "system",
                "content": f"User's current location: {user_location}. ONLY mention time information when the user specifically asks for time. Do not automatically add time information to responses unless explicitly requested. Use this location for location-based queries when relevant."
            })
        
        # Add additional context if provided
        if context:
            if context.get('memories'):
                messages.append({
                    "role": "system",
                    "content": f"Context from past conversations:\n{context['memories']}\n\nUse this context naturally in your response if relevant to the current question."
                })
            
            if context.get('widget_context'):
                messages.append({
                    "role": "system",
                    "content": f"Widget context: {context['widget_context']}"
                })
        
        # Add the current user message
        messages.append({"role": "user", "content": message})
        
        return messages
    
    async def _get_nova_ai_response(self, messages: List[Dict]) -> str:
        """Get response from Nova AI"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            response = loop.run_until_complete(
                self.nova_ai.get_response(messages, stream_to_terminal=False)
            )
            return response
        finally:
            loop.close()
    
    def get_session_history(self, session_id: str) -> List[Dict]:
        """Get chat history for a session"""
        return self.chat_histories.get(session_id, [])
    
    def clear_session_history(self, session_id: str) -> bool:
        """Clear chat history for a session"""
        if session_id in self.chat_histories:
            del self.chat_histories[session_id]
            return True
        return False
    
    def get_user_location(self, session_id: str) -> Optional[str]:
        """Get user location for a session"""
        return self.user_locations.get(session_id)
    
    def set_user_location(self, session_id: str, location: str):
        """Set user location for a session"""
        self.user_locations[session_id] = location
        self.logger.debug(f"Set location for session {session_id}: {location}")
    
    def get_active_sessions(self) -> List[str]:
        """Get list of active session IDs"""
        return list(self.chat_histories.keys())
    
    def get_session_stats(self, session_id: str) -> Dict[str, Any]:
        """Get statistics for a specific session"""
        history = self.chat_histories.get(session_id, [])
        return {
            'session_id': session_id,
            'message_count': len(history),
            'location': self.user_locations.get(session_id),
            'last_activity': self._get_last_activity(session_id)
        }
    
    def _get_last_activity(self, session_id: str) -> Optional[float]:
        """Get timestamp of last activity for a session"""
        # This would be implemented with proper timestamp tracking
        return time.time() if session_id in self.chat_histories else None
    
    async def search_memories(self, query: str, limit: int = 5) -> List[Dict]:
        """Search Nova AI memories"""
        try:
            if not self.nova_ai.memory_enabled or not self.nova_ai.memory:
                return []
            
            memories = self.nova_ai.memory.retrieve_memories(query, limit=limit)
            return memories
        except Exception as e:
            self.logger.error(f"Error searching memories: {e}")
            return []
    
    def get_memory_status(self) -> Dict[str, Any]:
        """Get memory system status"""
        try:
            if not self.nova_ai.memory_enabled or not self.nova_ai.memory:
                return {
                    'enabled': False,
                    'status': 'Memory system disabled'
                }
            
            stats = self.nova_ai.memory.get_memory_stats()
            return {
                'enabled': True,
                'status': 'Memory system active',
                'stats': stats
            }
        except Exception as e:
            return {
                'enabled': False,
                'status': f'Memory system error: {str(e)}'
            }
    
    async def get_time_for_location(self, location: str) -> str:
        """Get time for a specific location"""
        try:
            from core.nova_ai import get_time_in_location
            time_str = get_time_in_location(location)
            
            # Extract just the time
            import re
            match = re.search(r'is (\d{1,2}:\d{2})', time_str)
            if match:
                return match.group(1)
            else:
                return '--:--'
        except Exception as e:
            self.logger.error(f"Error getting time for location {location}: {e}")
            return '--:--'
    
    def reload_nova_ai(self):
        """Reload Nova AI module"""
        try:
            self.logger.info("Reloading Nova AI module...")
            
            # Clear module cache
            modules_to_reload = []
            for module_name in list(sys.modules.keys()):
                if 'nova_ai' in module_name or 'core' in module_name:
                    modules_to_reload.append(module_name)
            
            for module_name in modules_to_reload:
                if module_name in sys.modules:
                    del sys.modules[module_name]
            
            # Reimport and reinitialize
            from core.nova_ai import AleChatBot
            self.nova_ai = AleChatBot()
            self.logger.info("Nova AI reloaded successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to reload Nova AI: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get service statistics"""
        return {
            'total_requests': self.stats['total_requests'],
            'successful_requests': self.stats['successful_requests'],
            'failed_requests': self.stats['failed_requests'],
            'success_rate': self.stats['successful_requests'] / max(self.stats['total_requests'], 1),
            'avg_response_time': self.stats['avg_response_time'],
            'active_sessions': len(self.chat_histories),
            'nova_ai_initialized': self.nova_ai is not None,
            'memory_enabled': self.nova_ai.memory_enabled if self.nova_ai else False
        }
    
    def cleanup_old_sessions(self, max_age_hours: int = 24):
        """Clean up old inactive sessions"""
        # This would be implemented with proper timestamp tracking
        # For now, just limit the number of sessions
        if len(self.chat_histories) > 100:
            # Keep only the 50 most recent sessions
            sessions_to_keep = list(self.chat_histories.keys())[-50:]
            new_histories = {k: v for k, v in self.chat_histories.items() if k in sessions_to_keep}
            self.chat_histories = new_histories
            
            # Also clean up locations
            new_locations = {k: v for k, v in self.user_locations.items() if k in sessions_to_keep}
            self.user_locations = new_locations
            
            self.logger.info(f"Cleaned up old sessions, keeping {len(sessions_to_keep)} sessions")
    
    def shutdown(self):
        """Shutdown core service"""
        self.logger.info("Core Service shutdown initiated")
        
        # Save any pending data
        try:
            # This could save session data to persistent storage
            pass
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
        
        self.logger.info("Core Service shutdown completed")
