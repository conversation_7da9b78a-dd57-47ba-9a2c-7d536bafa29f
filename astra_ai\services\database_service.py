"""
Database Service for Enhanced Nova AI Server
Handles database connections, models, and data persistence
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
import json
import sqlite3
import threading
from pathlib import Path
from dataclasses import dataclass, asdict
from contextlib import contextmanager

try:
    import sqlalchemy as sa
    from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String, Text, DateTime, Boolean, Float
    from sqlalchemy.orm import sessionmaker, declarative_base
    from sqlalchemy.pool import StaticPool
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False

@dataclass
class ChatSession:
    """Chat session data model"""
    session_id: str
    user_location: str = ""
    created_at: datetime = None
    last_activity: datetime = None
    message_count: int = 0
    metadata: Dict[str, Any] = None

@dataclass
class ChatMessage:
    """Chat message data model"""
    id: int = None
    session_id: str = ""
    role: str = ""
    content: str = ""
    timestamp: datetime = None
    tokens_used: int = 0
    response_time: float = 0.0
    metadata: Dict[str, Any] = None

@dataclass
class AIRequest:
    """AI request data model"""
    id: int = None
    session_id: str = ""
    provider: str = ""
    model: str = ""
    request_data: Dict[str, Any] = None
    response_data: Dict[str, Any] = None
    tokens_used: int = 0
    response_time: float = 0.0
    success: bool = True
    error_message: str = ""
    timestamp: datetime = None

class DatabaseService:
    """
    Database service for persistent data storage
    """
    
    def __init__(self, config):
        """Initialize database service"""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Database configuration
        self.db_config = config.get('database', {})
        self.db_type = self.db_config.get('type', 'sqlite')
        
        # Connection and session management
        self.engine = None
        self.SessionLocal = None
        self.metadata = None
        self.tables = {}
        
        # Thread safety
        self.lock = threading.Lock()
        
        # Initialize database
        self._initialize_database()
        self.logger.info("Database Service initialized")
    
    def _initialize_database(self):
        """Initialize database connection and tables"""
        try:
            if self.db_type == 'sqlite':
                self._init_sqlite()
            elif self.db_type == 'postgresql':
                self._init_postgresql()
            elif self.db_type == 'mysql':
                self._init_mysql()
            else:
                raise ValueError(f"Unsupported database type: {self.db_type}")
            
            self._create_tables()
            self.logger.info(f"Database initialized successfully ({self.db_type})")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize database: {e}")
            # Fallback to simple file-based storage
            self._init_file_storage()
    
    def _init_sqlite(self):
        """Initialize SQLite database"""
        db_path = self.db_config.get('path', 'data/nova_ai.db')
        
        # Create directory if it doesn't exist
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        
        if SQLALCHEMY_AVAILABLE:
            # Use SQLAlchemy
            self.engine = create_engine(
                f'sqlite:///{db_path}',
                poolclass=StaticPool,
                connect_args={'check_same_thread': False},
                echo=self.config.get('debug', False)
            )
            self.SessionLocal = sessionmaker(bind=self.engine)
            self.metadata = MetaData()
        else:
            # Use raw SQLite
            self.db_path = db_path
            self._init_sqlite_raw()
    
    def _init_sqlite_raw(self):
        """Initialize raw SQLite connection"""
        self.logger.info("Using raw SQLite (SQLAlchemy not available)")
        
        # Create tables
        with self._get_sqlite_connection() as conn:
            cursor = conn.cursor()
            
            # Chat sessions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS chat_sessions (
                    session_id TEXT PRIMARY KEY,
                    user_location TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    message_count INTEGER DEFAULT 0,
                    metadata TEXT
                )
            ''')
            
            # Chat messages table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS chat_messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT,
                    role TEXT,
                    content TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    tokens_used INTEGER DEFAULT 0,
                    response_time REAL DEFAULT 0.0,
                    metadata TEXT,
                    FOREIGN KEY (session_id) REFERENCES chat_sessions (session_id)
                )
            ''')
            
            # AI requests table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ai_requests (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT,
                    provider TEXT,
                    model TEXT,
                    request_data TEXT,
                    response_data TEXT,
                    tokens_used INTEGER DEFAULT 0,
                    response_time REAL DEFAULT 0.0,
                    success BOOLEAN DEFAULT 1,
                    error_message TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Performance metrics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_name TEXT,
                    metric_value REAL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    metadata TEXT
                )
            ''')
            
            conn.commit()
    
    def _init_postgresql(self):
        """Initialize PostgreSQL database"""
        if not SQLALCHEMY_AVAILABLE:
            raise RuntimeError("SQLAlchemy required for PostgreSQL support")
        
        host = self.db_config.get('host', 'localhost')
        port = self.db_config.get('port', 5432)
        database = self.db_config.get('name', 'nova_ai')
        username = self.db_config.get('username', '')
        password = self.db_config.get('password', '')
        
        connection_string = f'postgresql://{username}:{password}@{host}:{port}/{database}'
        
        self.engine = create_engine(
            connection_string,
            pool_size=self.db_config.get('pool_size', 10),
            max_overflow=self.db_config.get('max_overflow', 20),
            echo=self.config.get('debug', False)
        )
        self.SessionLocal = sessionmaker(bind=self.engine)
        self.metadata = MetaData()
    
    def _init_mysql(self):
        """Initialize MySQL database"""
        if not SQLALCHEMY_AVAILABLE:
            raise RuntimeError("SQLAlchemy required for MySQL support")
        
        host = self.db_config.get('host', 'localhost')
        port = self.db_config.get('port', 3306)
        database = self.db_config.get('name', 'nova_ai')
        username = self.db_config.get('username', '')
        password = self.db_config.get('password', '')
        
        connection_string = f'mysql+pymysql://{username}:{password}@{host}:{port}/{database}'
        
        self.engine = create_engine(
            connection_string,
            pool_size=self.db_config.get('pool_size', 10),
            max_overflow=self.db_config.get('max_overflow', 20),
            echo=self.config.get('debug', False)
        )
        self.SessionLocal = sessionmaker(bind=self.engine)
        self.metadata = MetaData()
    
    def _init_file_storage(self):
        """Initialize simple file-based storage as fallback"""
        self.logger.warning("Using file-based storage fallback")
        self.storage_dir = Path('data/file_storage')
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        self.file_storage = True
    
    def _create_tables(self):
        """Create database tables using SQLAlchemy"""
        if not SQLALCHEMY_AVAILABLE or not self.engine:
            return
        
        # Chat sessions table
        self.tables['chat_sessions'] = Table(
            'chat_sessions', self.metadata,
            Column('session_id', String(255), primary_key=True),
            Column('user_location', String(255)),
            Column('created_at', DateTime, default=datetime.utcnow),
            Column('last_activity', DateTime, default=datetime.utcnow),
            Column('message_count', Integer, default=0),
            Column('metadata', Text)
        )
        
        # Chat messages table
        self.tables['chat_messages'] = Table(
            'chat_messages', self.metadata,
            Column('id', Integer, primary_key=True, autoincrement=True),
            Column('session_id', String(255)),
            Column('role', String(50)),
            Column('content', Text),
            Column('timestamp', DateTime, default=datetime.utcnow),
            Column('tokens_used', Integer, default=0),
            Column('response_time', Float, default=0.0),
            Column('metadata', Text)
        )
        
        # AI requests table
        self.tables['ai_requests'] = Table(
            'ai_requests', self.metadata,
            Column('id', Integer, primary_key=True, autoincrement=True),
            Column('session_id', String(255)),
            Column('provider', String(100)),
            Column('model', String(100)),
            Column('request_data', Text),
            Column('response_data', Text),
            Column('tokens_used', Integer, default=0),
            Column('response_time', Float, default=0.0),
            Column('success', Boolean, default=True),
            Column('error_message', Text),
            Column('timestamp', DateTime, default=datetime.utcnow)
        )
        
        # Performance metrics table
        self.tables['performance_metrics'] = Table(
            'performance_metrics', self.metadata,
            Column('id', Integer, primary_key=True, autoincrement=True),
            Column('metric_name', String(100)),
            Column('metric_value', Float),
            Column('timestamp', DateTime, default=datetime.utcnow),
            Column('metadata', Text)
        )
        
        # Create all tables
        self.metadata.create_all(self.engine)
    
    @contextmanager
    def _get_sqlite_connection(self):
        """Get SQLite connection context manager"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    @contextmanager
    def get_session(self):
        """Get database session context manager"""
        if hasattr(self, 'file_storage'):
            yield None  # File storage doesn't use sessions
            return
        
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def save_chat_session(self, session_data: ChatSession) -> bool:
        """Save chat session to database"""
        try:
            if hasattr(self, 'file_storage'):
                return self._save_chat_session_file(session_data)
            
            if SQLALCHEMY_AVAILABLE and self.engine:
                return self._save_chat_session_sqlalchemy(session_data)
            else:
                return self._save_chat_session_sqlite(session_data)
        except Exception as e:
            self.logger.error(f"Error saving chat session: {e}")
            return False
    
    def _save_chat_session_sqlite(self, session_data: ChatSession) -> bool:
        """Save chat session using raw SQLite"""
        with self._get_sqlite_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO chat_sessions 
                (session_id, user_location, created_at, last_activity, message_count, metadata)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                session_data.session_id,
                session_data.user_location,
                session_data.created_at or datetime.utcnow(),
                session_data.last_activity or datetime.utcnow(),
                session_data.message_count,
                json.dumps(session_data.metadata) if session_data.metadata else None
            ))
            conn.commit()
        return True
    
    def _save_chat_session_file(self, session_data: ChatSession) -> bool:
        """Save chat session to file"""
        file_path = self.storage_dir / f"session_{session_data.session_id}.json"
        with open(file_path, 'w') as f:
            json.dump(asdict(session_data), f, default=str, indent=2)
        return True
    
    def save_chat_message(self, message_data: ChatMessage) -> bool:
        """Save chat message to database"""
        try:
            if hasattr(self, 'file_storage'):
                return self._save_chat_message_file(message_data)
            
            if SQLALCHEMY_AVAILABLE and self.engine:
                return self._save_chat_message_sqlalchemy(message_data)
            else:
                return self._save_chat_message_sqlite(message_data)
        except Exception as e:
            self.logger.error(f"Error saving chat message: {e}")
            return False
    
    def _save_chat_message_sqlite(self, message_data: ChatMessage) -> bool:
        """Save chat message using raw SQLite"""
        with self._get_sqlite_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO chat_messages 
                (session_id, role, content, timestamp, tokens_used, response_time, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                message_data.session_id,
                message_data.role,
                message_data.content,
                message_data.timestamp or datetime.utcnow(),
                message_data.tokens_used,
                message_data.response_time,
                json.dumps(message_data.metadata) if message_data.metadata else None
            ))
            conn.commit()
        return True
    
    def get_chat_history(self, session_id: str, limit: int = 50) -> List[ChatMessage]:
        """Get chat history for a session"""
        try:
            if hasattr(self, 'file_storage'):
                return self._get_chat_history_file(session_id, limit)
            
            if SQLALCHEMY_AVAILABLE and self.engine:
                return self._get_chat_history_sqlalchemy(session_id, limit)
            else:
                return self._get_chat_history_sqlite(session_id, limit)
        except Exception as e:
            self.logger.error(f"Error getting chat history: {e}")
            return []
    
    def _get_chat_history_sqlite(self, session_id: str, limit: int) -> List[ChatMessage]:
        """Get chat history using raw SQLite"""
        messages = []
        with self._get_sqlite_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM chat_messages 
                WHERE session_id = ? 
                ORDER BY timestamp DESC 
                LIMIT ?
            ''', (session_id, limit))
            
            for row in cursor.fetchall():
                metadata = json.loads(row['metadata']) if row['metadata'] else {}
                messages.append(ChatMessage(
                    id=row['id'],
                    session_id=row['session_id'],
                    role=row['role'],
                    content=row['content'],
                    timestamp=datetime.fromisoformat(row['timestamp']),
                    tokens_used=row['tokens_used'],
                    response_time=row['response_time'],
                    metadata=metadata
                ))
        
        return list(reversed(messages))  # Return in chronological order
    
    def save_ai_request(self, request_data: AIRequest) -> bool:
        """Save AI request data"""
        try:
            if hasattr(self, 'file_storage'):
                return self._save_ai_request_file(request_data)
            
            if SQLALCHEMY_AVAILABLE and self.engine:
                return self._save_ai_request_sqlalchemy(request_data)
            else:
                return self._save_ai_request_sqlite(request_data)
        except Exception as e:
            self.logger.error(f"Error saving AI request: {e}")
            return False
    
    def _save_ai_request_sqlite(self, request_data: AIRequest) -> bool:
        """Save AI request using raw SQLite"""
        with self._get_sqlite_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO ai_requests 
                (session_id, provider, model, request_data, response_data, tokens_used, 
                 response_time, success, error_message, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                request_data.session_id,
                request_data.provider,
                request_data.model,
                json.dumps(request_data.request_data) if request_data.request_data else None,
                json.dumps(request_data.response_data) if request_data.response_data else None,
                request_data.tokens_used,
                request_data.response_time,
                request_data.success,
                request_data.error_message,
                request_data.timestamp or datetime.utcnow()
            ))
            conn.commit()
        return True
    
    def get_performance_metrics(self, metric_name: str = None, hours: int = 24) -> List[Dict]:
        """Get performance metrics"""
        try:
            if hasattr(self, 'file_storage'):
                return []  # Not implemented for file storage
            
            since = datetime.utcnow() - timedelta(hours=hours)
            
            with self._get_sqlite_connection() as conn:
                cursor = conn.cursor()
                if metric_name:
                    cursor.execute('''
                        SELECT * FROM performance_metrics 
                        WHERE metric_name = ? AND timestamp >= ?
                        ORDER BY timestamp DESC
                    ''', (metric_name, since))
                else:
                    cursor.execute('''
                        SELECT * FROM performance_metrics 
                        WHERE timestamp >= ?
                        ORDER BY timestamp DESC
                    ''', (since,))
                
                metrics = []
                for row in cursor.fetchall():
                    metrics.append({
                        'id': row['id'],
                        'metric_name': row['metric_name'],
                        'metric_value': row['metric_value'],
                        'timestamp': row['timestamp'],
                        'metadata': json.loads(row['metadata']) if row['metadata'] else {}
                    })
                
                return metrics
        except Exception as e:
            self.logger.error(f"Error getting performance metrics: {e}")
            return []
    
    def cleanup_old_data(self, days: int = 30):
        """Clean up old data"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            if hasattr(self, 'file_storage'):
                # Clean up old files
                for file_path in self.storage_dir.glob("*.json"):
                    if file_path.stat().st_mtime < cutoff_date.timestamp():
                        file_path.unlink()
                return
            
            with self._get_sqlite_connection() as conn:
                cursor = conn.cursor()
                
                # Clean up old messages
                cursor.execute('DELETE FROM chat_messages WHERE timestamp < ?', (cutoff_date,))
                
                # Clean up old AI requests
                cursor.execute('DELETE FROM ai_requests WHERE timestamp < ?', (cutoff_date,))
                
                # Clean up old metrics
                cursor.execute('DELETE FROM performance_metrics WHERE timestamp < ?', (cutoff_date,))
                
                conn.commit()
                
            self.logger.info(f"Cleaned up data older than {days} days")
            
        except Exception as e:
            self.logger.error(f"Error cleaning up old data: {e}")
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        try:
            if hasattr(self, 'file_storage'):
                return {'type': 'file_storage', 'files': len(list(self.storage_dir.glob("*.json")))}
            
            with self._get_sqlite_connection() as conn:
                cursor = conn.cursor()
                
                stats = {'type': self.db_type}
                
                # Count records in each table
                tables = ['chat_sessions', 'chat_messages', 'ai_requests', 'performance_metrics']
                for table in tables:
                    cursor.execute(f'SELECT COUNT(*) FROM {table}')
                    stats[f'{table}_count'] = cursor.fetchone()[0]
                
                return stats
        except Exception as e:
            self.logger.error(f"Error getting database stats: {e}")
            return {'error': str(e)}
    
    def shutdown(self):
        """Shutdown database service"""
        try:
            if self.engine:
                self.engine.dispose()
            self.logger.info("Database Service shutdown completed")
        except Exception as e:
            self.logger.error(f"Error during database shutdown: {e}")
