"""
File Service for Enhanced Nova AI Server
Handles file uploads, processing, and management
"""

import logging
import os
import hashlib
import mimetypes
from pathlib import Path
from typing import Dict, Any, List, Optional, BinaryIO
import time
import json
import shutil

class FileService:
    """
    File service for handling file operations
    """
    
    def __init__(self, config):
        """Initialize file service"""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # File configuration
        file_config = config.get('file_upload', {})
        self.upload_folder = file_config.get('upload_folder', 'uploads')
        self.max_file_size = file_config.get('max_file_size', 16 * 1024 * 1024)  # 16MB
        self.allowed_extensions = set(file_config.get('allowed_extensions', [
            '.txt', '.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png', '.gif', '.json'
        ]))
        
        # Create upload directory
        Path(self.upload_folder).mkdir(parents=True, exist_ok=True)
        
        # File metadata storage
        self.file_metadata = {}
        self.metadata_file = os.path.join(self.upload_folder, 'metadata.json')
        self._load_metadata()
        
        self.logger.info("File Service initialized")
    
    def _load_metadata(self):
        """Load file metadata from storage"""
        try:
            if os.path.exists(self.metadata_file):
                with open(self.metadata_file, 'r') as f:
                    self.file_metadata = json.load(f)
        except Exception as e:
            self.logger.error(f"Error loading file metadata: {e}")
            self.file_metadata = {}
    
    def _save_metadata(self):
        """Save file metadata to storage"""
        try:
            with open(self.metadata_file, 'w') as f:
                json.dump(self.file_metadata, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving file metadata: {e}")
    
    def validate_file(self, filename: str, file_size: int) -> Dict[str, Any]:
        """Validate uploaded file"""
        try:
            # Check file size
            if file_size > self.max_file_size:
                return {
                    'valid': False,
                    'error': f'File size {file_size} exceeds maximum {self.max_file_size} bytes'
                }
            
            # Check file extension
            file_ext = Path(filename).suffix.lower()
            if file_ext not in self.allowed_extensions:
                return {
                    'valid': False,
                    'error': f'File extension {file_ext} not allowed. Allowed: {", ".join(self.allowed_extensions)}'
                }
            
            # Check filename for security
            if '..' in filename or '/' in filename or '\\' in filename:
                return {
                    'valid': False,
                    'error': 'Invalid filename - path traversal detected'
                }
            
            return {'valid': True}
            
        except Exception as e:
            self.logger.error(f"Error validating file: {e}")
            return {'valid': False, 'error': str(e)}
    
    def save_file(self, file_data: BinaryIO, filename: str, user_id: str = 'anonymous') -> Dict[str, Any]:
        """Save uploaded file"""
        try:
            # Generate unique filename
            timestamp = int(time.time())
            file_ext = Path(filename).suffix.lower()
            safe_filename = self._sanitize_filename(Path(filename).stem)
            unique_filename = f"{timestamp}_{safe_filename}{file_ext}"
            
            file_path = os.path.join(self.upload_folder, unique_filename)
            
            # Calculate file hash while saving
            hasher = hashlib.sha256()
            file_size = 0
            
            with open(file_path, 'wb') as f:
                while True:
                    chunk = file_data.read(8192)
                    if not chunk:
                        break
                    hasher.update(chunk)
                    f.write(chunk)
                    file_size += len(chunk)
            
            file_hash = hasher.hexdigest()
            
            # Detect MIME type
            mime_type, _ = mimetypes.guess_type(filename)
            
            # Store metadata
            file_id = f"file_{timestamp}_{file_hash[:8]}"
            metadata = {
                'file_id': file_id,
                'original_filename': filename,
                'stored_filename': unique_filename,
                'file_path': file_path,
                'file_size': file_size,
                'file_hash': file_hash,
                'mime_type': mime_type,
                'uploaded_by': user_id,
                'upload_time': timestamp,
                'processed': False
            }
            
            self.file_metadata[file_id] = metadata
            self._save_metadata()
            
            self.logger.info(f"File saved: {filename} -> {unique_filename} (ID: {file_id})")
            
            return {
                'success': True,
                'file_id': file_id,
                'filename': unique_filename,
                'size': file_size,
                'hash': file_hash
            }
            
        except Exception as e:
            self.logger.error(f"Error saving file: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_file(self, file_id: str) -> Optional[Dict[str, Any]]:
        """Get file metadata"""
        return self.file_metadata.get(file_id)
    
    def get_file_path(self, file_id: str) -> Optional[str]:
        """Get file path"""
        metadata = self.get_file(file_id)
        if metadata and os.path.exists(metadata['file_path']):
            return metadata['file_path']
        return None
    
    def delete_file(self, file_id: str) -> bool:
        """Delete file"""
        try:
            metadata = self.get_file(file_id)
            if not metadata:
                return False
            
            file_path = metadata['file_path']
            if os.path.exists(file_path):
                os.remove(file_path)
            
            del self.file_metadata[file_id]
            self._save_metadata()
            
            self.logger.info(f"File deleted: {file_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting file {file_id}: {e}")
            return False
    
    def list_files(self, user_id: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """List files"""
        try:
            files = []
            for file_id, metadata in self.file_metadata.items():
                if user_id and metadata.get('uploaded_by') != user_id:
                    continue
                
                # Remove sensitive information
                file_info = {
                    'file_id': file_id,
                    'original_filename': metadata['original_filename'],
                    'file_size': metadata['file_size'],
                    'mime_type': metadata['mime_type'],
                    'upload_time': metadata['upload_time'],
                    'processed': metadata.get('processed', False)
                }
                files.append(file_info)
            
            # Sort by upload time (newest first)
            files.sort(key=lambda x: x['upload_time'], reverse=True)
            
            return files[:limit]
            
        except Exception as e:
            self.logger.error(f"Error listing files: {e}")
            return []
    
    def process_file(self, file_id: str) -> Dict[str, Any]:
        """Process uploaded file (extract text, analyze, etc.)"""
        try:
            metadata = self.get_file(file_id)
            if not metadata:
                return {'success': False, 'error': 'File not found'}
            
            file_path = metadata['file_path']
            if not os.path.exists(file_path):
                return {'success': False, 'error': 'File does not exist'}
            
            mime_type = metadata['mime_type']
            processing_result = {}
            
            # Process based on file type
            if mime_type and mime_type.startswith('text/'):
                processing_result = self._process_text_file(file_path)
            elif mime_type and mime_type.startswith('image/'):
                processing_result = self._process_image_file(file_path)
            elif mime_type == 'application/json':
                processing_result = self._process_json_file(file_path)
            else:
                processing_result = {'type': 'unknown', 'content': 'File type not supported for processing'}
            
            # Update metadata
            metadata['processed'] = True
            metadata['processing_result'] = processing_result
            self._save_metadata()
            
            self.logger.info(f"File processed: {file_id}")
            
            return {
                'success': True,
                'file_id': file_id,
                'result': processing_result
            }
            
        except Exception as e:
            self.logger.error(f"Error processing file {file_id}: {e}")
            return {'success': False, 'error': str(e)}
    
    def _process_text_file(self, file_path: str) -> Dict[str, Any]:
        """Process text file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return {
                'type': 'text',
                'content': content,
                'length': len(content),
                'lines': len(content.splitlines())
            }
        except Exception as e:
            return {'type': 'text', 'error': str(e)}
    
    def _process_image_file(self, file_path: str) -> Dict[str, Any]:
        """Process image file"""
        try:
            # Basic image information
            file_size = os.path.getsize(file_path)
            
            result = {
                'type': 'image',
                'file_size': file_size
            }
            
            # Try to get image dimensions if PIL is available
            try:
                from PIL import Image
                with Image.open(file_path) as img:
                    result['width'] = img.width
                    result['height'] = img.height
                    result['format'] = img.format
                    result['mode'] = img.mode
            except ImportError:
                result['note'] = 'PIL not available for detailed image analysis'
            except Exception as e:
                result['error'] = f'Error analyzing image: {str(e)}'
            
            return result
            
        except Exception as e:
            return {'type': 'image', 'error': str(e)}
    
    def _process_json_file(self, file_path: str) -> Dict[str, Any]:
        """Process JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return {
                'type': 'json',
                'valid': True,
                'keys': list(data.keys()) if isinstance(data, dict) else None,
                'length': len(data) if isinstance(data, (list, dict)) else None
            }
        except json.JSONDecodeError as e:
            return {'type': 'json', 'valid': False, 'error': str(e)}
        except Exception as e:
            return {'type': 'json', 'error': str(e)}
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe storage"""
        # Remove or replace dangerous characters
        safe_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_."
        sanitized = ''.join(c if c in safe_chars else '_' for c in filename)
        
        # Limit length
        if len(sanitized) > 100:
            sanitized = sanitized[:100]
        
        return sanitized
    
    def cleanup_old_files(self, max_age_days: int = 30):
        """Clean up old files"""
        try:
            current_time = time.time()
            max_age_seconds = max_age_days * 24 * 60 * 60
            
            files_to_delete = []
            for file_id, metadata in self.file_metadata.items():
                if current_time - metadata['upload_time'] > max_age_seconds:
                    files_to_delete.append(file_id)
            
            for file_id in files_to_delete:
                self.delete_file(file_id)
            
            if files_to_delete:
                self.logger.info(f"Cleaned up {len(files_to_delete)} old files")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up old files: {e}")
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics"""
        try:
            total_files = len(self.file_metadata)
            total_size = sum(metadata['file_size'] for metadata in self.file_metadata.values())
            
            # Get disk usage
            upload_path = Path(self.upload_folder)
            disk_usage = shutil.disk_usage(upload_path)
            
            return {
                'total_files': total_files,
                'total_size': total_size,
                'disk_free': disk_usage.free,
                'disk_total': disk_usage.total,
                'disk_used': disk_usage.used,
                'upload_folder': str(upload_path.absolute())
            }
            
        except Exception as e:
            self.logger.error(f"Error getting storage stats: {e}")
            return {}
    
    def health_check(self) -> Dict[str, Any]:
        """Perform file service health check"""
        try:
            # Check upload directory
            upload_path = Path(self.upload_folder)
            if not upload_path.exists():
                return {'status': 'unhealthy', 'error': 'Upload directory does not exist'}
            
            if not os.access(upload_path, os.W_OK):
                return {'status': 'unhealthy', 'error': 'Upload directory not writable'}
            
            # Check disk space
            disk_usage = shutil.disk_usage(upload_path)
            free_space_mb = disk_usage.free / (1024 * 1024)
            
            if free_space_mb < 100:  # Less than 100MB free
                return {'status': 'degraded', 'warning': f'Low disk space: {free_space_mb:.1f}MB free'}
            
            return {
                'status': 'healthy',
                'upload_folder': str(upload_path.absolute()),
                'free_space_mb': free_space_mb,
                'total_files': len(self.file_metadata)
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }
    
    def shutdown(self):
        """Shutdown file service"""
        try:
            self._save_metadata()
            self.logger.info("File Service shutdown completed")
        except Exception as e:
            self.logger.error(f"Error during file service shutdown: {e}")
