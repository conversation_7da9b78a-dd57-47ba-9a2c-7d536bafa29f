"""
Google Gemini 1.5 Vision API integration for screen analysis.
Provides screen capture analysis using Google's Gemini 1.5 Pro Vision model.
"""

import os
import base64
import logging
from typing import Optional, Dict, Any, List
from io import BytesIO
from PIL import Image

# Configure logging
logger = logging.getLogger(__name__)

try:
    import google.generativeai as genai
    from google.generativeai.types import HarmCategory, HarmBlockThreshold
    GEMINI_AVAILABLE = True
except ImportError:
    logger.warning("google-generativeai package not installed. Gemini Vision features will be unavailable.")
    GEMINI_AVAILABLE = False

class GeminiVisionAnalyzer:
    """
    Google Gemini 1.5 Vision API integration for analyzing screen captures.
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Gemini Vision analyzer.
        
        Args:
            api_key: Google AI Studio API key. If None, will try to get from environment.
        """
        if not GEMINI_AVAILABLE:
            raise ImportError("google-generativeai package is required. Install with: pip install google-generativeai")
        
        self.api_key = api_key or os.getenv('GOOGLE_AI_API_KEY') or os.getenv('GEMINI_API_KEY') or "AIzaSyBe7dZj5mN_40Yo9t2xIeNgEcKdJWSe42w"

        if not self.api_key:
            raise ValueError(
                "Google AI API key not found. Please set GOOGLE_AI_API_KEY or GEMINI_API_KEY "
                "environment variable, or pass api_key parameter."
            )
        
        # Configure the API
        genai.configure(api_key=self.api_key)
        
        # Initialize the model - try different models based on availability
        try:
            # Try gemini-1.5-flash first (lower quota usage)
            self.model = genai.GenerativeModel('gemini-1.5-flash')
            self.model_name = 'gemini-1.5-flash'
            logger.info("✅ Using Gemini 1.5 Flash model")
        except Exception:
            try:
                # Fallback to gemini-pro-vision
                self.model = genai.GenerativeModel('gemini-pro-vision')
                self.model_name = 'gemini-pro-vision'
                logger.info("✅ Using Gemini Pro Vision model")
            except Exception:
                # Final fallback to gemini-1.5-pro
                self.model = genai.GenerativeModel('gemini-1.5-pro')
                self.model_name = 'gemini-1.5-pro'
                logger.info("✅ Using Gemini 1.5 Pro model")
        
        # Safety settings for content filtering
        self.safety_settings = {
            HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
        }
        
        logger.info("✅ Gemini Vision analyzer initialized successfully")
    
    def analyze_screen_image(self, image_data: str, prompt: str = None) -> Dict[str, Any]:
        """
        Analyze a screen capture image using Gemini 1.5 Vision.
        
        Args:
            image_data: Base64 encoded image data
            prompt: Custom prompt for analysis. If None, uses default screen analysis prompt.
            
        Returns:
            Dict containing analysis results and metadata
        """
        try:
            # Default prompt for screen analysis
            if prompt is None:
                prompt = """
                Analyze this screen capture in detail. Please provide:
                
                1. **Main Content**: What is the primary content or application visible?
                2. **UI Elements**: Describe key interface elements, buttons, menus, etc.
                3. **Text Content**: Summarize any visible text, documents, or messages
                4. **Activity**: What appears to be happening or what task is being performed?
                5. **Context**: What type of work or activity does this screen suggest?
                6. **Notable Details**: Any interesting or important details worth mentioning
                
                Be thorough but concise. Focus on what would be most helpful for someone asking "What's on my screen?"
                """
            
            # Decode base64 image
            try:
                # Remove data URL prefix if present
                if image_data.startswith('data:image'):
                    image_data = image_data.split(',')[1]
                
                image_bytes = base64.b64decode(image_data)
                image = Image.open(BytesIO(image_bytes))
                
                # Convert to RGB if necessary
                if image.mode != 'RGB':
                    image = image.convert('RGB')
                
                logger.info(f"📸 Processing image: {image.size[0]}x{image.size[1]} pixels")
                
            except Exception as e:
                logger.error(f"❌ Failed to decode image: {e}")
                return {
                    'success': False,
                    'error': f'Failed to decode image: {str(e)}',
                    'analysis': None
                }
            
            # Generate content with Gemini with retry logic
            try:
                # Optimize generation config for lower quota usage
                generation_config = genai.types.GenerationConfig(
                    temperature=0.3,  # Lower temperature for more focused responses
                    top_p=0.8,
                    top_k=20,  # Reduced for efficiency
                    max_output_tokens=500,  # Reduced to save quota
                )

                response = self.model.generate_content(
                    [prompt, image],
                    safety_settings=self.safety_settings,
                    generation_config=generation_config
                )
                
                # Check if response was blocked
                if response.prompt_feedback.block_reason:
                    logger.warning(f"⚠️ Response blocked: {response.prompt_feedback.block_reason}")
                    return {
                        'success': False,
                        'error': f'Content blocked: {response.prompt_feedback.block_reason}',
                        'analysis': None
                    }
                
                analysis_text = response.text
                logger.info("✅ Screen analysis completed successfully")
                
                return {
                    'success': True,
                    'analysis': analysis_text,
                    'image_size': f"{image.size[0]}x{image.size[1]}",
                    'model': 'gemini-1.5-pro',
                    'prompt_used': prompt[:100] + "..." if len(prompt) > 100 else prompt
                }
                
            except Exception as e:
                error_str = str(e)
                logger.error(f"❌ Gemini API error: {e}")

                # Handle specific quota errors
                if "429" in error_str or "quota" in error_str.lower():
                    return {
                        'success': False,
                        'error': 'API quota exceeded. Please wait a few minutes and try again, or consider upgrading your API plan.',
                        'analysis': None,
                        'quota_exceeded': True
                    }
                elif "403" in error_str:
                    return {
                        'success': False,
                        'error': 'API access denied. Please check your API key permissions.',
                        'analysis': None
                    }
                else:
                    return {
                        'success': False,
                        'error': f'Gemini API error: {str(e)}',
                        'analysis': None
                    }
                
        except Exception as e:
            logger.error(f"❌ Unexpected error in screen analysis: {e}")
            return {
                'success': False,
                'error': f'Unexpected error: {str(e)}',
                'analysis': None
            }
    
    def analyze_with_custom_question(self, image_data: str, question: str) -> Dict[str, Any]:
        """
        Analyze screen with a specific user question.
        
        Args:
            image_data: Base64 encoded image data
            question: Specific question about the screen content
            
        Returns:
            Dict containing analysis results
        """
        custom_prompt = f"""
        Looking at this screen capture, please answer this specific question:
        
        "{question}"
        
        Provide a detailed and helpful response based on what you can see in the image.
        If the question cannot be answered from the visible content, explain what you can see instead.
        """
        
        return self.analyze_screen_image(image_data, custom_prompt)
    
    def get_quick_summary(self, image_data: str) -> Dict[str, Any]:
        """
        Get a quick, concise summary of the screen content.
        
        Args:
            image_data: Base64 encoded image data
            
        Returns:
            Dict containing quick summary
        """
        quick_prompt = """
        Provide a brief, one-paragraph summary of what's currently visible on this screen.
        Focus on the most important elements and what the user appears to be doing.
        Keep it concise but informative.
        """
        
        return self.analyze_screen_image(image_data, quick_prompt)

    def analyze_camera_image(self, image_data: str, prompt: str = None) -> Dict[str, Any]:
        """
        Analyze a camera image using Gemini Vision.

        Args:
            image_data: Base64 encoded image data from camera
            prompt: Custom prompt for analysis. If None, uses default camera analysis prompt.

        Returns:
            Dict containing analysis results and metadata
        """
        if prompt is None:
            prompt = """
            You are an AI assistant with vision capabilities. A user is asking you what you can see through their camera.

            Respond in natural first-person language as if you're personally looking at the scene. Always start with:
            - "I'm seeing..."
            - "I can see..."
            - "I'm looking at..."
            - "Right now I see..."

            Use natural, conversational language like you're describing what you see to a friend. Focus on:
            - People and what they're doing
            - Objects and their context
            - The setting/environment
            - Interesting details

            Examples of good responses:
            - "I'm seeing a person holding an earbud"
            - "I can see someone sitting at a desk with a computer"
            - "I'm looking at a black person in a bedroom"
            - "Right now I see a young man making a hand gesture"

            Keep responses concise, natural, and conversational. Avoid technical language.
            """

        return self.analyze_screen_image(image_data, prompt)

    def identify_objects_in_camera(self, image_data: str) -> Dict[str, Any]:
        """
        Identify specific objects in a camera image.

        Args:
            image_data: Base64 encoded image data from camera

        Returns:
            Dict containing object identification results
        """
        object_prompt = """
        Identify and list all the objects you can see in this camera image. For each object, provide:
        1. The object name
        2. Its approximate location in the image (left, right, center, top, bottom)
        3. Any notable characteristics or details
        4. Your confidence level in the identification

        Format as a clear, numbered list. Be specific and detailed.
        """

        return self.analyze_screen_image(image_data, object_prompt)

    def read_text_in_camera(self, image_data: str) -> Dict[str, Any]:
        """
        Extract and read text from a camera image.

        Args:
            image_data: Base64 encoded image data from camera

        Returns:
            Dict containing text extraction results
        """
        text_prompt = """
        Extract and transcribe all visible text in this camera image. Include:
        1. All readable text, signs, labels, or writing
        2. The approximate location of each text element
        3. Any formatting or styling information
        4. Language identification if not English

        If no text is visible, clearly state that. Be thorough and accurate.
        """

        return self.analyze_screen_image(image_data, text_prompt)

    def test_connection(self) -> bool:
        """
        Test the Gemini API connection.
        
        Returns:
            True if connection is successful, False otherwise
        """
        try:
            # Create a simple test image (1x1 white pixel)
            test_image = Image.new('RGB', (1, 1), color='white')
            
            response = self.model.generate_content(
                ["What color is this image?", test_image],
                safety_settings=self.safety_settings
            )
            
            logger.info("✅ Gemini API connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"❌ Gemini API connection test failed: {e}")
            return False

# Global instance for easy access
_gemini_analyzer = None

def get_gemini_analyzer() -> GeminiVisionAnalyzer:
    """
    Get or create the global Gemini analyzer instance.
    
    Returns:
        GeminiVisionAnalyzer instance
    """
    global _gemini_analyzer
    if _gemini_analyzer is None:
        _gemini_analyzer = GeminiVisionAnalyzer()
    return _gemini_analyzer

def analyze_screen_capture(image_data: str, question: str = None) -> Dict[str, Any]:
    """
    Convenience function to analyze a screen capture.

    Args:
        image_data: Base64 encoded image data
        question: Optional specific question about the screen

    Returns:
        Dict containing analysis results
    """
    if not GEMINI_AVAILABLE:
        return {
            'success': False,
            'error': 'Gemini Vision API not available. Please install google-generativeai package.',
            'analysis': None
        }

    try:
        analyzer = get_gemini_analyzer()

        if question:
            return analyzer.analyze_with_custom_question(image_data, question)
        else:
            return analyzer.analyze_screen_image(image_data)
    except Exception as e:
        return {
            'success': False,
            'error': f'Failed to analyze screen: {str(e)}',
            'analysis': None
        }

def analyze_camera_capture(image_data: str, analysis_type: str = "scene", question: str = None) -> Dict[str, Any]:
    """
    Convenience function to analyze a camera capture.

    Args:
        image_data: Base64 encoded image data from camera
        analysis_type: Type of analysis - "scene", "objects", "text", or "custom"
        question: Optional specific question for custom analysis

    Returns:
        Dict containing analysis results
    """
    if not GEMINI_AVAILABLE:
        return {
            'success': False,
            'error': 'Gemini Vision API not available. Please install google-generativeai package.',
            'analysis': None
        }

    try:
        analyzer = get_gemini_analyzer()

        if analysis_type == "objects":
            return analyzer.identify_objects_in_camera(image_data)
        elif analysis_type == "text":
            return analyzer.read_text_in_camera(image_data)
        elif analysis_type == "custom" and question:
            return analyzer.analyze_with_custom_question(image_data, question)
        else:
            # Default to scene analysis
            return analyzer.analyze_camera_image(image_data)
    except Exception as e:
        return {
            'success': False,
            'error': f'Failed to analyze camera image: {str(e)}',
            'analysis': None
        }
