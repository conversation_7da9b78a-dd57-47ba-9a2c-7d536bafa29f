#!/usr/bin/env python3
"""
GINI 1.5 Camera Bridge for Nova AI Integration
==============================================

This service bridges GINI 1.5's camera analysis capabilities with the Nova AI camera widget,
providing real-time vision analysis and AI-to-AI communication.

Features:
- Real-time camera frame analysis using GINI 1.5
- WebSocket communication with Nova AI camera widget
- Automatic analysis triggers and chat integration
- Visual indicators for active AI analysis
- Seamless user experience with live feed + AI insights
"""

import asyncio
import base64
import json
import logging
import os
import sys
import time
import threading
import websockets
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Callable
from io import BytesIO

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

try:
    import cv2
    import numpy as np
    CAMERA_AVAILABLE = True
except ImportError:
    CAMERA_AVAILABLE = False

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    import google.generativeai as genai
    from google.generativeai.types import HarmCategory, HarmBlockThreshold
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GiniCameraBridge:
    """Bridge service connecting GINI 1.5 camera analysis with Nova AI"""
    
    def __init__(self):
        """Initialize the camera bridge service"""
        self.camera = None
        self.camera_active = False
        self.analysis_active = False
        self.websocket_server = None
        self.connected_clients = set()
        self.last_frame = None
        self.analysis_interval = 3.0  # Analyze every 3 seconds
        self.last_analysis_time = 0
        self.analysis_queue = asyncio.Queue()
        
        # GINI 1.5 integration
        self.online_mode = False
        self.model = None
        self.model_name = None
        
        # Initialize Gemini AI
        self._setup_gemini()
        
        # Demo responses for offline mode
        self.demo_responses = [
            "I can see a person in front of the camera. They appear to be in an indoor environment with good lighting.",
            "The camera shows a workspace or room setting. I can observe various objects and a person interacting with the environment.",
            "I'm looking at what appears to be a desktop or work area. There are some items visible and the lighting suggests an indoor setting.",
            "The camera feed shows a person in what looks like a home or office environment. The scene appears calm and well-lit."
        ]
        self.response_index = 0
        
    def _setup_gemini(self):
        """Set up Gemini AI for vision analysis"""
        if not GEMINI_AVAILABLE:
            logger.warning("Gemini not available - using offline mode")
            return False
        
        try:
            # Use the API key from GINI 1.5
            api_key = 'AIzaSyBe7dZj5mN_40Yo9t2xIeNgEcKdJWSe42w'
            genai.configure(api_key=api_key)
            
            # Try different models
            models_to_try = ['gemini-1.5-flash', 'gemini-pro-vision', 'gemini-1.5-pro']
            
            for model_name in models_to_try:
                try:
                    self.model = genai.GenerativeModel(model_name)
                    self.model_name = model_name
                    
                    # Test with minimal request
                    test_image = Image.new('RGB', (1, 1), color='white')
                    response = self.model.generate_content(
                        ["Test", test_image],
                        generation_config=genai.types.GenerationConfig(max_output_tokens=1)
                    )
                    
                    logger.info(f"✅ Online mode: {model_name}")
                    self.online_mode = True
                    return True
                    
                except Exception as e:
                    if "429" in str(e) or "quota" in str(e).lower():
                        logger.warning(f"⚠️  {model_name}: Quota exceeded")
                        continue
                    else:
                        logger.warning(f"⚠️  {model_name}: {str(e)[:50]}...")
                        continue
            
            logger.warning("⚠️  All models quota exceeded - using offline mode")
            return False
            
        except Exception as e:
            logger.warning(f"⚠️  Setup failed: {e} - using offline mode")
            return False
    
    async def start_server(self, host='localhost', port=8765):
        """Start the WebSocket server for camera bridge communication"""
        try:
            # Create a wrapper function for the handler
            async def handler_wrapper(websocket, path=None):
                return await self.handle_client(websocket)

            self.websocket_server = await websockets.serve(
                handler_wrapper,
                host,
                port
            )
            logger.info(f"🚀 GINI Camera Bridge server started on ws://{host}:{port}")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to start WebSocket server: {e}")
            return False
    
    async def handle_client(self, websocket):
        """Handle WebSocket client connections"""
        self.connected_clients.add(websocket)
        logger.info(f"📱 Client connected. Total clients: {len(self.connected_clients)}")
        
        try:
            async for message in websocket:
                await self.process_message(websocket, message)
        except websockets.exceptions.ConnectionClosed:
            logger.info("📱 Client disconnected")
        except Exception as e:
            logger.error(f"❌ Client error: {e}")
        finally:
            self.connected_clients.discard(websocket)
    
    async def process_message(self, websocket, message):
        """Process incoming WebSocket messages"""
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type == 'camera_frame':
                await self.handle_camera_frame(data)
            elif message_type == 'start_analysis':
                await self.start_analysis()
            elif message_type == 'stop_analysis':
                await self.stop_analysis()
            elif message_type == 'analyze_now':
                await self.analyze_current_frame(data.get('question', 'What do you see?'))
            elif message_type == 'ping':
                await websocket.send(json.dumps({'type': 'pong'}))
                
        except json.JSONDecodeError:
            logger.error("❌ Invalid JSON received")
        except Exception as e:
            logger.error(f"❌ Error processing message: {e}")
    
    async def handle_camera_frame(self, data):
        """Handle incoming camera frame data"""
        try:
            frame_data = data.get('frame')
            if frame_data:
                # Decode base64 frame
                frame_bytes = base64.b64decode(frame_data.split(',')[1])
                self.last_frame = frame_bytes

                # Only analyze if specifically requested (not continuous)
                if data.get('analysis_requested', False):
                    await self.queue_analysis("What do you see in the camera right now?")

        except Exception as e:
            logger.error(f"❌ Error handling camera frame: {e}")
    
    async def queue_analysis(self, question):
        """Queue an analysis request"""
        try:
            await self.analysis_queue.put({
                'question': question,
                'frame': self.last_frame,
                'timestamp': time.time()
            })
        except Exception as e:
            logger.error(f"❌ Error queuing analysis: {e}")
    
    async def start_analysis(self):
        """Start continuous camera analysis"""
        self.analysis_active = True
        logger.info("🧠 Started continuous camera analysis")
        
        # Notify clients
        await self.broadcast_message({
            'type': 'analysis_status',
            'active': True,
            'message': 'AI analysis started'
        })
        
        # Start analysis worker if not already running
        if not hasattr(self, '_analysis_worker_running'):
            self._analysis_worker_running = True
            asyncio.create_task(self.analysis_worker())
    
    async def stop_analysis(self):
        """Stop continuous camera analysis"""
        self.analysis_active = False
        logger.info("🧠 Stopped continuous camera analysis")
        
        # Notify clients
        await self.broadcast_message({
            'type': 'analysis_status',
            'active': False,
            'message': 'AI analysis stopped'
        })
    
    async def analyze_current_frame(self, question):
        """Analyze the current frame immediately"""
        if self.last_frame:
            await self.queue_analysis(question)
        else:
            await self.broadcast_message({
                'type': 'analysis_result',
                'success': False,
                'message': 'No camera frame available for analysis'
            })
    
    async def analysis_worker(self):
        """Background worker for processing analysis requests"""
        while True:
            try:
                # Get analysis request from queue
                analysis_request = await self.analysis_queue.get()
                
                # Perform analysis
                result = await self.perform_analysis(
                    analysis_request['frame'],
                    analysis_request['question']
                )
                
                # Send result to clients
                await self.broadcast_message({
                    'type': 'analysis_result',
                    'success': result['success'],
                    'analysis': result['analysis'],
                    'mode': result.get('mode', 'unknown'),
                    'timestamp': analysis_request['timestamp']
                })
                
            except Exception as e:
                logger.error(f"❌ Analysis worker error: {e}")
                await asyncio.sleep(1)
    
    async def perform_analysis(self, frame_bytes, question):
        """Perform AI analysis on camera frame"""
        try:
            if not frame_bytes:
                return {
                    'success': False,
                    'analysis': 'No frame data available',
                    'mode': 'error'
                }
            
            # Convert frame to PIL Image
            image = Image.open(BytesIO(frame_bytes))
            
            # Enhance question for live analysis
            enhanced_question = f"Looking at this live camera feed frame captured right now, {question}"
            
            # Try online analysis first
            if self.online_mode and self.model:
                try:
                    response = self.model.generate_content(
                        [enhanced_question, image],
                        generation_config=genai.types.GenerationConfig(
                            temperature=0.3,
                            max_output_tokens=300
                        )
                    )
                    
                    return {
                        'success': True,
                        'analysis': response.text,
                        'mode': 'online',
                        'model': self.model_name
                    }
                    
                except Exception as e:
                    logger.warning(f"Online analysis failed: {e}")
                    # Fall back to offline mode
            
            # Offline analysis
            analysis = self.demo_responses[self.response_index]
            self.response_index = (self.response_index + 1) % len(self.demo_responses)
            
            return {
                'success': True,
                'analysis': analysis,
                'mode': 'offline'
            }
            
        except Exception as e:
            logger.error(f"❌ Analysis error: {e}")
            return {
                'success': False,
                'analysis': f'Analysis failed: {str(e)}',
                'mode': 'error'
            }
    
    async def broadcast_message(self, message):
        """Broadcast message to all connected clients"""
        if self.connected_clients:
            disconnected = set()
            for client in self.connected_clients:
                try:
                    await client.send(json.dumps(message))
                except websockets.exceptions.ConnectionClosed:
                    disconnected.add(client)
                except Exception as e:
                    logger.error(f"❌ Error broadcasting to client: {e}")
                    disconnected.add(client)
            
            # Remove disconnected clients
            self.connected_clients -= disconnected
    
    async def run(self):
        """Run the camera bridge service"""
        logger.info("🚀 Starting GINI Camera Bridge Service...")
        
        # Start WebSocket server
        success = await self.start_server()
        if not success:
            return False
        
        logger.info("✅ GINI Camera Bridge Service is running")
        logger.info("📡 Waiting for camera widget connections...")
        
        # Keep the service running
        try:
            await asyncio.Future()  # Run forever
        except KeyboardInterrupt:
            logger.info("🛑 Shutting down GINI Camera Bridge Service...")
        
        return True

async def main():
    """Main entry point for the camera bridge service"""
    print("🔧 Initializing GINI Camera Bridge...")
    bridge = GiniCameraBridge()
    print("📡 Starting WebSocket server...")
    await bridge.run()

if __name__ == "__main__":
    print("🚀 Starting GINI Camera Bridge...")
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 GINI Bridge stopped by user.")
    except Exception as e:
        print(f"❌ Error starting bridge: {e}")
        import traceback
        traceback.print_exc()
