"""
Monitoring Service for Enhanced Nova AI Server
Handles performance monitoring, metrics collection, and alerting
"""

import logging
import time
import threading
from typing import Dict, Any, List, Optional
from collections import deque
import json
import os

class MonitoringService:
    """
    Monitoring service for collecting and managing system metrics
    """
    
    def __init__(self, config):
        """Initialize monitoring service"""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Monitoring configuration
        monitoring_config = config.get('monitoring', {})
        self.enabled = monitoring_config.get('enabled', True)
        self.collection_interval = monitoring_config.get('performance_collection_interval', 60)
        self.retention_days = monitoring_config.get('retention_days', 30)
        
        # Metrics storage
        self.metrics_history = deque(maxlen=10000)  # Keep last 10k metrics
        self.health_history = deque(maxlen=1000)    # Keep last 1k health checks
        self.alerts = deque(maxlen=500)             # Keep last 500 alerts
        
        # Performance counters
        self.performance_counters = {
            'requests_total': 0,
            'requests_success': 0,
            'requests_error': 0,
            'response_times': deque(maxlen=1000),
            'ai_requests': 0,
            'ai_tokens_used': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        
        # Alert thresholds
        self.alert_thresholds = {
            'cpu_critical': 90,
            'cpu_warning': 75,
            'memory_critical': 90,
            'memory_warning': 75,
            'disk_critical': 90,
            'disk_warning': 80,
            'response_time_warning': 5.0,  # seconds
            'error_rate_warning': 0.1      # 10%
        }
        
        # Background collection thread
        self.collection_thread = None
        self.stop_collection = threading.Event()
        
        if self.enabled:
            self._start_collection()
        
        self.logger.info("Monitoring Service initialized")
    
    def _start_collection(self):
        """Start background metrics collection"""
        try:
            self.collection_thread = threading.Thread(
                target=self._collection_loop,
                daemon=True,
                name="MetricsCollector"
            )
            self.collection_thread.start()
            self.logger.info("Started metrics collection thread")
        except Exception as e:
            self.logger.error(f"Failed to start metrics collection: {e}")
    
    def _collection_loop(self):
        """Background loop for collecting metrics"""
        while not self.stop_collection.is_set():
            try:
                self._collect_system_metrics()
                time.sleep(self.collection_interval)
            except Exception as e:
                self.logger.error(f"Error in metrics collection loop: {e}")
                time.sleep(60)  # Wait longer on error
    
    def _collect_system_metrics(self):
        """Collect system metrics"""
        try:
            import psutil
            
            timestamp = time.time()
            
            # System metrics
            metrics = {
                'timestamp': timestamp,
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': (psutil.disk_usage('/').used / psutil.disk_usage('/').total) * 100,
                'network_bytes_sent': psutil.net_io_counters().bytes_sent,
                'network_bytes_recv': psutil.net_io_counters().bytes_recv,
                'process_count': len(psutil.pids()),
                'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else None
            }
            
            # Add performance counters
            metrics.update({
                'requests_total': self.performance_counters['requests_total'],
                'requests_success': self.performance_counters['requests_success'],
                'requests_error': self.performance_counters['requests_error'],
                'ai_requests': self.performance_counters['ai_requests'],
                'ai_tokens_used': self.performance_counters['ai_tokens_used'],
                'cache_hits': self.performance_counters['cache_hits'],
                'cache_misses': self.performance_counters['cache_misses']
            })
            
            # Calculate derived metrics
            if self.performance_counters['requests_total'] > 0:
                metrics['success_rate'] = self.performance_counters['requests_success'] / self.performance_counters['requests_total']
                metrics['error_rate'] = self.performance_counters['requests_error'] / self.performance_counters['requests_total']
            else:
                metrics['success_rate'] = 0
                metrics['error_rate'] = 0
            
            if self.performance_counters['response_times']:
                response_times = list(self.performance_counters['response_times'])
                metrics['avg_response_time'] = sum(response_times) / len(response_times)
                metrics['max_response_time'] = max(response_times)
                metrics['min_response_time'] = min(response_times)
            
            if self.performance_counters['cache_hits'] + self.performance_counters['cache_misses'] > 0:
                total_cache_requests = self.performance_counters['cache_hits'] + self.performance_counters['cache_misses']
                metrics['cache_hit_rate'] = self.performance_counters['cache_hits'] / total_cache_requests
            else:
                metrics['cache_hit_rate'] = 0
            
            # Store metrics
            self.metrics_history.append(metrics)
            
            # Check for alerts
            self._check_alerts(metrics)
            
        except Exception as e:
            self.logger.error(f"Error collecting system metrics: {e}")
    
    def _check_alerts(self, metrics: Dict[str, Any]):
        """Check metrics against alert thresholds"""
        try:
            timestamp = metrics['timestamp']
            
            # CPU alerts
            cpu_percent = metrics.get('cpu_percent', 0)
            if cpu_percent > self.alert_thresholds['cpu_critical']:
                self._create_alert('cpu', 'critical', f'CPU usage is {cpu_percent:.1f}%', timestamp)
            elif cpu_percent > self.alert_thresholds['cpu_warning']:
                self._create_alert('cpu', 'warning', f'CPU usage is {cpu_percent:.1f}%', timestamp)
            
            # Memory alerts
            memory_percent = metrics.get('memory_percent', 0)
            if memory_percent > self.alert_thresholds['memory_critical']:
                self._create_alert('memory', 'critical', f'Memory usage is {memory_percent:.1f}%', timestamp)
            elif memory_percent > self.alert_thresholds['memory_warning']:
                self._create_alert('memory', 'warning', f'Memory usage is {memory_percent:.1f}%', timestamp)
            
            # Disk alerts
            disk_percent = metrics.get('disk_percent', 0)
            if disk_percent > self.alert_thresholds['disk_critical']:
                self._create_alert('disk', 'critical', f'Disk usage is {disk_percent:.1f}%', timestamp)
            elif disk_percent > self.alert_thresholds['disk_warning']:
                self._create_alert('disk', 'warning', f'Disk usage is {disk_percent:.1f}%', timestamp)
            
            # Response time alerts
            avg_response_time = metrics.get('avg_response_time', 0)
            if avg_response_time > self.alert_thresholds['response_time_warning']:
                self._create_alert('performance', 'warning', f'Average response time is {avg_response_time:.2f}s', timestamp)
            
            # Error rate alerts
            error_rate = metrics.get('error_rate', 0)
            if error_rate > self.alert_thresholds['error_rate_warning']:
                self._create_alert('errors', 'warning', f'Error rate is {error_rate:.1%}', timestamp)
            
        except Exception as e:
            self.logger.error(f"Error checking alerts: {e}")
    
    def _create_alert(self, alert_type: str, level: str, message: str, timestamp: float):
        """Create an alert"""
        try:
            alert = {
                'type': alert_type,
                'level': level,
                'message': message,
                'timestamp': timestamp,
                'id': f"{alert_type}_{level}_{int(timestamp)}"
            }
            
            # Check if similar alert already exists recently
            recent_alerts = [a for a in self.alerts if timestamp - a['timestamp'] < 300]  # 5 minutes
            similar_alert = any(a['type'] == alert_type and a['level'] == level for a in recent_alerts)
            
            if not similar_alert:
                self.alerts.append(alert)
                self.logger.warning(f"Alert created: {level.upper()} - {message}")
            
        except Exception as e:
            self.logger.error(f"Error creating alert: {e}")
    
    def record_request(self, success: bool, response_time: float):
        """Record a request for performance tracking"""
        try:
            self.performance_counters['requests_total'] += 1
            if success:
                self.performance_counters['requests_success'] += 1
            else:
                self.performance_counters['requests_error'] += 1
            
            self.performance_counters['response_times'].append(response_time)
            
        except Exception as e:
            self.logger.error(f"Error recording request: {e}")
    
    def record_ai_request(self, tokens_used: int):
        """Record an AI request"""
        try:
            self.performance_counters['ai_requests'] += 1
            self.performance_counters['ai_tokens_used'] += tokens_used
        except Exception as e:
            self.logger.error(f"Error recording AI request: {e}")
    
    def record_cache_hit(self):
        """Record a cache hit"""
        try:
            self.performance_counters['cache_hits'] += 1
        except Exception as e:
            self.logger.error(f"Error recording cache hit: {e}")
    
    def record_cache_miss(self):
        """Record a cache miss"""
        try:
            self.performance_counters['cache_misses'] += 1
        except Exception as e:
            self.logger.error(f"Error recording cache miss: {e}")
    
    def record_health_status(self, health_data: Dict[str, Any]):
        """Record health check status"""
        try:
            health_record = {
                'timestamp': time.time(),
                'status': health_data.get('status', 'unknown'),
                'services': health_data.get('services', {}),
                'system': health_data.get('system', {})
            }
            
            self.health_history.append(health_record)
            
        except Exception as e:
            self.logger.error(f"Error recording health status: {e}")
    
    def record_performance_metrics(self, metrics: Dict[str, Any]):
        """Record performance metrics from external sources"""
        try:
            metrics['timestamp'] = time.time()
            self.metrics_history.append(metrics)
        except Exception as e:
            self.logger.error(f"Error recording performance metrics: {e}")
    
    def get_metrics(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get metrics for the specified time period"""
        try:
            cutoff_time = time.time() - (hours * 3600)
            return [m for m in self.metrics_history if m['timestamp'] > cutoff_time]
        except Exception as e:
            self.logger.error(f"Error getting metrics: {e}")
            return []
    
    def get_health_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get health check history"""
        try:
            cutoff_time = time.time() - (hours * 3600)
            return [h for h in self.health_history if h['timestamp'] > cutoff_time]
        except Exception as e:
            self.logger.error(f"Error getting health history: {e}")
            return []
    
    def get_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get alerts for the specified time period"""
        try:
            cutoff_time = time.time() - (hours * 3600)
            return [a for a in self.alerts if a['timestamp'] > cutoff_time]
        except Exception as e:
            self.logger.error(f"Error getting alerts: {e}")
            return []
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        try:
            summary = {
                'timestamp': time.time(),
                'counters': dict(self.performance_counters),
                'thresholds': self.alert_thresholds.copy()
            }
            
            # Remove deque objects for JSON serialization
            if 'response_times' in summary['counters']:
                response_times = list(summary['counters']['response_times'])
                summary['counters']['response_times_count'] = len(response_times)
                if response_times:
                    summary['counters']['avg_response_time'] = sum(response_times) / len(response_times)
                    summary['counters']['max_response_time'] = max(response_times)
                    summary['counters']['min_response_time'] = min(response_times)
                del summary['counters']['response_times']
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Error getting performance summary: {e}")
            return {}
    
    def reset_counters(self):
        """Reset performance counters"""
        try:
            self.performance_counters = {
                'requests_total': 0,
                'requests_success': 0,
                'requests_error': 0,
                'response_times': deque(maxlen=1000),
                'ai_requests': 0,
                'ai_tokens_used': 0,
                'cache_hits': 0,
                'cache_misses': 0
            }
            self.logger.info("Performance counters reset")
        except Exception as e:
            self.logger.error(f"Error resetting counters: {e}")
    
    def health_check(self) -> Dict[str, Any]:
        """Perform monitoring service health check"""
        try:
            return {
                'status': 'healthy',
                'enabled': self.enabled,
                'collection_interval': self.collection_interval,
                'metrics_count': len(self.metrics_history),
                'health_records_count': len(self.health_history),
                'alerts_count': len(self.alerts),
                'collection_thread_alive': self.collection_thread.is_alive() if self.collection_thread else False
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }
    
    def shutdown(self):
        """Shutdown monitoring service"""
        try:
            if self.collection_thread:
                self.stop_collection.set()
                self.collection_thread.join(timeout=5)
            
            self.logger.info("Monitoring Service shutdown completed")
        except Exception as e:
            self.logger.error(f"Error during monitoring service shutdown: {e}")
