#!/usr/bin/env python3
"""
Simple GINI Bridge Test
"""

import asyncio
import json
import logging
import websockets

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleGiniBridge:
    def __init__(self):
        self.connected_clients = set()
    
    async def handle_client(self, websocket):
        """Handle WebSocket client connections"""
        self.connected_clients.add(websocket)
        logger.info(f"📱 Client connected. Total clients: {len(self.connected_clients)}")
        
        try:
            async for message in websocket:
                logger.info(f"📨 Received: {message}")
                
                # Echo back a simple response
                response = {
                    'type': 'analysis_result',
                    'success': True,
                    'analysis': 'I can see the camera feed! GINI bridge is working.',
                    'mode': 'test'
                }
                await websocket.send(json.dumps(response))
                
        except websockets.exceptions.ConnectionClosed:
            logger.info("📱 Client disconnected")
        except Exception as e:
            logger.error(f"❌ Client error: {e}")
        finally:
            self.connected_clients.discard(websocket)
    
    async def start_server(self, host='localhost', port=8766):
        """Start the WebSocket server"""
        try:
            async def handler_wrapper(websocket, path=None):
                return await self.handle_client(websocket)
            
            server = await websockets.serve(handler_wrapper, host, port)
            logger.info(f"🚀 Test GINI Bridge server started on ws://{host}:{port}")
            return server
        except Exception as e:
            logger.error(f"❌ Failed to start server: {e}")
            return None
    
    async def run(self):
        """Run the test bridge"""
        server = await self.start_server()
        if server:
            logger.info("✅ Test GINI Bridge is running")
            logger.info("📡 Waiting for camera widget connections...")
            
            try:
                await asyncio.Future()  # Run forever
            except KeyboardInterrupt:
                logger.info("🛑 Shutting down...")
        
        return True

async def main():
    bridge = SimpleGiniBridge()
    await bridge.run()

if __name__ == "__main__":
    print("🧪 Starting Test GINI Bridge...")
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
