"""
WebSocket Service for Enhanced Nova AI Server
Handles real-time communication via WebSockets
"""

import logging
import time
import json
from typing import Dict, Any, List, Optional, Callable
import asyncio
import threading

try:
    import socketio
    SOCKETIO_AVAILABLE = True
except ImportError:
    SOCKETIO_AVAILABLE = False

class WebSocketService:
    """
    WebSocket service for real-time communication
    """
    
    def __init__(self, config):
        """Initialize WebSocket service"""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # WebSocket configuration
        ws_config = config.get('websocket', {})
        self.enabled = ws_config.get('enabled', True) and SOCKETIO_AVAILABLE
        
        if not self.enabled:
            if not SOCKETIO_AVAILABLE:
                self.logger.warning("SocketIO not available, WebSocket service disabled")
            else:
                self.logger.info("WebSocket service disabled in configuration")
            return
        
        # Initialize SocketIO server
        self.sio = socketio.AsyncServer(
            cors_allowed_origins=ws_config.get('cors_allowed_origins', ["*"]),
            ping_timeout=ws_config.get('ping_timeout', 60),
            ping_interval=ws_config.get('ping_interval', 25)
        )
        
        # Connection management
        self.connections = {}
        self.rooms = {}
        
        # Event handlers
        self.event_handlers = {}
        
        # Setup default event handlers
        self._setup_default_handlers()
        
        self.logger.info("WebSocket Service initialized")
    
    def _setup_default_handlers(self):
        """Setup default WebSocket event handlers"""
        
        @self.sio.event
        async def connect(sid, environ):
            """Handle client connection"""
            try:
                self.connections[sid] = {
                    'connected_at': time.time(),
                    'user_id': None,
                    'rooms': set()
                }
                
                self.logger.info(f"WebSocket client connected: {sid}")
                
                # Send welcome message
                await self.sio.emit('connected', {
                    'message': 'Connected to Nova AI WebSocket',
                    'sid': sid,
                    'timestamp': time.time()
                }, room=sid)
                
            except Exception as e:
                self.logger.error(f"Error handling WebSocket connection: {e}")
        
        @self.sio.event
        async def disconnect(sid):
            """Handle client disconnection"""
            try:
                if sid in self.connections:
                    connection_info = self.connections[sid]
                    
                    # Leave all rooms
                    for room in connection_info['rooms']:
                        await self.sio.leave_room(sid, room)
                        if room in self.rooms:
                            self.rooms[room].discard(sid)
                    
                    del self.connections[sid]
                    
                    self.logger.info(f"WebSocket client disconnected: {sid}")
                
            except Exception as e:
                self.logger.error(f"Error handling WebSocket disconnection: {e}")
        
        @self.sio.event
        async def authenticate(sid, data):
            """Handle client authentication"""
            try:
                token = data.get('token')
                if not token:
                    await self.sio.emit('auth_error', {'error': 'No token provided'}, room=sid)
                    return
                
                # Verify token with auth service
                if hasattr(self, 'auth_service') and self.auth_service:
                    user_data = self.auth_service.verify_jwt_token(token)
                    if user_data:
                        self.connections[sid]['user_id'] = user_data.get('username')
                        await self.sio.emit('authenticated', {
                            'user': user_data.get('username'),
                            'timestamp': time.time()
                        }, room=sid)
                        self.logger.info(f"WebSocket client authenticated: {sid} -> {user_data.get('username')}")
                    else:
                        await self.sio.emit('auth_error', {'error': 'Invalid token'}, room=sid)
                else:
                    await self.sio.emit('auth_error', {'error': 'Authentication service not available'}, room=sid)
                
            except Exception as e:
                self.logger.error(f"Error handling WebSocket authentication: {e}")
                await self.sio.emit('auth_error', {'error': 'Authentication failed'}, room=sid)
        
        @self.sio.event
        async def join_room(sid, data):
            """Handle room join requests"""
            try:
                room_name = data.get('room')
                if not room_name:
                    await self.sio.emit('error', {'error': 'No room specified'}, room=sid)
                    return
                
                # Join room
                await self.sio.enter_room(sid, room_name)
                
                # Update connection info
                if sid in self.connections:
                    self.connections[sid]['rooms'].add(room_name)
                
                # Update room info
                if room_name not in self.rooms:
                    self.rooms[room_name] = set()
                self.rooms[room_name].add(sid)
                
                await self.sio.emit('joined_room', {
                    'room': room_name,
                    'timestamp': time.time()
                }, room=sid)
                
                self.logger.info(f"WebSocket client {sid} joined room: {room_name}")
                
            except Exception as e:
                self.logger.error(f"Error handling room join: {e}")
                await self.sio.emit('error', {'error': 'Failed to join room'}, room=sid)
        
        @self.sio.event
        async def leave_room(sid, data):
            """Handle room leave requests"""
            try:
                room_name = data.get('room')
                if not room_name:
                    await self.sio.emit('error', {'error': 'No room specified'}, room=sid)
                    return
                
                # Leave room
                await self.sio.leave_room(sid, room_name)
                
                # Update connection info
                if sid in self.connections:
                    self.connections[sid]['rooms'].discard(room_name)
                
                # Update room info
                if room_name in self.rooms:
                    self.rooms[room_name].discard(sid)
                    if not self.rooms[room_name]:
                        del self.rooms[room_name]
                
                await self.sio.emit('left_room', {
                    'room': room_name,
                    'timestamp': time.time()
                }, room=sid)
                
                self.logger.info(f"WebSocket client {sid} left room: {room_name}")
                
            except Exception as e:
                self.logger.error(f"Error handling room leave: {e}")
                await self.sio.emit('error', {'error': 'Failed to leave room'}, room=sid)
    
    def set_auth_service(self, auth_service):
        """Set authentication service for token verification"""
        self.auth_service = auth_service
    
    def register_event_handler(self, event_name: str, handler: Callable):
        """Register custom event handler"""
        if not self.enabled:
            return
        
        @self.sio.event
        async def custom_handler(sid, data):
            try:
                await handler(sid, data, self)
            except Exception as e:
                self.logger.error(f"Error in custom handler for {event_name}: {e}")
                await self.sio.emit('error', {'error': f'Handler error: {str(e)}'}, room=sid)
        
        # Register with SocketIO
        self.sio.on(event_name, custom_handler)
        self.event_handlers[event_name] = handler
        
        self.logger.info(f"Registered WebSocket event handler: {event_name}")
    
    async def emit_to_user(self, user_id: str, event: str, data: Dict[str, Any]):
        """Emit event to specific user"""
        if not self.enabled:
            return
        
        try:
            # Find user's connections
            user_sids = [sid for sid, conn in self.connections.items() 
                        if conn.get('user_id') == user_id]
            
            for sid in user_sids:
                await self.sio.emit(event, data, room=sid)
            
            self.logger.debug(f"Emitted {event} to user {user_id} ({len(user_sids)} connections)")
            
        except Exception as e:
            self.logger.error(f"Error emitting to user {user_id}: {e}")
    
    async def emit_to_room(self, room_name: str, event: str, data: Dict[str, Any]):
        """Emit event to all clients in a room"""
        if not self.enabled:
            return
        
        try:
            await self.sio.emit(event, data, room=room_name)
            self.logger.debug(f"Emitted {event} to room {room_name}")
            
        except Exception as e:
            self.logger.error(f"Error emitting to room {room_name}: {e}")
    
    async def emit_to_all(self, event: str, data: Dict[str, Any]):
        """Emit event to all connected clients"""
        if not self.enabled:
            return
        
        try:
            await self.sio.emit(event, data)
            self.logger.debug(f"Emitted {event} to all clients")
            
        except Exception as e:
            self.logger.error(f"Error emitting to all clients: {e}")
    
    async def broadcast_chat_message(self, message: str, user: str, session_id: str):
        """Broadcast chat message to relevant clients"""
        if not self.enabled:
            return
        
        try:
            data = {
                'message': message,
                'user': user,
                'session_id': session_id,
                'timestamp': time.time(),
                'type': 'chat_message'
            }
            
            # Emit to session room
            await self.emit_to_room(f"session_{session_id}", 'chat_message', data)
            
        except Exception as e:
            self.logger.error(f"Error broadcasting chat message: {e}")
    
    async def broadcast_ai_response(self, response: str, provider: str, session_id: str):
        """Broadcast AI response to relevant clients"""
        if not self.enabled:
            return
        
        try:
            data = {
                'response': response,
                'provider': provider,
                'session_id': session_id,
                'timestamp': time.time(),
                'type': 'ai_response'
            }
            
            # Emit to session room
            await self.emit_to_room(f"session_{session_id}", 'ai_response', data)
            
        except Exception as e:
            self.logger.error(f"Error broadcasting AI response: {e}")
    
    async def broadcast_system_notification(self, message: str, level: str = 'info'):
        """Broadcast system notification to all clients"""
        if not self.enabled:
            return
        
        try:
            data = {
                'message': message,
                'level': level,
                'timestamp': time.time(),
                'type': 'system_notification'
            }
            
            await self.emit_to_all('system_notification', data)
            
        except Exception as e:
            self.logger.error(f"Error broadcasting system notification: {e}")
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get WebSocket connection statistics"""
        if not self.enabled:
            return {'enabled': False}
        
        try:
            authenticated_users = len(set(conn.get('user_id') for conn in self.connections.values() 
                                        if conn.get('user_id')))
            
            return {
                'enabled': True,
                'total_connections': len(self.connections),
                'authenticated_users': authenticated_users,
                'total_rooms': len(self.rooms),
                'event_handlers': len(self.event_handlers)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting connection stats: {e}")
            return {'enabled': True, 'error': str(e)}
    
    def get_room_info(self, room_name: str) -> Dict[str, Any]:
        """Get information about a specific room"""
        if not self.enabled:
            return {'enabled': False}
        
        try:
            if room_name not in self.rooms:
                return {'exists': False}
            
            room_sids = self.rooms[room_name]
            users = [self.connections[sid].get('user_id') for sid in room_sids 
                    if sid in self.connections and self.connections[sid].get('user_id')]
            
            return {
                'exists': True,
                'room_name': room_name,
                'connection_count': len(room_sids),
                'users': list(set(users))  # Remove duplicates
            }
            
        except Exception as e:
            self.logger.error(f"Error getting room info: {e}")
            return {'exists': False, 'error': str(e)}
    
    def health_check(self) -> Dict[str, Any]:
        """Perform WebSocket service health check"""
        try:
            if not self.enabled:
                return {
                    'status': 'disabled',
                    'reason': 'WebSocket service disabled or SocketIO not available'
                }
            
            stats = self.get_connection_stats()
            
            return {
                'status': 'healthy',
                'stats': stats
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }
    
    def get_asgi_app(self):
        """Get ASGI app for integration with web framework"""
        if not self.enabled:
            return None
        
        return socketio.ASGIApp(self.sio)
    
    def shutdown(self):
        """Shutdown WebSocket service"""
        try:
            if self.enabled and hasattr(self, 'sio'):
                # Disconnect all clients
                asyncio.create_task(self.emit_to_all('server_shutdown', {
                    'message': 'Server is shutting down',
                    'timestamp': time.time()
                }))
            
            self.logger.info("WebSocket Service shutdown completed")
            
        except Exception as e:
            self.logger.error(f"Error during WebSocket service shutdown: {e}")
