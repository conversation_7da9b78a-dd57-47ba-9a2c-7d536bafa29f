<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gini 1.5 - AI Screen Analyzer</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --neon-green: #00ff88;
            --neon-cyan: #00ffff;
            --neon-purple: #8a2be2;
            --dark-bg: #0a0a0a;
            --glass-bg: rgba(0, 255, 136, 0.1);
            --glass-border: rgba(0, 255, 136, 0.3);
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --error-color: #ff4444;
            --success-color: #00ff88;
            --warning-color: #ffaa00;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', 'Courier New', monospace;
            background: radial-gradient(ellipse at center, #0a1a0a 0%, #000000 100%);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Animated background grid */
        .grid-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(0, 255, 136, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 136, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: grid-move 20s linear infinite;
            z-index: -1;
        }

        @keyframes grid-move {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        .main-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .app-header {
            text-align: center;
            margin-bottom: 40px;
            animation: glow-pulse 3s ease-in-out infinite alternate;
        }

        .app-title {
            font-size: 4rem;
            font-weight: bold;
            background: linear-gradient(45deg, var(--neon-green), var(--neon-cyan), var(--neon-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            text-shadow: 0 0 30px rgba(0, 255, 136, 0.5);
            letter-spacing: 3px;
        }

        .app-subtitle {
            color: var(--text-secondary);
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .app-version {
            color: var(--neon-cyan);
            font-size: 0.9rem;
            opacity: 0.7;
        }

        @keyframes glow-pulse {
            0% { text-shadow: 0 0 30px rgba(0, 255, 136, 0.5); }
            100% { text-shadow: 0 0 50px rgba(0, 255, 136, 0.8), 0 0 70px rgba(0, 255, 255, 0.3); }
        }

        .screen-analyzer-container {
            background: var(--glass-bg);
            backdrop-filter: blur(15px);
            border: 2px solid var(--glass-border);
            border-radius: 25px;
            padding: 40px;
            max-width: 900px;
            width: 100%;
            box-shadow:
                0 0 40px rgba(0, 255, 136, 0.3),
                inset 0 0 40px rgba(0, 255, 136, 0.1);
            position: relative;
            animation: container-glow 4s ease-in-out infinite alternate;
        }

        @keyframes container-glow {
            0% {
                box-shadow: 0 0 40px rgba(0, 255, 136, 0.3), inset 0 0 40px rgba(0, 255, 136, 0.1);
                border-color: rgba(0, 255, 136, 0.3);
            }
            100% {
                box-shadow: 0 0 60px rgba(0, 255, 136, 0.5), inset 0 0 60px rgba(0, 255, 136, 0.2);
                border-color: rgba(0, 255, 136, 0.5);
            }
        }

        /* Corner brackets for futuristic look */
        .screen-analyzer-container::before,
        .screen-analyzer-container::after {
            content: '';
            position: absolute;
            width: 40px;
            height: 40px;
            border: 3px solid var(--neon-green);
            z-index: 2;
        }

        .screen-analyzer-container::before {
            top: 15px;
            left: 15px;
            border-right: none;
            border-bottom: none;
            animation: corner-glow 2s ease-in-out infinite alternate;
        }

        .screen-analyzer-container::after {
            bottom: 15px;
            right: 15px;
            border-left: none;
            border-top: none;
            animation: corner-glow 2s ease-in-out infinite alternate 1s;
        }

        @keyframes corner-glow {
            0% { border-color: var(--neon-green); }
            100% { border-color: var(--neon-cyan); }
        }

        .control-section {
            margin-bottom: 30px;
        }

        .main-capture-button {
            background: linear-gradient(45deg, var(--neon-green), var(--neon-cyan));
            border: none;
            border-radius: 20px;
            padding: 20px 40px;
            font-size: 1.4rem;
            font-weight: bold;
            color: var(--dark-bg);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            width: 100%;
            margin-bottom: 25px;
            box-shadow: 0 8px 20px rgba(0, 255, 136, 0.4);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .main-capture-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(0, 255, 136, 0.6);
            background: linear-gradient(45deg, var(--neon-cyan), var(--neon-green));
        }

        .main-capture-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .voice-command-section {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(0, 255, 136, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .voice-command-title {
            color: var(--neon-cyan);
            font-size: 1.1rem;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .voice-commands {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }

        .voice-command {
            background: rgba(0, 255, 136, 0.2);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 8px 15px;
            font-size: 0.9rem;
            color: var(--text-primary);
            opacity: 0.8;
        }

        .analysis-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }

        .option-card {
            background: rgba(0, 0, 0, 0.4);
            border: 2px solid transparent;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .option-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .option-card:hover::before {
            left: 100%;
        }

        .option-card.active {
            border-color: var(--neon-green);
            background: rgba(0, 255, 136, 0.1);
            transform: scale(1.05);
        }

        .option-card:hover {
            border-color: var(--neon-cyan);
            transform: translateY(-2px);
        }

        .option-icon {
            font-size: 2rem;
            color: var(--neon-green);
            margin-bottom: 10px;
        }

        .option-title {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .option-description {
            font-size: 0.9rem;
            color: var(--text-secondary);
            opacity: 0.8;
        }

        .custom-question-section {
            margin-bottom: 25px;
            display: none;
        }

        .question-input {
            width: 100%;
            background: rgba(0, 0, 0, 0.6);
            border: 2px solid var(--glass-border);
            border-radius: 15px;
            padding: 20px;
            color: var(--text-primary);
            font-size: 1.1rem;
            font-family: inherit;
            resize: vertical;
            min-height: 80px;
            transition: border-color 0.3s ease;
        }

        .question-input:focus {
            outline: none;
            border-color: var(--neon-green);
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
        }

        .question-input::placeholder {
            color: var(--text-secondary);
            opacity: 0.6;
        }

        .preview-section {
            margin-bottom: 30px;
        }

        .image-preview {
            width: 100%;
            max-height: 400px;
            border-radius: 15px;
            border: 2px solid var(--glass-border);
            object-fit: contain;
            background: rgba(0, 0, 0, 0.3);
            display: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }

        .loading-section {
            display: none;
            text-align: center;
            padding: 30px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(0, 255, 136, 0.3);
            border-top: 4px solid var(--neon-green);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 1.1rem;
            color: var(--neon-green);
            margin-bottom: 10px;
        }

        .loading-subtext {
            font-size: 0.9rem;
            color: var(--text-secondary);
            opacity: 0.7;
        }

        .results-section {
            margin-top: 30px;
        }

        .analysis-result {
            background: rgba(0, 0, 0, 0.6);
            border: 2px solid var(--glass-border);
            border-radius: 20px;
            padding: 30px;
            margin-top: 20px;
            max-height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
            line-height: 1.8;
            font-size: 1rem;
            position: relative;
        }

        .result-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--glass-border);
        }

        .result-title {
            color: var(--neon-green);
            font-size: 1.3rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .result-meta {
            font-size: 0.9rem;
            color: var(--text-secondary);
            opacity: 0.8;
        }

        .result-content {
            color: var(--text-primary);
            line-height: 1.8;
        }

        .copy-result-button {
            background: rgba(0, 255, 136, 0.2);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 8px 15px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .copy-result-button:hover {
            background: rgba(0, 255, 136, 0.4);
            transform: translateY(-1px);
        }

        .copy-result-button.copied {
            background: var(--success-color);
            color: var(--dark-bg);
        }

        .status-message {
            padding: 15px 20px;
            border-radius: 10px;
            margin-top: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: bold;
        }

        .status-message.error {
            background: rgba(255, 68, 68, 0.2);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }

        .status-message.success {
            background: rgba(0, 255, 136, 0.2);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }

        .status-message.warning {
            background: rgba(255, 170, 0, 0.2);
            border: 1px solid var(--warning-color);
            color: var(--warning-color);
        }

        .chat-integration {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(0, 255, 136, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
            text-align: center;
        }

        .chat-title {
            color: var(--neon-purple);
            font-size: 1.2rem;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .chat-input {
            width: 100%;
            background: rgba(0, 0, 0, 0.6);
            border: 2px solid var(--glass-border);
            border-radius: 15px;
            padding: 15px;
            color: var(--text-primary);
            font-size: 1rem;
            font-family: inherit;
            margin-bottom: 15px;
            transition: border-color 0.3s ease;
        }

        .chat-input:focus {
            outline: none;
            border-color: var(--neon-purple);
            box-shadow: 0 0 20px rgba(138, 43, 226, 0.3);
        }

        .chat-send-button {
            background: linear-gradient(45deg, var(--neon-purple), var(--neon-cyan));
            border: none;
            border-radius: 15px;
            padding: 12px 25px;
            font-size: 1rem;
            font-weight: bold;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin: 0 auto;
        }

        .chat-send-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(138, 43, 226, 0.4);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .app-title {
                font-size: 2.5rem;
            }

            .screen-analyzer-container {
                padding: 25px;
                margin: 10px;
            }

            .analysis-options {
                grid-template-columns: 1fr;
            }

            .main-capture-button {
                font-size: 1.2rem;
                padding: 15px 30px;
            }
        }

        /* Custom scrollbar */
        .analysis-result::-webkit-scrollbar {
            width: 8px;
        }

        .analysis-result::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
        }

        .analysis-result::-webkit-scrollbar-thumb {
            background: var(--neon-green);
            border-radius: 4px;
        }

        .analysis-result::-webkit-scrollbar-thumb:hover {
            background: var(--neon-cyan);
        }
    </style>
</head>
<body>
    <div class="grid-background"></div>

    <div class="main-container">
        <div class="app-header">
            <h1 class="app-title">GINI 1.5</h1>
            <p class="app-subtitle">AI-Powered Screen Analysis System</p>
            <p class="app-version">Powered by Google Gemini 1.5 Pro Vision</p>
        </div>

        <div class="screen-analyzer-container">
            <div class="control-section">
                <button class="main-capture-button" id="mainCaptureBtn" onclick="captureAndAnalyzeScreen()">
                    <i class="fas fa-camera-retro"></i>
                    What's on my screen?
                </button>

                <div class="voice-command-section">
                    <div class="voice-command-title">
                        <i class="fas fa-microphone"></i> Voice Commands
                    </div>
                    <div class="voice-commands">
                        <span class="voice-command">"What's on my screen?"</span>
                        <span class="voice-command">"Analyze my screen"</span>
                        <span class="voice-command">"Describe what I'm looking at"</span>
                        <span class="voice-command">"Screen capture"</span>
                    </div>
                </div>

                <div class="analysis-options">
                    <div class="option-card active" data-type="detailed" onclick="selectAnalysisType('detailed')">
                        <div class="option-icon">
                            <i class="fas fa-search-plus"></i>
                        </div>
                        <div class="option-title">Detailed Analysis</div>
                        <div class="option-description">Comprehensive breakdown of all screen elements</div>
                    </div>

                    <div class="option-card" data-type="quick" onclick="selectAnalysisType('quick')">
                        <div class="option-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="option-title">Quick Summary</div>
                        <div class="option-description">Brief overview of main content</div>
                    </div>

                    <div class="option-card" data-type="custom" onclick="selectAnalysisType('custom')">
                        <div class="option-icon">
                            <i class="fas fa-question-circle"></i>
                        </div>
                        <div class="option-title">Custom Question</div>
                        <div class="option-description">Ask specific questions about your screen</div>
                    </div>
                </div>

                <div class="custom-question-section" id="customQuestionSection">
                    <textarea
                        class="question-input"
                        id="customQuestionInput"
                        placeholder="Ask a specific question about your screen content... For example: 'What programming language is being used?' or 'Summarize the main points of this document'"
                        rows="3"
                    ></textarea>
                </div>
            </div>

            <div class="preview-section">
                <img class="image-preview" id="imagePreview" alt="Screen capture preview">
            </div>

            <div class="loading-section" id="loadingSection">
                <div class="loading-spinner"></div>
                <div class="loading-text">Analyzing your screen with AI...</div>
                <div class="loading-subtext">This may take a few moments</div>
            </div>

            <div class="results-section" id="resultsSection">
                <!-- Analysis results will be displayed here -->
            </div>

            <div class="chat-integration">
                <div class="chat-title">
                    <i class="fas fa-comments"></i> Ask Follow-up Questions
                </div>
                <input
                    type="text"
                    class="chat-input"
                    id="chatInput"
                    placeholder="Ask a follow-up question about the analysis..."
                    disabled
                >
                <button class="chat-send-button" id="chatSendBtn" onclick="sendFollowUpQuestion()" disabled>
                    <i class="fas fa-paper-plane"></i>
                    Ask AI
                </button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentAnalysisType = 'detailed';
        let lastCapturedImage = null;
        let apiPort = new URLSearchParams(window.location.search).get('api_port') || '5000';
        let isAnalyzing = false;

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Gini 1.5 Screen Analyzer initialized');
            testApiConnection();
            setupEventListeners();
        });

        function setupEventListeners() {
            // Chat input enter key
            document.getElementById('chatInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendFollowUpQuestion();
                }
            });

            // Global keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'Enter') {
                    captureAndAnalyzeScreen();
                } else if (e.key === 'Escape') {
                    // Clear results or close any modals
                    clearResults();
                }
            });
        }

        function selectAnalysisType(type) {
            currentAnalysisType = type;

            // Update UI
            document.querySelectorAll('.option-card').forEach(card => {
                card.classList.remove('active');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('active');

            // Show/hide custom question section
            const customSection = document.getElementById('customQuestionSection');
            if (type === 'custom') {
                customSection.style.display = 'block';
                document.getElementById('customQuestionInput').focus();
            } else {
                customSection.style.display = 'none';
            }

            console.log(`📊 Analysis type changed to: ${type}`);
        }

        async function captureAndAnalyzeScreen() {
            if (isAnalyzing) {
                showStatusMessage('Analysis already in progress...', 'warning');
                return;
            }

            try {
                isAnalyzing = true;
                updateCaptureButton(true);
                showLoading();
                clearResults();

                console.log('📸 Starting screen capture...');

                // Request screen capture permission
                const stream = await navigator.mediaDevices.getDisplayMedia({
                    video: {
                        mediaSource: 'screen',
                        width: { ideal: 1920 },
                        height: { ideal: 1080 }
                    }
                });

                console.log('✅ Screen capture permission granted');

                // Create video element to capture frame
                const video = document.createElement('video');
                video.srcObject = stream;
                video.play();

                // Wait for video to load
                await new Promise(resolve => {
                    video.onloadedmetadata = resolve;
                });

                // Create canvas and capture frame
                const canvas = document.createElement('canvas');
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(video, 0, 0);

                // Stop the stream
                stream.getTracks().forEach(track => track.stop());

                // Convert to base64
                const imageData = canvas.toDataURL('image/png').split(',')[1];
                lastCapturedImage = imageData;

                console.log(`📸 Screen captured: ${canvas.width}x${canvas.height}`);

                // Show preview
                const preview = document.getElementById('imagePreview');
                preview.src = canvas.toDataURL('image/png');
                preview.style.display = 'block';

                // Prepare analysis request
                const requestData = {
                    image_data: imageData,
                    type: currentAnalysisType
                };

                if (currentAnalysisType === 'custom') {
                    const question = document.getElementById('customQuestionInput').value.trim();
                    if (!question) {
                        throw new Error('Please enter a question for custom analysis');
                    }
                    requestData.question = question;
                }

                console.log('🧠 Sending to AI for analysis...');

                // Send to backend for analysis
                const response = await fetch(`http://127.0.0.1:${apiPort}/api/screen-capture/analyze`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    throw new Error(`API request failed: ${response.status} ${response.statusText}`);
                }

                const result = await response.json();

                hideLoading();

                if (result.success) {
                    showAnalysisResult(result);
                    enableFollowUpChat();
                    showStatusMessage('Screen analysis completed successfully! 🎉', 'success');
                } else {
                    throw new Error(result.error || 'Analysis failed');
                }

            } catch (error) {
                console.error('❌ Screen capture/analysis error:', error);
                hideLoading();
                showStatusMessage(`Error: ${error.message}`, 'error');

                if (error.name === 'NotAllowedError') {
                    showStatusMessage('Screen capture permission denied. Please allow screen sharing to use this feature.', 'error');
                } else if (error.name === 'NotSupportedError') {
                    showStatusMessage('Screen capture is not supported in this browser. Please use Chrome, Firefox, or Edge.', 'error');
                }
            } finally {
                isAnalyzing = false;
                updateCaptureButton(false);
            }
        }

        async function sendFollowUpQuestion() {
            const chatInput = document.getElementById('chatInput');
            const question = chatInput.value.trim();

            if (!question || !lastCapturedImage) {
                showStatusMessage('Please enter a question and ensure you have captured a screen first.', 'warning');
                return;
            }

            try {
                showLoading();
                chatInput.disabled = true;
                document.getElementById('chatSendBtn').disabled = true;

                const requestData = {
                    image_data: lastCapturedImage,
                    type: 'custom',
                    question: question
                };

                const response = await fetch(`http://127.0.0.1:${apiPort}/api/screen-capture/analyze`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();
                hideLoading();

                if (result.success) {
                    showAnalysisResult(result, true);
                    chatInput.value = '';
                    showStatusMessage('Follow-up analysis completed! 💬', 'success');
                } else {
                    throw new Error(result.error || 'Follow-up analysis failed');
                }

            } catch (error) {
                console.error('❌ Follow-up question error:', error);
                hideLoading();
                showStatusMessage(`Error: ${error.message}`, 'error');
            } finally {
                chatInput.disabled = false;
                document.getElementById('chatSendBtn').disabled = false;
            }
        }

        function showAnalysisResult(result, isFollowUp = false) {
            const resultsSection = document.getElementById('resultsSection');

            const resultDiv = document.createElement('div');
            resultDiv.className = 'analysis-result';

            const headerDiv = document.createElement('div');
            headerDiv.className = 'result-header';

            const titleDiv = document.createElement('div');
            titleDiv.className = 'result-title';
            titleDiv.innerHTML = `
                <i class="fas fa-robot"></i>
                ${isFollowUp ? 'Follow-up Analysis' : 'AI Analysis Results'}
            `;

            const metaDiv = document.createElement('div');
            metaDiv.className = 'result-meta';
            metaDiv.innerHTML = `
                Type: ${result.analysis_type || 'detailed'} |
                ${result.image_size ? `Size: ${result.image_size} | ` : ''}
                Model: ${result.model || 'gemini-1.5-pro'}
            `;

            const copyBtn = document.createElement('button');
            copyBtn.className = 'copy-result-button';
            copyBtn.innerHTML = '<i class="fas fa-copy"></i> Copy';
            copyBtn.onclick = () => copyAnalysisResult(result.analysis, copyBtn);

            headerDiv.appendChild(titleDiv);
            headerDiv.appendChild(metaDiv);
            headerDiv.appendChild(copyBtn);

            const contentDiv = document.createElement('div');
            contentDiv.className = 'result-content';
            contentDiv.textContent = result.analysis;

            resultDiv.appendChild(headerDiv);
            resultDiv.appendChild(contentDiv);

            if (isFollowUp) {
                resultsSection.appendChild(resultDiv);
            } else {
                resultsSection.innerHTML = '';
                resultsSection.appendChild(resultDiv);
            }

            // Scroll to new result
            resultDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

            console.log('✅ Analysis result displayed');
        }

        function copyAnalysisResult(text, button) {
            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i> Copied!';
                button.classList.add('copied');

                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.remove('copied');
                }, 2000);

                console.log('📋 Analysis result copied to clipboard');
            }).catch(err => {
                console.error('Failed to copy text:', err);
                showStatusMessage('Failed to copy to clipboard', 'error');
            });
        }

        function showLoading() {
            document.getElementById('loadingSection').style.display = 'block';
        }

        function hideLoading() {
            document.getElementById('loadingSection').style.display = 'none';
        }

        function updateCaptureButton(isLoading) {
            const button = document.getElementById('mainCaptureBtn');
            if (isLoading) {
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Analyzing...';
            } else {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-camera-retro"></i> What\'s on my screen?';
            }
        }

        function enableFollowUpChat() {
            document.getElementById('chatInput').disabled = false;
            document.getElementById('chatSendBtn').disabled = false;
        }

        function clearResults() {
            document.getElementById('resultsSection').innerHTML = '';
            document.getElementById('chatInput').disabled = true;
            document.getElementById('chatSendBtn').disabled = true;
            document.getElementById('chatInput').value = '';
        }

        function showStatusMessage(message, type) {
            // Remove existing status messages
            const existingMessages = document.querySelectorAll('.status-message');
            existingMessages.forEach(msg => msg.remove());

            const statusDiv = document.createElement('div');
            statusDiv.className = `status-message ${type}`;

            let icon = '';
            switch(type) {
                case 'error':
                    icon = '<i class="fas fa-exclamation-triangle"></i>';
                    break;
                case 'success':
                    icon = '<i class="fas fa-check-circle"></i>';
                    break;
                case 'warning':
                    icon = '<i class="fas fa-exclamation-circle"></i>';
                    break;
                default:
                    icon = '<i class="fas fa-info-circle"></i>';
            }

            statusDiv.innerHTML = `${icon} ${message}`;

            document.querySelector('.screen-analyzer-container').appendChild(statusDiv);

            // Auto-remove success and warning messages after 5 seconds
            if (type === 'success' || type === 'warning') {
                setTimeout(() => {
                    if (statusDiv.parentNode) {
                        statusDiv.remove();
                    }
                }, 5000);
            }
        }

        async function testApiConnection() {
            try {
                const response = await fetch(`http://127.0.0.1:${apiPort}/api/screen-capture/test`);
                const result = await response.json();

                if (result.gemini_available) {
                    console.log('✅ Gemini Vision API is ready');
                    showStatusMessage('Gemini Vision API connected successfully! 🚀', 'success');
                } else {
                    console.warn('⚠️ Gemini Vision API not available');
                    showStatusMessage('Warning: Gemini Vision API is not available. Please check your API configuration.', 'warning');
                }
            } catch (error) {
                console.error('❌ API connection test failed:', error);
                showStatusMessage('Could not connect to backend API. Please ensure the server is running.', 'error');
            }
        }

        // Voice command simulation (for demo purposes)
        function simulateVoiceCommand(command) {
            console.log(`🎤 Voice command: "${command}"`);

            const lowerCommand = command.toLowerCase();
            if (lowerCommand.includes('what\'s on my screen') ||
                lowerCommand.includes('analyze my screen') ||
                lowerCommand.includes('screen capture')) {
                captureAndAnalyzeScreen();
            }
        }

        // Expose functions globally for external access
        window.captureAndAnalyzeScreen = captureAndAnalyzeScreen;
        window.simulateVoiceCommand = simulateVoiceCommand;
        window.selectAnalysisType = selectAnalysisType;
        window.sendFollowUpQuestion = sendFollowUpQuestion;

        // Add some visual feedback for user interactions
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('option-card')) {
                // Add click animation
                e.target.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    e.target.style.transform = '';
                }, 150);
            }
        });

        console.log('🎯 Gini 1.5 Screen Analyzer ready!');
        console.log('💡 Keyboard shortcuts:');
        console.log('   - Ctrl+Enter: Capture and analyze screen');
        console.log('   - Escape: Clear results');
        console.log('   - Enter in chat: Send follow-up question');
    </script>
</body>
</html>