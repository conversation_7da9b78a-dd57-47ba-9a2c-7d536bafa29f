<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Widget - Nova AI</title>
    <!-- FontAwesome for better icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: radial-gradient(ellipse at 30% 50%, #0C294F 0%, #061D3B 70%, #041529 100%);
            font-family: 'Orbitron', 'Segoe UI', monospace;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        /* Enhanced Camera Widget with consistent styling - COMPACT SIZE */
        .camera-widget {
            position: absolute;
            top: 20vh;
            left: 50%;
            transform: translateX(-50%);
            min-width: 350px;
            max-width: 450px;
            width: 380px;
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.15) 0%, rgba(0, 200, 255, 0.1) 100%);
            border: 1px solid rgba(0, 255, 255, 0.4);
            border-radius: 10px;
            font-family: 'Orbitron', monospace;
            color: white;
            box-shadow:
                0 0 15px rgba(0, 255, 255, 0.3),
                inset 0 0 10px rgba(0, 255, 255, 0.1);
            backdrop-filter: blur(8px);
            animation: cameraGlow 4s ease-in-out infinite;
            z-index: 25;
            display: block;
        }

        /* Corner brackets for visual consistency - matching other widgets */
        .camera-widget::before,
        .camera-widget::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.9);
            z-index: 1;
        }

        .camera-widget::before {
            top: -6px;
            left: -6px;
            border-right: none;
            border-bottom: none;
        }

        .camera-widget::after {
            bottom: -6px;
            right: -6px;
            border-left: none;
            border-top: none;
        }

        .camera-widget .corner-top-right {
            position: absolute;
            top: -6px;
            right: -6px;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.9);
            border-left: none;
            border-bottom: none;
            z-index: 1;
        }

        .camera-widget .corner-bottom-left {
            position: absolute;
            bottom: -6px;
            left: -6px;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.9);
            border-right: none;
            border-top: none;
            z-index: 1;
        }

        /* Header Section - matching other widget styling */
        .camera-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px 12px 24px;
            border-bottom: 1px solid rgba(0, 255, 255, 0.3);
            position: relative;
        }

        .camera-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .camera-logo {
            width: 28px;
            height: 28px;
            background: linear-gradient(135deg, #00FFFF 0%, #00CCFF 100%);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 900;
            font-size: 14px;
            color: #041529;
            box-shadow: 0 4px 12px rgba(0, 255, 255, 0.4);
        }

        .camera-brand {
            font-size: 16px;
            font-weight: 700;
            color: white;
            letter-spacing: 2px;
            text-transform: uppercase;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.6);
        }

        .camera-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 10px;
            font-weight: 500;
            letter-spacing: 1px;
            color: rgba(255, 255, 255, 0.8);
        }

        .camera-status .status-indicator {
            width: 6px;
            height: 6px;
            background: #00FFFF;
            border-radius: 50%;
            animation: pulse 2s infinite;
            box-shadow: 0 0 8px #00FFFF;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(0.8); }
        }

        /* Content Area - matching other widget styling - COMPACT */
        .camera-content {
            padding: 15px 18px 18px 18px;
            min-height: 280px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow-y: auto;
            max-height: 60vh;
        }

        .camera-viewport {
            position: relative;
            width: 100%;
            height: 220px;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid rgba(0, 255, 255, 0.3);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .camera-placeholder {
            text-align: center;
            color: rgba(0, 255, 255, 0.7);
            font-size: 16px;
            font-family: 'Orbitron', monospace;
        }

        .camera-feed {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: none;
            border-radius: 10px;
        }

        /* Widget Controls - matching other widgets */
        .widget-controls {
            position: absolute;
            top: 12px;
            right: 12px;
            display: flex;
            gap: 6px;
            z-index: 30;
        }

        .widget-control-btn {
            width: 28px;
            height: 28px;
            background: rgba(0, 0, 0, 0.6);
            border: 1px solid rgba(0, 255, 255, 0.4);
            border-radius: 6px;
            color: rgba(0, 255, 255, 0.8);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .widget-control-btn:hover {
            background: rgba(0, 255, 255, 0.2);
            border-color: rgba(0, 255, 255, 0.6);
            color: white;
            transform: scale(1.1);
        }

        /* Camera Controls - COMPACT */
        .camera-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .control-btn {
            width: 40px;
            height: 40px;
            border: 2px solid rgba(0, 255, 255, 0.4);
            border-radius: 50%;
            background: linear-gradient(145deg, rgba(0, 255, 255, 0.2), rgba(0, 200, 255, 0.1));
            color: rgba(0, 255, 255, 0.9);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
        }

        .control-btn:hover {
            transform: scale(1.1);
            border-color: rgba(0, 255, 255, 0.6);
            background: linear-gradient(145deg, rgba(0, 255, 255, 0.3), rgba(0, 200, 255, 0.2));
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
        }

        .control-btn:active {
            transform: scale(0.95);
        }

        .control-btn.record {
            border-color: rgba(255, 51, 102, 0.4);
            color: rgba(255, 51, 102, 0.9);
            background: linear-gradient(145deg, rgba(255, 51, 102, 0.2), rgba(204, 17, 68, 0.1));
        }

        .control-btn.record:hover {
            border-color: rgba(255, 51, 102, 0.6);
            background: linear-gradient(145deg, rgba(255, 51, 102, 0.3), rgba(204, 17, 68, 0.2));
            box-shadow: 0 0 15px rgba(255, 51, 102, 0.3);
        }

        .control-btn.capture {
            border-color: rgba(0, 255, 136, 0.4);
            color: rgba(0, 255, 136, 0.9);
            background: linear-gradient(145deg, rgba(0, 255, 136, 0.2), rgba(0, 204, 102, 0.1));
        }

        .control-btn.capture:hover {
            border-color: rgba(0, 255, 136, 0.6);
            background: linear-gradient(145deg, rgba(0, 255, 136, 0.3), rgba(0, 204, 102, 0.2));
            box-shadow: 0 0 15px rgba(0, 255, 136, 0.3);
        }

        /* Info Panel */
        .info-panel {
            position: absolute;
            top: 80px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: rgba(0, 255, 255, 0.9);
            padding: 12px;
            border-radius: 8px;
            font-size: 11px;
            font-family: 'Orbitron', monospace;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 255, 255, 0.3);
            display: none;
            z-index: 25;
        }

        .processing-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: rgba(0, 255, 255, 0.9);
            font-size: 16px;
            font-family: 'Orbitron', monospace;
            display: none;
            z-index: 30;
            text-align: center;
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #00ffff;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @keyframes cameraGlow {
            0%, 100% {
                box-shadow:
                    0 0 20px rgba(0, 255, 255, 0.3),
                    inset 0 0 15px rgba(0, 255, 255, 0.1);
            }
            50% {
                box-shadow:
                    0 0 30px rgba(0, 255, 255, 0.5),
                    inset 0 0 20px rgba(0, 255, 255, 0.2);
            }
        }

        .filters-panel {
            position: absolute;
            top: 80px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 255, 255, 0.3);
            display: none;
            z-index: 25;
        }

        .filter-btn {
            display: block;
            width: 100%;
            padding: 8px 12px;
            margin-bottom: 8px;
            background: transparent;
            border: 1px solid rgba(0, 255, 255, 0.4);
            color: rgba(0, 255, 255, 0.8);
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            font-family: 'Orbitron', monospace;
            transition: all 0.3s ease;
        }

        .filter-btn:hover {
            background: rgba(0, 255, 255, 0.2);
            border-color: rgba(0, 255, 255, 0.6);
            color: white;
        }

        .filter-btn.active {
            background: rgba(0, 255, 255, 0.3);
            border-color: rgba(0, 255, 255, 0.8);
            color: white;
        }
    </style>
</head>
<body>
    <div class="camera-widget widget-draggable widget-size-normal" id="cameraWidget">
        <div class="corner-top-right"></div>
        <div class="corner-bottom-left"></div>

        <!-- Widget Controls -->
        <div class="widget-controls">
            <div class="widget-control-btn" onclick="increaseWidgetSize('cameraWidget')" title="Make Bigger">⧨</div>
            <div class="widget-control-btn" onclick="decreaseWidgetSize('cameraWidget')" title="Make Smaller">⧩</div>
            <div class="widget-control-btn" onclick="hideCameraWidget()" title="Close">⧬</div>
        </div>

        <!-- Header -->
        <div class="camera-header">
            <div class="camera-title">
                <div class="camera-logo">📹</div>
                <div class="camera-brand">Camera</div>
            </div>
            <div class="camera-status" id="cameraStatus">
                <div class="status-indicator"></div>
                <span>Ready</span>
            </div>
        </div>

        <!-- Content Area -->
        <div class="camera-content">
            <div class="camera-viewport">
                <div class="camera-placeholder" id="cameraPlaceholder">
                    <div style="font-size: 48px; margin-bottom: 10px;">📹</div>
                    <div>Initializing camera...</div>
                </div>
                <canvas class="camera-feed" id="cameraCanvas"></canvas>

                <div class="processing-indicator" id="processingIndicator">
                    <div class="spinner"></div>
                    Processing...
                </div>
            </div>

            <!-- Camera Controls -->
            <div class="camera-controls">
                <button class="control-btn" id="startBtn" title="Start Camera">
                    <i class="fas fa-play"></i>
                </button>
                <button class="control-btn capture" id="captureBtn" title="Capture Photo">
                    <i class="fas fa-camera"></i>
                </button>
                <button class="control-btn record" id="recordBtn" title="Record Video">
                    <i class="fas fa-circle"></i>
                </button>
                <button class="control-btn" id="filtersBtn" title="Filters">
                    <i class="fas fa-palette"></i>
                </button>
                <button class="control-btn" id="infoBtn" title="Info">
                    <i class="fas fa-info"></i>
                </button>
            </div>
        </div>

        <!-- Info Panel -->
        <div class="info-panel" id="infoPanel">
            <div>Resolution: 640x480</div>
            <div>FPS: 30</div>
            <div>Filter: None</div>
            <div>Status: Ready</div>
        </div>

        <!-- Filters Panel -->
        <div class="filters-panel" id="filtersPanel">
            <button class="filter-btn active" data-filter="none">Normal</button>
            <button class="filter-btn" data-filter="gray">Grayscale</button>
            <button class="filter-btn" data-filter="blur">Blur</button>
            <button class="filter-btn" data-filter="edge">Edge Detection</button>
            <button class="filter-btn" data-filter="cartoon">Cartoon</button>
        </div>
    </div>

    <script>
        // ================= CAMERA WIDGET MANAGEMENT =================

        // Widget management functions
        function showCameraWidget() {
            const widget = document.getElementById('cameraWidget');
            widget.style.display = 'block';
            initializeCameraWidget();
            console.log('📹 Camera widget shown');
        }

        function hideCameraWidget() {
            const widget = document.getElementById('cameraWidget');
            widget.style.display = 'none';
            if (window.cameraWidgetInstance) {
                window.cameraWidgetInstance.stopCamera();
            }
            console.log('📹 Camera widget hidden');
        }

        // Widget size management (placeholder functions - integrate with main system)
        function increaseWidgetSize(widgetId) {
            console.log(`📹 Increasing size for ${widgetId}`);
            // This would integrate with the main widget sizing system
        }

        function decreaseWidgetSize(widgetId) {
            console.log(`📹 Decreasing size for ${widgetId}`);
            // This would integrate with the main widget sizing system
        }

        // ================= CAMERA WIDGET CLASS =================

        class CameraWidget {
            constructor() {
                this.canvas = document.getElementById('cameraCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.video = document.createElement('video');
                this.stream = null;
                this.isRecording = false;
                this.currentFilter = 'none';
                this.animationId = null;

                this.initializeElements();
                this.setupEventListeners();
                this.updateStatus('Ready');
            }

            initializeElements() {
                this.startBtn = document.getElementById('startBtn');
                this.captureBtn = document.getElementById('captureBtn');
                this.recordBtn = document.getElementById('recordBtn');
                this.filtersBtn = document.getElementById('filtersBtn');
                this.infoBtn = document.getElementById('infoBtn');
                this.infoPanel = document.getElementById('infoPanel');
                this.filtersPanel = document.getElementById('filtersPanel');
                this.processingIndicator = document.getElementById('processingIndicator');
                this.cameraStatus = document.getElementById('cameraStatus');
                this.cameraPlaceholder = document.getElementById('cameraPlaceholder');

                this.canvas.width = 480;
                this.canvas.height = 320;
            }

            setupEventListeners() {
                this.startBtn.addEventListener('click', () => this.toggleCamera());
                this.captureBtn.addEventListener('click', () => this.capturePhoto());
                this.recordBtn.addEventListener('click', () => this.toggleRecording());
                this.filtersBtn.addEventListener('click', () => this.toggleFilters());
                this.infoBtn.addEventListener('click', () => this.toggleInfo());

                document.querySelectorAll('.filter-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => this.setFilter(e.target.dataset.filter));
                });
            }

            updateStatus(status) {
                if (this.cameraStatus) {
                    const statusText = this.cameraStatus.querySelector('span');
                    if (statusText) {
                        statusText.textContent = status;
                    }
                }
            }

            // Check camera availability before attempting to start
            async checkCameraAvailability() {
                try {
                    const devices = await navigator.mediaDevices.enumerateDevices();
                    const videoDevices = devices.filter(device => device.kind === 'videoinput');

                    if (videoDevices.length === 0) {
                        throw new Error('No camera devices found');
                    }

                    console.log(`📹 Found ${videoDevices.length} camera device(s)`);
                    return true;
                } catch (error) {
                    console.error('📹 Camera availability check failed:', error);
                    return false;
                }
            }

            // Auto-start camera method for seamless experience
            async autoStartCamera() {
                if (this.stream) {
                    console.log('📹 Camera already active');
                    return;
                }

                // Check if camera is available first
                const cameraAvailable = await this.checkCameraAvailability();
                if (!cameraAvailable) {
                    this.handleCameraError(new Error('No camera devices found'));
                    return;
                }

                try {
                    this.updateStatus('Initializing camera...');
                    // Starting camera feed...

                    // Request camera access with compact settings
                    this.stream = await navigator.mediaDevices.getUserMedia({
                        video: {
                            width: { ideal: 480 },
                            height: { ideal: 320 },
                            facingMode: 'user' // Prefer front-facing camera
                        },
                        audio: false // No audio needed for video feed
                    });

                    this.video.srcObject = this.stream;
                    this.video.play();

                    this.video.addEventListener('loadedmetadata', () => {
                        this.startVideoProcessing();
                        console.log('📹 Camera ready');

                        // Hide any loading indicators
                        this.hideProcessing();
                    });

                    // Update UI to reflect active camera
                    this.startBtn.innerHTML = '<i class="fas fa-pause"></i>';
                    this.canvas.style.display = 'block';
                    this.cameraPlaceholder.style.display = 'none';
                    this.updateStatus('Camera active');

                } catch (error) {
                    console.error('📹 Auto-start camera error:', error);
                    this.handleCameraError(error);
                }
            }

            // Enhanced error handling for camera access
            handleCameraError(error) {
                let errorMessage = 'Camera access failed';
                let statusMessage = 'Camera unavailable';

                if (error.name === 'NotAllowedError') {
                    errorMessage = 'Camera access denied. Please allow camera permissions and try again.';
                    statusMessage = 'Permission denied';
                } else if (error.name === 'NotFoundError') {
                    errorMessage = 'No camera found. Please connect a camera and try again.';
                    statusMessage = 'No camera found';
                } else if (error.name === 'NotReadableError') {
                    errorMessage = 'Camera is being used by another application.';
                    statusMessage = 'Camera in use';
                } else if (error.name === 'OverconstrainedError') {
                    errorMessage = 'Camera settings not supported. Trying with default settings...';
                    statusMessage = 'Retrying...';

                    // Try with more basic settings
                    setTimeout(() => this.fallbackCameraStart(), 1000);
                    return;
                }

                this.updateStatus(statusMessage);
                this.showCameraErrorMessage(errorMessage);
            }

            // Fallback camera start with basic settings
            async fallbackCameraStart() {
                try {
                    // Attempting fallback camera start...
                    this.updateStatus('Retrying camera...');

                    this.stream = await navigator.mediaDevices.getUserMedia({
                        video: true // Use basic video constraints
                    });

                    this.video.srcObject = this.stream;
                    this.video.play();

                    this.video.addEventListener('loadedmetadata', () => {
                        this.startVideoProcessing();
                        // Don't log again - already logged in main start
                    });

                    this.startBtn.innerHTML = '<i class="fas fa-pause"></i>';
                    this.canvas.style.display = 'block';
                    this.cameraPlaceholder.style.display = 'none';
                    this.updateStatus('Camera active');

                } catch (fallbackError) {
                    console.error('📹 Fallback camera start failed:', fallbackError);
                    this.updateStatus('Camera failed');
                    this.showCameraErrorMessage('Unable to access camera. Please check your camera settings and permissions.');
                }
            }

            // Show user-friendly error message
            showCameraErrorMessage(message) {
                // Update placeholder with error message
                this.cameraPlaceholder.innerHTML = `
                    <div style="color: rgba(255, 51, 102, 0.9); text-align: center;">
                        <div style="font-size: 48px; margin-bottom: 15px;">⚠️</div>
                        <div style="font-size: 14px; margin-bottom: 10px;">Camera Error</div>
                        <div style="font-size: 12px; line-height: 1.4; max-width: 300px; margin: 0 auto;">${message}</div>
                        <button onclick="window.cameraWidgetInstance.retryCamera()"
                                style="margin-top: 15px; padding: 8px 16px; background: rgba(0, 255, 255, 0.2);
                                       border: 1px solid rgba(0, 255, 255, 0.4); color: rgba(0, 255, 255, 0.9);
                                       border-radius: 6px; cursor: pointer; font-family: 'Orbitron', monospace;">
                            Retry Camera
                        </button>
                    </div>
                `;
                this.cameraPlaceholder.style.display = 'block';
                this.canvas.style.display = 'none';
            }

            // Retry camera method
            async retryCamera() {
                console.log('📹 Retrying camera access...');
                this.cameraPlaceholder.innerHTML = `
                    <div style="font-size: 48px; margin-bottom: 10px;">📹</div>
                    <div>Initializing camera...</div>
                `;
                await this.autoStartCamera();
            }

            async toggleCamera() {
                if (!this.stream) {
                    // Use the auto-start method for consistency
                    await this.autoStartCamera();
                } else {
                    this.stopCamera();
                }
            }

            startVideoProcessing() {
                const processFrame = () => {
                    if (this.stream && this.video.readyState === this.video.HAVE_ENOUGH_DATA) {
                        this.ctx.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);
                        
                        // Apply OpenCV-style filters
                        this.applyFilter();
                        
                        // Add frame overlay effects
                        this.addFrameEffects();
                    }
                    
                    this.animationId = requestAnimationFrame(processFrame);
                };
                
                processFrame();
            }

            applyFilter() {
                const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
                const data = imageData.data;
                
                switch(this.currentFilter) {
                    case 'gray':
                        this.applyGrayscale(data);
                        break;
                    case 'blur':
                        this.applyBlur();
                        break;
                    case 'edge':
                        this.applyEdgeDetection(data);
                        break;
                    case 'cartoon':
                        this.applyCartoonEffect(data);
                        break;
                }
                
                if (this.currentFilter !== 'none') {
                    this.ctx.putImageData(imageData, 0, 0);
                }
            }

            applyGrayscale(data) {
                for (let i = 0; i < data.length; i += 4) {
                    const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
                    data[i] = gray;
                    data[i + 1] = gray;
                    data[i + 2] = gray;
                }
            }

            applyBlur() {
                this.ctx.filter = 'blur(3px)';
                this.ctx.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);
                this.ctx.filter = 'none';
            }

            applyEdgeDetection(data) {
                // Simple edge detection using Sobel-like filter
                const width = this.canvas.width;
                const height = this.canvas.height;
                const newData = new Uint8ClampedArray(data);
                
                for (let y = 1; y < height - 1; y++) {
                    for (let x = 1; x < width - 1; x++) {
                        const idx = (y * width + x) * 4;
                        
                        // Calculate gradient
                        const gx = (
                            -data[((y-1) * width + (x-1)) * 4] + data[((y-1) * width + (x+1)) * 4] +
                            -2 * data[(y * width + (x-1)) * 4] + 2 * data[(y * width + (x+1)) * 4] +
                            -data[((y+1) * width + (x-1)) * 4] + data[((y+1) * width + (x+1)) * 4]
                        );
                        
                        const gy = (
                            -data[((y-1) * width + (x-1)) * 4] - 2 * data[((y-1) * width + x) * 4] - data[((y-1) * width + (x+1)) * 4] +
                            data[((y+1) * width + (x-1)) * 4] + 2 * data[((y+1) * width + x) * 4] + data[((y+1) * width + (x+1)) * 4]
                        );
                        
                        const magnitude = Math.sqrt(gx * gx + gy * gy);
                        const value = magnitude > 50 ? 255 : 0;
                        
                        newData[idx] = value;
                        newData[idx + 1] = value;
                        newData[idx + 2] = value;
                    }
                }
                
                for (let i = 0; i < data.length; i++) {
                    data[i] = newData[i];
                }
            }

            applyCartoonEffect(data) {
                // Quantize colors for cartoon effect
                const levels = 6;
                const factor = 255 / (levels - 1);
                
                for (let i = 0; i < data.length; i += 4) {
                    data[i] = Math.round(data[i] / factor) * factor;
                    data[i + 1] = Math.round(data[i + 1] / factor) * factor;
                    data[i + 2] = Math.round(data[i + 2] / factor) * factor;
                }
            }

            addFrameEffects() {
                // Add scan lines
                this.ctx.strokeStyle = 'rgba(0, 255, 255, 0.1)';
                this.ctx.lineWidth = 1;
                
                for (let y = 0; y < this.canvas.height; y += 4) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(0, y);
                    this.ctx.lineTo(this.canvas.width, y);
                    this.ctx.stroke();
                }
                
                // Add timestamp
                this.ctx.fillStyle = '#00ffff';
                this.ctx.font = '12px monospace';
                this.ctx.fillText(new Date().toLocaleTimeString(), 10, 25);
            }

            capturePhoto() {
                this.showProcessing();
                
                setTimeout(() => {
                    const dataURL = this.canvas.toDataURL('image/png');
                    const link = document.createElement('a');
                    link.download = `capture_${Date.now()}.png`;
                    link.href = dataURL;
                    link.click();
                    
                    this.hideProcessing();
                }, 500);
            }

            toggleRecording() {
                this.isRecording = !this.isRecording;
                this.recordBtn.innerHTML = this.isRecording ?
                    '<i class="fas fa-stop"></i>' :
                    '<i class="fas fa-circle"></i>';

                if (this.isRecording) {
                    this.updateStatus('Recording...');
                } else {
                    this.updateStatus('Camera active');
                }
            }

            setFilter(filterType) {
                this.currentFilter = filterType;
                
                document.querySelectorAll('.filter-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelector(`[data-filter="${filterType}"]`).classList.add('active');
            }

            toggleFilters() {
                this.filtersPanel.style.display = 
                    this.filtersPanel.style.display === 'block' ? 'none' : 'block';
            }

            toggleInfo() {
                this.infoPanel.style.display = 
                    this.infoPanel.style.display === 'block' ? 'none' : 'block';
            }

            showProcessing() {
                this.processingIndicator.style.display = 'block';
            }

            hideProcessing() {
                this.processingIndicator.style.display = 'none';
            }

            stopCamera() {
                if (this.stream) {
                    this.stream.getTracks().forEach(track => track.stop());
                    this.stream = null;
                }

                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }

                this.startBtn.innerHTML = '<i class="fas fa-play"></i>';
                this.canvas.style.display = 'none';
                this.cameraPlaceholder.style.display = 'block';
                this.updateStatus('Ready');
                this.isRecording = false;
                this.recordBtn.innerHTML = '<i class="fas fa-circle"></i>';
            }
        }

        // ================= INITIALIZATION =================

        function initializeCameraWidget() {
            if (!window.cameraWidgetInstance) {
                window.cameraWidgetInstance = new CameraWidget();
                console.log('📹 Starting...');

                // Auto-start camera for standalone widget
                setTimeout(() => {
                    if (window.cameraWidgetInstance) {
                        // Don't log here - will log when camera is actually ready
                        window.cameraWidgetInstance.autoStartCamera();
                    }
                }, 500);
            }
        }

        // Initialize the camera widget when page loads
        document.addEventListener('DOMContentLoaded', () => {
            initializeCameraWidget();
        });

        // Expose functions globally for integration with main system
        window.showCameraWidget = showCameraWidget;
        window.hideCameraWidget = hideCameraWidget;
        window.initializeCameraWidget = initializeCameraWidget;

        // Python backend simulation (in a real implementation, this would connect to a Flask/FastAPI server)
        const pythonBackend = {
            processFrame: async (imageData, filterType) => {
                // This would send data to Python backend running OpenCV
                console.log(`Processing frame with filter: ${filterType}`);
                return imageData;
            },

            detectFaces: async (imageData) => {
                // Face detection using OpenCV
                console.log('Detecting faces...');
                return [];
            },

            applyAdvancedFilter: async (imageData, filterParams) => {
                // Advanced OpenCV filters
                console.log('Applying advanced filter...');
                return imageData;
            }
        };
    </script>
</body>
</html>