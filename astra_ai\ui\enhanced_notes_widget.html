<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Notes Widget</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&display=swap');
        
        body {
            margin: 0;
            padding: 20px;
            background: radial-gradient(ellipse at 30% 50%, #0C294F 0%, #061D3B 70%, #041529 100%);
            min-height: 100vh;
            font-family: 'Orbitron', 'Segoe UI', monospace;
            overflow-x: hidden;
        }

        /* Main Notes Widget Container */
        .notes-widget {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            min-width: 500px;
            max-width: 700px;
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.15) 0%, rgba(0, 200, 120, 0.1) 100%);
            border: 1px solid rgba(0, 255, 136, 0.4);
            border-radius: 12px;
            font-family: 'Orbitron', monospace;
            color: white;
            box-shadow: 
                0 0 20px rgba(0, 255, 136, 0.3),
                inset 0 0 15px rgba(0, 255, 136, 0.1);
            backdrop-filter: blur(8px);
            animation: notesGlow 4s ease-in-out infinite;
            z-index: 25;
        }

        /* Header Section */
        .notes-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px 12px 24px;
            border-bottom: 1px solid rgba(0, 255, 136, 0.3);
            position: relative;
        }

        .notes-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .notes-logo {
            width: 28px;
            height: 28px;
            background: linear-gradient(135deg, #00FF88 0%, #00CC66 100%);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 900;
            font-size: 14px;
            color: #041529;
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
        }

        .notes-brand {
            font-size: 18px;
            font-weight: 700;
            letter-spacing: 2px;
            color: white;
            text-transform: uppercase;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.6);
        }

        .notes-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 10px;
            font-weight: 500;
            letter-spacing: 1px;
            text-transform: uppercase;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #00FF88;
            animation: pulse 2s infinite;
            box-shadow: 0 0 8px #00FF88;
        }

        /* Navigation Tabs */
        .notes-nav {
            display: flex;
            padding: 0 24px;
            border-bottom: 1px solid rgba(0, 255, 136, 0.2);
            background: rgba(0, 255, 136, 0.05);
        }

        .nav-tab {
            padding: 12px 16px;
            font-size: 11px;
            font-weight: 500;
            letter-spacing: 1px;
            text-transform: uppercase;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
            color: rgba(255, 255, 255, 0.7);
        }

        .nav-tab:hover {
            color: rgba(0, 255, 136, 0.9);
            background: rgba(0, 255, 136, 0.1);
        }

        .nav-tab.active {
            color: #00FF88;
            border-bottom-color: #00FF88;
            background: rgba(0, 255, 136, 0.1);
            text-shadow: 0 0 8px rgba(0, 255, 136, 0.6);
        }

        /* Content Area */
        .notes-content-area {
            padding: 20px 24px;
            min-height: 300px;
            max-height: 450px;
            overflow-y: auto;
        }

        .notes-item {
            margin-bottom: 18px;
            padding: 16px;
            border: 1px solid rgba(0, 255, 136, 0.2);
            border-radius: 8px;
            background: rgba(0, 255, 136, 0.05);
            animation: slideIn 0.5s ease-out;
            transition: all 0.3s ease;
        }

        .notes-item:hover {
            background: rgba(0, 255, 136, 0.1);
            border-color: rgba(0, 255, 136, 0.3);
            transform: translateY(-2px);
        }

        .notes-item:last-child {
            margin-bottom: 0;
        }

        .notes-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            font-size: 10px;
            color: rgba(255, 255, 255, 0.7);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .notes-id {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.4) 0%, rgba(0, 200, 120, 0.3) 100%);
            padding: 3px 8px;
            border-radius: 12px;
            font-weight: 700;
            font-size: 8px;
            border: 1px solid rgba(0, 255, 136, 0.5);
            color: #FFF;
        }

        .notes-time {
            font-weight: 500;
            font-size: 10px;
            color: rgba(0, 255, 136, 0.8);
            text-shadow: 0 0 4px rgba(0, 255, 136, 0.4);
        }

        .notes-preview {
            font-size: 12px;
            line-height: 1.5;
            color: rgba(255, 255, 255, 0.85);
            margin-bottom: 10px;
            text-shadow: 0 0 4px rgba(255, 255, 255, 0.2);
        }

        .notes-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .notes-action-btn {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.3) 0%, rgba(0, 200, 120, 0.2) 100%);
            border: 1px solid rgba(0, 255, 136, 0.4);
            border-radius: 4px;
            color: white;
            padding: 4px 8px;
            cursor: pointer;
            font-size: 10px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .notes-action-btn:hover {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.5) 0%, rgba(0, 200, 120, 0.3) 100%);
            transform: translateY(-1px);
            box-shadow: 0 0 8px rgba(0, 255, 136, 0.4);
        }

        .notes-action-btn.danger {
            background: linear-gradient(135deg, rgba(255, 100, 100, 0.3) 0%, rgba(255, 50, 50, 0.2) 100%);
            border-color: rgba(255, 100, 100, 0.4);
        }

        .notes-action-btn.danger:hover {
            background: linear-gradient(135deg, rgba(255, 100, 100, 0.5) 0%, rgba(255, 50, 50, 0.3) 100%);
            border-color: rgba(255, 100, 100, 0.6);
        }

        /* Empty State */
        .notes-empty {
            text-align: center;
            padding: 40px 20px;
            color: rgba(255, 255, 255, 0.6);
        }

        .notes-empty i {
            font-size: 48px;
            margin-bottom: 16px;
            color: rgba(0, 255, 136, 0.4);
        }

        .notes-empty h3 {
            font-size: 16px;
            margin-bottom: 8px;
            color: rgba(255, 255, 255, 0.8);
        }

        .notes-empty p {
            font-size: 12px;
            line-height: 1.5;
        }

        /* Add Note Form */
        .add-note-form {
            padding: 20px;
            border-top: 1px solid rgba(0, 255, 136, 0.2);
            background: rgba(0, 255, 136, 0.05);
        }

        .add-note-textarea {
            width: 100%;
            min-height: 100px;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 8px;
            color: white;
            font-family: 'Segoe UI', system-ui, sans-serif;
            font-size: 14px;
            padding: 12px;
            resize: vertical;
            box-sizing: border-box;
            margin-bottom: 12px;
        }

        .add-note-textarea:focus {
            outline: none;
            border-color: rgba(0, 255, 136, 0.5);
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
        }

        .add-note-textarea::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .add-note-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .add-note-btn {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.6) 0%, rgba(0, 200, 120, 0.7) 100%);
            border: none;
            border-radius: 6px;
            color: #041529;
            font-weight: 700;
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 11px;
        }

        .add-note-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 15px rgba(0, 255, 136, 0.5);
        }

        .add-note-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .char-count {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.6);
        }

        /* Footer Controls */
        .notes-footer {
            padding: 12px 24px;
            border-top: 1px solid rgba(0, 255, 136, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(0, 255, 136, 0.05);
        }

        .notes-count {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.6);
            font-weight: 500;
        }

        .notes-controls {
            display: flex;
            gap: 8px;
        }

        .control-btn {
            width: 28px;
            height: 28px;
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.3) 0%, rgba(0, 200, 120, 0.2) 100%);
            border: 1px solid rgba(0, 255, 136, 0.4);
            border-radius: 4px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            transition: all 0.3s ease;
            font-family: 'Orbitron', monospace;
        }

        .control-btn:hover {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.5) 0%, rgba(0, 200, 120, 0.3) 100%);
            box-shadow: 0 0 12px rgba(0, 255, 136, 0.4);
            transform: translateY(-1px);
        }

        /* Corner Brackets */
        .notes-widget::before,
        .notes-widget::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.9);
            z-index: 1;
        }

        .notes-widget::before {
            top: -6px;
            left: -6px;
            border-right: none;
            border-bottom: none;
        }

        .notes-widget::after {
            bottom: -6px;
            right: -6px;
            border-left: none;
            border-top: none;
        }

        .corner-top-right {
            position: absolute;
            top: -6px;
            right: -6px;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.9);
            border-left: none;
            border-bottom: none;
            z-index: 1;
        }

        .corner-bottom-left {
            position: absolute;
            bottom: -6px;
            left: -6px;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.9);
            border-right: none;
            border-top: none;
            z-index: 1;
        }

        /* Animations */
        @keyframes notesGlow {
            0%, 100% { 
                box-shadow: 
                    0 0 20px rgba(0, 255, 136, 0.3),
                    inset 0 0 15px rgba(0, 255, 136, 0.1);
            }
            50% { 
                box-shadow: 
                    0 0 35px rgba(0, 255, 136, 0.5),
                    inset 0 0 20px rgba(0, 255, 136, 0.2);
            }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Scrollbar Styling */
        .notes-content-area::-webkit-scrollbar {
            width: 6px;
        }

        .notes-content-area::-webkit-scrollbar-track {
            background: rgba(0, 255, 136, 0.1);
            border-radius: 3px;
        }

        .notes-content-area::-webkit-scrollbar-thumb {
            background: rgba(0, 255, 136, 0.5);
            border-radius: 3px;
        }

        .notes-content-area::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 255, 136, 0.7);
        }

        /* Search Bar */
        .notes-search {
            padding: 12px 24px;
            border-bottom: 1px solid rgba(0, 255, 136, 0.2);
        }

        .search-input {
            width: 100%;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 6px;
            color: white;
            font-size: 12px;
            padding: 8px 12px;
            box-sizing: border-box;
        }

        .search-input:focus {
            outline: none;
            border-color: rgba(0, 255, 136, 0.5);
            box-shadow: 0 0 8px rgba(0, 255, 136, 0.3);
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        /* Note Modal */
        .note-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .note-modal-content {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.15) 0%, rgba(0, 200, 120, 0.1) 100%);
            border: 1px solid rgba(0, 255, 136, 0.4);
            border-radius: 12px;
            padding: 24px;
            max-width: 600px;
            width: 90%;
            max-height: 80%;
            overflow-y: auto;
            color: white;
            font-family: 'Orbitron', monospace;
        }

        .note-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(0, 255, 136, 0.3);
        }

        .note-modal-title {
            font-size: 18px;
            font-weight: 700;
            color: #00FF88;
        }

        .note-modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            line-height: 1;
        }

        .note-modal-close:hover {
            color: #00FF88;
        }

        .note-modal-body {
            font-size: 14px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <!-- Enhanced Notes Widget -->
    <div class="notes-widget" id="notesWidget">
        <div class="corner-top-right"></div>
        <div class="corner-bottom-left"></div>
        
        <!-- Header -->
        <div class="notes-header">
            <div class="notes-title">
                <div class="notes-logo">📝</div>
                <div class="notes-brand">Notes Manager</div>
            </div>
            <div class="notes-status">
                <div class="status-indicator"></div>
                <span>Active</span>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="notes-nav">
            <div class="nav-tab active" onclick="switchTab('view')">View Notes</div>
            <div class="nav-tab" onclick="switchTab('add')">Add Note</div>
        </div>

        <!-- Search Bar -->
        <div class="notes-search" id="notesSearch">
            <input type="text" class="search-input" placeholder="Search notes..." id="searchInput" onkeyup="searchNotes()">
        </div>

        <!-- Content Area -->
        <div class="notes-content-area" id="notesContentArea">
            <!-- Notes will be populated here -->
        </div>

        <!-- Add Note Form -->
        <div class="add-note-form" id="addNoteForm" style="display: none;">
            <textarea class="add-note-textarea" id="addNoteTextarea" placeholder="Write your note here..."></textarea>
            <div class="add-note-controls">
                <div class="char-count" id="charCount">0 characters</div>
                <button class="add-note-btn" id="addNoteBtn" onclick="saveNote()">Save Note</button>
            </div>
        </div>

        <!-- Footer Controls -->
        <div class="notes-footer">
            <div class="notes-count" id="notesCount">0 notes</div>
            <div class="notes-controls">
                <div class="control-btn" onclick="exportNotes()" title="Export Notes">💾</div>
                <div class="control-btn" onclick="clearAllNotes()" title="Clear All">🗑️</div>
            </div>
        </div>
    </div>

    <!-- Note Modal -->
    <div class="note-modal" id="noteModal">
        <div class="note-modal-content">
            <div class="note-modal-header">
                <div class="note-modal-title" id="noteModalTitle">Note Details</div>
                <button class="note-modal-close" onclick="closeNoteModal()">×</button>
            </div>
            <div class="note-modal-body" id="noteModalBody"></div>
        </div>
    </div>

    <script>
        // Constants
        const NOTES_STORAGE_KEY = 'astra_enhanced_notes';
        let currentTab = 'view';
        let allNotes = [];
        let filteredNotes = [];

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadNotes();
            setupEventListeners();
            renderNotes();
        });

        function setupEventListeners() {
            // Add note textarea character count
            const textarea = document.getElementById('addNoteTextarea');
            textarea.addEventListener('input', function() {
                const charCount = this.value.length;
                document.getElementById('charCount').textContent = `${charCount} characters`;
                
                const saveBtn = document.getElementById('addNoteBtn');
                saveBtn.disabled = charCount === 0;
            });

            // Search input
            document.getElementById('searchInput').addEventListener('input', searchNotes);

            // Modal close on click outside
            document.getElementById('noteModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeNoteModal();
                }
            });
        }

        function loadNotes() {
            try {
                const savedNotes = localStorage.getItem(NOTES_STORAGE_KEY);
                allNotes = savedNotes ? JSON.parse(savedNotes) : [];
                filteredNotes = [...allNotes];
            } catch (error) {
                console.error('Error loading notes:', error);
                allNotes = [];
                filteredNotes = [];
            }
        }

        function saveNotesToStorage() {
            try {
                localStorage.setItem(NOTES_STORAGE_KEY, JSON.stringify(allNotes));
            } catch (error) {
                console.error('Error saving notes:', error);
                alert('Error saving notes. Please try again.');
            }
        }

        function saveNote() {
            const content = document.getElementById('addNoteTextarea').value.trim();
            
            if (!content) {
                alert('Please write something before saving!');
                return;
            }

            const newNote = {
                id: Date.now().toString(),
                content: content,
                timestamp: new Date().toISOString(),
                dateCreated: new Date().toLocaleString(),
                preview: content.substring(0, 120) + (content.length > 120 ? '...' : ''),
                wordCount: content.split(/\s+/).filter(word => word.length > 0).length
            };

            allNotes.unshift(newNote);
            filteredNotes = [...allNotes];
            saveNotesToStorage();
            
            // Clear form
            document.getElementById('addNoteTextarea').value = '';
            document.getElementById('charCount').textContent = '0 characters';
            document.getElementById('addNoteBtn').disabled = true;
            
            // Switch to view tab
            switchTab('view');
            renderNotes();
            
            console.log('📝 Note saved successfully:', newNote.id);
        }

        function renderNotes() {
            const contentArea = document.getElementById('notesContentArea');
            
            if (filteredNotes.length === 0) {
                contentArea.innerHTML = `
                    <div class="notes-empty">
                        <i class="fas fa-sticky-note"></i>
                        <h3>No Notes Yet</h3>
                        <p>Click "Add Note" to create your first note!</p>
                    </div>
                `;
            } else {
                contentArea.innerHTML = filteredNotes.map(note => `
                    <div class="notes-item">
                        <div class="notes-meta">
                            <span class="notes-id">ID: ${note.id.slice(-4)}</span>
                            <span class="notes-time">${note.dateCreated}</span>
                        </div>
                        <div class="notes-preview">${note.preview}</div>
                        <div class="notes-actions">
                            <button class="notes-action-btn" onclick="viewNote('${note.id}')">
                                <i class="fas fa-eye"></i> View
                            </button>
                            <button class="notes-action-btn" onclick="editNote('${note.id}')">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            <button class="notes-action-btn" onclick="copyNote('${note.id}')">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                            <button class="notes-action-btn danger" onclick="deleteNote('${note.id}')">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </div>
                    </div>
                `).join('');
            }
            
            updateNotesCount();
        }

        function switchTab(tabName) {
            // Update active tab
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // Show/hide content based on tab
            const contentArea = document.getElementById('notesContentArea');
            const addNoteForm = document.getElementById('addNoteForm');
            const searchArea = document.getElementById('notesSearch');

            if (tabName === 'view') {
                contentArea.style.display = 'block';
                addNoteForm.style.display = 'none';
                searchArea.style.display = 'block';
                renderNotes();
            } else if (tabName === 'add') {
                contentArea.style.display = 'none';
                addNoteForm.style.display = 'block';
                searchArea.style.display = 'none';
                document.getElementById('addNoteTextarea').focus();
            }

            currentTab = tabName;
        }

        function searchNotes() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            
            if (searchTerm === '') {
                filteredNotes = [...allNotes];
            } else {
                filteredNotes = allNotes.filter(note => 
                    note.content.toLowerCase().includes(searchTerm) ||
                    note.dateCreated.toLowerCase().includes(searchTerm)
                );
            }
            
            renderNotes();
        }

        function viewNote(noteId) {
            const note = allNotes.find(n => n.id === noteId);
            if (note) {
                document.getElementById('noteModalTitle').textContent = `Note - ${note.dateCreated}`;
                document.getElementById('noteModalBody').textContent = note.content;
                document.getElementById('noteModal').style.display = 'flex';
            }
        }

        function editNote(noteId) {
            const note = allNotes.find(n => n.id === noteId);
            if (note) {
                document.getElementById('addNoteTextarea').value = note.content;
                document.getElementById('charCount').textContent = `${note.content.length} characters`;
                document.getElementById('addNoteBtn').disabled = false;
                
                // Remove the old note
                allNotes = allNotes.filter(n => n.id !== noteId);
                filteredNotes = [...allNotes];
                saveNotesToStorage();
                
                // Switch to add tab
                switchTab('add');
            }
        }

        function copyNote(noteId) {
            const note = allNotes.find(n => n.id === noteId);
            if (note) {
                navigator.clipboard.writeText(note.content).then(() => {
                    alert('Note copied to clipboard!');
                }).catch(err => {
                    console.error('Failed to copy text:', err);
                    alert('Failed to copy note. Please try again.');
                });
            }
        }

        function deleteNote(noteId) {
            if (confirm('Are you sure you want to delete this note?')) {
                allNotes = allNotes.filter(n => n.id !== noteId);
                filteredNotes = [...allNotes];
                saveNotesToStorage();
                renderNotes();
                console.log('📝 Note deleted:', noteId);
            }
        }

        function updateNotesCount() {
            const count = filteredNotes.length;
            document.getElementById('notesCount').textContent = `${count} note${count !== 1 ? 's' : ''}`;
        }

        function exportNotes() {
            if (allNotes.length === 0) {
                alert('No notes to export!');
                return;
            }

            const exportData = {
                exportDate: new Date().toISOString(),
                totalNotes: allNotes.length,
                notes: allNotes
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `notes-export-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            URL.revokeObjectURL(url);
            console.log('📝 Notes exported successfully');
        }

        function clearAllNotes() {
            if (allNotes.length === 0) {
                alert('No notes to clear!');
                return;
            }

            if (confirm(`Are you sure you want to delete all ${allNotes.length} notes? This action cannot be undone.`)) {
                allNotes = [];
                filteredNotes = [];
                saveNotesToStorage();
                renderNotes();
                console.log('📝 All notes cleared');
            }
        }

        function closeNoteModal() {
            document.getElementById('noteModal').style.display = 'none';
        }

        // Initialize the widget
        setTimeout(() => {
            console.log('📝 Enhanced Notes Widget initialized');
        }, 100);
    </script>
</body>
</html>