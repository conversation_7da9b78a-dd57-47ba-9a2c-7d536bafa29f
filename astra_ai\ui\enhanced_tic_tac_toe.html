<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Tic-Tac-Toe Widget</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', 'Segoe UI', monospace;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        /* ================= TIC-TAC-TOE WIDGET STYLES ================= */

        .tictactoe-widget {
            position: relative;
            width: clamp(350px, 50vw, 600px);
            min-height: clamp(400px, 60vh, 700px);
            max-width: 90vw;
            max-height: 95vh;
            overflow: hidden;
            
            /* Styling matching other widgets */
            z-index: 30;
            background: rgba(0, 255, 255, 0.05);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 15px;
            color: white;
            box-shadow: 
                0 0 20px rgba(0, 255, 255, 0.2),
                inset 0 0 15px rgba(0, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            animation: ticTacToeGlow 4s ease-in-out infinite alternate;
            display: flex;
            flex-direction: column;
            user-select: none;
            cursor: move;
        }

        /* Corner Brackets */
        .tictactoe-widget::before,
        .tictactoe-widget::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(0, 255, 255, 0.9);
            z-index: 1;
        }

        .tictactoe-widget::before {
            top: -6px;
            left: -6px;
            border-right: none;
            border-bottom: none;
        }

        .tictactoe-widget::after {
            bottom: -6px;
            right: -6px;
            border-left: none;
            border-top: none;
        }

        /* Widget Controls */
        .widget-controls {
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            gap: 4px;
            z-index: 100;
        }

        .widget-control-btn {
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.2) 0%, rgba(0, 200, 255, 0.15) 100%);
            border: 1px solid rgba(0, 255, 255, 0.4);
            border-radius: 4px;
            color: rgba(0, 255, 255, 0.9);
            font-size: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
        }

        .widget-control-btn:hover {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.3) 0%, rgba(0, 200, 255, 0.25) 100%);
            border-color: rgba(0, 255, 255, 0.6);
            box-shadow: 0 0 8px rgba(0, 255, 255, 0.4);
            transform: scale(1.1);
        }

        /* Resize Handle */
        .resize-handle {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 16px;
            height: 16px;
            cursor: se-resize;
            background: linear-gradient(135deg, transparent 0%, rgba(0, 255, 255, 0.3) 100%);
            border-top-left-radius: 4px;
        }

        .resize-handle::after {
            content: '';
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-bottom: 6px solid rgba(0, 255, 255, 0.6);
        }

        /* Header Section */
        .tictactoe-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: clamp(12px, calc(16px + 0.3vw), 24px) clamp(16px, calc(20px + 0.4vw), 32px);
            border-bottom: 1px solid rgba(0, 255, 255, 0.2);
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.1) 0%, rgba(0, 200, 255, 0.05) 100%);
        }

        .tictactoe-title {
            display: flex;
            align-items: center;
            gap: clamp(8px, calc(12px + 0.2vw), 16px);
        }

        .tictactoe-logo {
            font-size: clamp(16px, calc(20px + 0.3vw), 28px);
            filter: drop-shadow(0 0 8px rgba(0, 255, 255, 0.6));
        }

        .tictactoe-brand {
            font-size: clamp(14px, calc(16px + 0.2vw), 20px);
            font-weight: 600;
            color: rgba(0, 255, 255, 0.9);
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
            letter-spacing: 1px;
        }

        .tictactoe-status {
            display: flex;
            align-items: center;
            gap: clamp(6px, calc(8px + 0.1vw), 12px);
            font-size: clamp(10px, calc(12px + 0.1vw), 14px);
            color: rgba(255, 255, 255, 0.8);
        }

        .tictactoe-status .status-indicator {
            width: clamp(6px, calc(8px + 0.1vw), 10px);
            height: clamp(6px, calc(8px + 0.1vw), 10px);
            background: #00FF88;
            border-radius: 50%;
            box-shadow: 0 0 8px rgba(0, 255, 136, 0.6);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.2); }
        }

        /* Game Statistics */
        .game-stats {
            display: flex;
            justify-content: space-around;
            padding: 8px 16px;
            background: rgba(0, 255, 255, 0.05);
            border-bottom: 1px solid rgba(0, 255, 255, 0.1);
            font-size: 11px;
            color: rgba(255, 255, 255, 0.7);
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-weight: 600;
            color: rgba(0, 255, 255, 0.9);
            font-size: 14px;
        }

        /* Mode Selection Styles */
        .tictactoe-mode-selection {
            padding: clamp(20px, calc(24px + 0.4vw), 32px);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: clamp(20px, calc(24px + 0.4vw), 32px);
            flex: 1;
            justify-content: center;
        }

        .mode-selection-title {
            font-size: clamp(16px, calc(18px + 0.3vw), 24px);
            font-weight: 600;
            color: rgba(0, 255, 255, 0.9);
            text-align: center;
            margin-bottom: clamp(8px, calc(12px + 0.2vw), 16px);
        }

        .mode-buttons {
            display: flex;
            gap: clamp(16px, calc(20px + 0.3vw), 28px);
            flex-wrap: wrap;
            justify-content: center;
        }

        .mode-btn {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.15) 0%, rgba(0, 200, 255, 0.1) 100%);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 12px;
            padding: clamp(16px, calc(20px + 0.3vw), 28px);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: clamp(8px, calc(10px + 0.2vw), 14px);
            min-width: clamp(120px, calc(140px + 2vw), 180px);
            font-family: 'Orbitron', monospace;
        }

        .mode-btn:hover {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.25) 0%, rgba(0, 200, 255, 0.2) 100%);
            border-color: rgba(0, 255, 255, 0.5);
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .mode-icon {
            font-size: clamp(24px, calc(28px + 0.4vw), 36px);
            filter: drop-shadow(0 0 8px rgba(0, 255, 255, 0.6));
        }

        .mode-text {
            font-size: clamp(12px, calc(14px + 0.2vw), 18px);
            font-weight: 600;
            color: rgba(0, 255, 255, 0.9);
        }

        .mode-desc {
            font-size: clamp(9px, calc(11px + 0.1vw), 13px);
            color: rgba(255, 255, 255, 0.7);
            text-align: center;
        }

        /* Difficulty Selection */
        .difficulty-selection {
            display: none;
            flex-direction: column;
            align-items: center;
            gap: 16px;
            padding: 20px;
        }

        .difficulty-title {
            font-size: 18px;
            color: rgba(0, 255, 255, 0.9);
            margin-bottom: 8px;
        }

        .difficulty-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .difficulty-btn {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.1) 0%, rgba(0, 200, 255, 0.05) 100%);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 8px;
            padding: 12px 20px;
            color: rgba(0, 255, 255, 0.9);
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Orbitron', monospace;
            font-size: 12px;
        }

        .difficulty-btn:hover,
        .difficulty-btn.selected {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.2) 0%, rgba(0, 200, 255, 0.15) 100%);
            border-color: rgba(0, 255, 255, 0.5);
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }

        /* Game Board Styles */
        .tictactoe-game-board {
            padding: clamp(16px, calc(20px + 0.3vw), 28px);
            display: none;
            flex-direction: column;
            gap: clamp(16px, calc(20px + 0.3vw), 28px);
            flex: 1;
        }

        .game-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: clamp(8px, calc(12px + 0.2vw), 16px);
            background: rgba(0, 255, 255, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(0, 255, 255, 0.2);
        }

        .current-player {
            font-size: clamp(12px, calc(14px + 0.2vw), 18px);
            font-weight: 600;
            color: rgba(0, 255, 255, 0.9);
        }

        .game-mode-display {
            font-size: clamp(10px, calc(12px + 0.1vw), 14px);
            color: rgba(255, 255, 255, 0.7);
        }

        .game-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: clamp(4px, calc(6px + 0.1vw), 8px);
            aspect-ratio: 1;
            max-width: clamp(250px, calc(300px + 5vw), 400px);
            margin: 0 auto;
        }

        .grid-cell {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.1) 0%, rgba(0, 200, 255, 0.05) 100%);
            border: 2px solid rgba(0, 255, 255, 0.3);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: clamp(24px, calc(32px + 1vw), 48px);
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            color: rgba(0, 255, 255, 0.9);
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
            position: relative;
            overflow: hidden;
        }

        .grid-cell::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .grid-cell:hover:not(.occupied)::before {
            left: 100%;
        }

        .grid-cell:hover:not(.occupied) {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.2) 0%, rgba(0, 200, 255, 0.15) 100%);
            border-color: rgba(0, 255, 255, 0.5);
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .grid-cell.occupied {
            cursor: not-allowed;
        }

        .grid-cell.x {
            color: rgba(0, 255, 136, 0.9);
            text-shadow: 0 0 15px rgba(0, 255, 136, 0.7);
            animation: cellAppear 0.5s ease-out;
        }

        .grid-cell.o {
            color: rgba(255, 136, 0, 0.9);
            text-shadow: 0 0 15px rgba(255, 136, 0, 0.7);
            animation: cellAppear 0.5s ease-out;
        }

        .grid-cell.winning {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.3) 0%, rgba(0, 200, 120, 0.2) 100%);
            border-color: rgba(0, 255, 136, 0.6);
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
            animation: winningCell 1s ease-in-out infinite alternate;
        }

        @keyframes cellAppear {
            0% { transform: scale(0) rotate(180deg); opacity: 0; }
            50% { transform: scale(1.2) rotate(90deg); opacity: 0.8; }
            100% { transform: scale(1) rotate(0deg); opacity: 1; }
        }

        @keyframes winningCell {
            0% { transform: scale(1); }
            100% { transform: scale(1.1); }
        }

        .game-controls {
            display: flex;
            gap: clamp(12px, calc(16px + 0.2vw), 20px);
            justify-content: center;
            margin-top: auto;
        }

        .game-btn {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.2) 0%, rgba(0, 200, 255, 0.15) 100%);
            border: 1px solid rgba(0, 255, 255, 0.4);
            border-radius: 8px;
            padding: clamp(8px, calc(10px + 0.2vw), 14px) clamp(12px, calc(16px + 0.3vw), 20px);
            color: rgba(0, 255, 255, 0.9);
            font-size: clamp(10px, calc(12px + 0.1vw), 14px);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Orbitron', monospace;
        }

        .game-btn:hover {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.3) 0%, rgba(0, 200, 255, 0.25) 100%);
            border-color: rgba(0, 255, 255, 0.6);
            box-shadow: 0 0 12px rgba(0, 255, 255, 0.4);
            transform: translateY(-1px);
        }

        /* Game Result Modal */
        .game-result-modal {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            border-radius: 15px;
            backdrop-filter: blur(5px);
        }

        .result-content {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.2) 0%, rgba(0, 200, 255, 0.15) 100%);
            border: 2px solid rgba(0, 255, 255, 0.5);
            border-radius: 12px;
            padding: clamp(20px, calc(24px + 0.4vw), 32px);
            text-align: center;
            display: flex;
            flex-direction: column;
            gap: clamp(12px, calc(16px + 0.2vw), 20px);
            min-width: clamp(200px, calc(250px + 3vw), 300px);
            animation: modalAppear 0.5s ease-out;
        }

        @keyframes modalAppear {
            0% { transform: scale(0.5) rotate(10deg); opacity: 0; }
            100% { transform: scale(1) rotate(0deg); opacity: 1; }
        }

        .result-icon {
            font-size: clamp(32px, calc(40px + 1vw), 56px);
            filter: drop-shadow(0 0 15px rgba(0, 255, 255, 0.7));
            animation: iconBounce 2s ease-in-out infinite;
        }

        @keyframes iconBounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        .result-text {
            font-size: clamp(16px, calc(18px + 0.3vw), 24px);
            font-weight: 600;
            color: rgba(0, 255, 255, 0.9);
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
        }

        .result-buttons {
            display: flex;
            gap: clamp(8px, calc(12px + 0.2vw), 16px);
            justify-content: center;
        }

        .result-btn {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.2) 0%, rgba(0, 200, 255, 0.15) 100%);
            border: 1px solid rgba(0, 255, 255, 0.4);
            border-radius: 6px;
            padding: clamp(6px, calc(8px + 0.1vw), 10px) clamp(10px, calc(12px + 0.2vw), 16px);
            color: rgba(0, 255, 255, 0.9);
            font-size: clamp(9px, calc(11px + 0.1vw), 13px);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Orbitron', monospace;
        }

        .result-btn:hover {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.3) 0%, rgba(0, 200, 255, 0.25) 100%);
            border-color: rgba(0, 255, 255, 0.6);
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.4);
            transform: translateY(-1px);
        }

        /* Animations */
        @keyframes ticTacToeGlow {
            0%, 100% {
                box-shadow:
                    0 0 20px rgba(0, 255, 255, 0.3),
                    inset 0 0 15px rgba(0, 255, 255, 0.1);
            }
            50% {
                box-shadow:
                    0 0 35px rgba(0, 255, 255, 0.5),
                    inset 0 0 20px rgba(0, 255, 255, 0.2);
            }
        }
    </style>
</head>
<body>
    <div class="tictactoe-widget widget-draggable widget-size-normal" id="ticTacToeWidget">
        <div class="widget-controls">
            <div class="widget-control-btn" onclick="increaseWidgetSize()" title="Make Bigger">⧨</div>
            <div class="widget-control-btn" onclick="decreaseWidgetSize()" title="Make Smaller">⧩</div>
            <div class="widget-control-btn" onclick="closeWidget()" title="Close Game">✕</div>
        </div>
        <div class="resize-handle"></div>
        
        <!-- Header -->
        <div class="tictactoe-header">
            <div class="tictactoe-title">
                <div class="tictactoe-logo">🎮</div>
                <div class="tictactoe-brand">TIC-TAC-TOE</div>
            </div>
            <div class="tictactoe-status" id="ticTacToeStatus">
                <div class="status-indicator"></div>
                <span>Ready to Play</span>
            </div>
        </div>

        <!-- Game Statistics -->
        <div class="game-stats" id="gameStats" style="display: none;">
            <div class="stat-item">
                <div class="stat-value" id="playerWins">0</div>
                <div>Player Wins</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="aiWins">0</div>
                <div>AI Wins</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="draws">0</div>
                <div>Draws</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="totalGames">0</div>
                <div>Total Games</div>
            </div>
        </div>

        <!-- Mode Selection -->
        <div class="tictactoe-mode-selection" id="ticTacToeModeSelection">
            <div class="mode-selection-title">Choose Game Mode</div>
            <div class="mode-buttons">
                <button class="mode-btn ai-mode-btn" onclick="selectAIMode()">
                    <div class="mode-icon">🤖</div>
                    <div class="mode-text">Play vs AI</div>
                    <div class="mode-desc">Challenge the computer</div>
                </button>
                <button class="mode-btn multiplayer-mode-btn" onclick="startGame('multiplayer')">
                    <div class="mode-icon">👥</div>
                    <div class="mode-text">Play Multiplayer</div>
                    <div class="mode-desc">Human vs Human</div>
                </button>
            </div>
        </div>

        <!-- AI Difficulty Selection -->
        <div class="difficulty-selection" id="difficultySelection">
            <div class="difficulty-title">Choose AI Difficulty</div>
            <div class="difficulty-buttons">
                <button class="difficulty-btn" data-difficulty="easy" onclick="selectDifficulty('easy')">
                    <div>😊 Easy</div>
                    <div style="font-size: 10px; margin-top: 4px;">Random moves</div>
                </button>
                <button class="difficulty-btn selected" data-difficulty="medium" onclick="selectDifficulty('medium')">
                    <div>🤔 Medium</div>
                    <div style="font-size: 10px; margin-top: 4px;">Smart strategy</div>
                </button>
                <button class="difficulty-btn" data-difficulty="hard" onclick="selectDifficulty('hard')">
                    <div>🧠 Hard</div>
                    <div style="font-size: 10px; margin-top: 4px;">Unbeatable</div>
                </button>
            </div>
            <div style="margin-top: 16px; display: flex; gap: 12px;">
                <button class="difficulty-btn" onclick="startGame('ai')" style="background: linear-gradient(135deg, rgba(0, 255, 136, 0.2) 0%, rgba(0, 200, 120, 0.15) 100%); border-color: rgba(0, 255, 136, 0.4);">
                    Start Game
                </button>
                <button class="difficulty-btn" onclick="backToModeSelection()">
                    Back
                </button>
            </div>
        </div>

        <!-- Game Board -->
        <div class="tictactoe-game-board" id="ticTacToeGameBoard">
            <div class="game-info">
                <div class="current-player" id="currentPlayer">Player X's Turn</div>
                <div class="game-mode-display" id="gameModeDisplay">vs AI</div>
            </div>

            <div class="game-grid" id="gameGrid">
                <div class="grid-cell" data-index="0" onclick="makeMove(0)"></div>
                <div class="grid-cell" data-index="1" onclick="makeMove(1)"></div>
                <div class="grid-cell" data-index="2" onclick="makeMove(2)"></div>
                <div class="grid-cell" data-index="3" onclick="makeMove(3)"></div>
                <div class="grid-cell" data-index="4" onclick="makeMove(4)"></div>
                <div class="grid-cell" data-index="5" onclick="makeMove(5)"></div>
                <div class="grid-cell" data-index="6" onclick="makeMove(6)"></div>
                <div class="grid-cell" data-index="7" onclick="makeMove(7)"></div>
                <div class="grid-cell" data-index="8" onclick="makeMove(8)"></div>
            </div>

            <div class="game-controls">
                <button class="game-btn new-game-btn" onclick="newGame()">New Game</button>
                <button class="game-btn back-btn" onclick="backToModeSelection()">Change Mode</button>
                <button class="game-btn stats-btn" onclick="toggleStats()">Stats</button>
            </div>
        </div>

        <!-- Game Result Modal -->
        <div class="game-result-modal" id="gameResultModal">
            <div class="result-content">
                <div class="result-icon" id="resultIcon">🎉</div>
                <div class="result-text" id="resultText">You Win!</div>
                <div class="result-buttons">
                    <button class="result-btn play-again-btn" onclick="newGame()">Play Again</button>
                    <button class="result-btn change-mode-btn" onclick="backToModeSelection()">Change Mode</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Enhanced game state with statistics and difficulty
        let gameStats = {
            playerWins: 0,
            aiWins: 0,
            draws: 0,
            totalGames: 0
        };

        let gameSettings = {
            difficulty: 'medium',
            mode: null,
            playerSymbol: 'X',
            aiSymbol: 'O'
        };

        // Load stats from localStorage
        function loadGameStats() {
            const saved = localStorage.getItem('ticTacToeStats');
            if (saved) {
                gameStats = { ...gameStats, ...JSON.parse(saved) };
                updateStatsDisplay();
            }
        }

        // Save stats to localStorage
        function saveGameStats() {
            localStorage.setItem('ticTacToeStats', JSON.stringify(gameStats));
        }

        // Update statistics display
        function updateStatsDisplay() {
            document.getElementById('playerWins').textContent = gameStats.playerWins;
            document.getElementById('aiWins').textContent = gameStats.aiWins;
            document.getElementById('draws').textContent = gameStats.draws;
            document.getElementById('totalGames').textContent = gameStats.totalGames;
        }

        function selectAIMode() {
            document.getElementById('ticTacToeModeSelection').style.display = 'none';
            document.getElementById('difficultySelection').style.display = 'flex';
        }

        function selectDifficulty(difficulty) {
            gameSettings.difficulty = difficulty;
            
            // Update button selection
            document.querySelectorAll('.difficulty-btn[data-difficulty]').forEach(btn => {
                btn.classList.remove('selected');
            });
            document.querySelector(`[data-difficulty="${difficulty}"]`).classList.add('selected');
        }

        function backToModeSelection() {
            document.getElementById('difficultySelection').style.display = 'none';
            document.getElementById('ticTacToeModeSelection').style.display = 'block';
        }

        function closeWidget() {
            // In real application, this would close the widget
            console.log('Widget would be closed');
        }

        function increaseWidgetSize() {
            // Widget size increase logic
            console.log('Increase widget size');
        }

        function decreaseWidgetSize() {
            // Widget size decrease logic
            console.log('Decrease widget size');
        }

        // Initialize the widget
        document.addEventListener('DOMContentLoaded', () => {
            loadGameStats();
            updateStatsDisplay();
        });
    </script>
</body>
</html>
