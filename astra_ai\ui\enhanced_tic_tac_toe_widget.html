<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Tic-Tac-Toe Widget</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', 'Segoe UI', monospace;
            background: radial-gradient(ellipse at 30% 50%, #0C294F 0%, #061D3B 70%, #041529 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        /* Enhanced Tic-Tac-Toe Widget with consistent styling */
        .tictactoe-widget {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            min-width: 450px;
            max-width: 600px;
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.15) 0%, rgba(0, 200, 120, 0.1) 100%);
            border: 1px solid rgba(0, 255, 136, 0.4);
            border-radius: 12px;
            font-family: 'Orbitron', monospace;
            color: white;
            box-shadow: 
                0 0 20px rgba(0, 255, 136, 0.3),
                inset 0 0 15px rgba(0, 255, 136, 0.1);
            backdrop-filter: blur(8px);
            animation: tictactoeGlow 4s ease-in-out infinite;
            z-index: 25;
            display: none;
        }

        /* Corner brackets for visual consistency */
        .tictactoe-widget::before,
        .tictactoe-widget::after {
            content: '';
            position: absolute;
            width: 12px;
            height: 12px;
            border: 2px solid rgba(255, 255, 255, 1);
            z-index: 1;
        }

        .tictactoe-widget::before {
            top: -4px;
            left: -4px;
            border-right: none;
            border-bottom: none;
        }

        .tictactoe-widget::after {
            bottom: -4px;
            right: -4px;
            border-left: none;
            border-top: none;
        }

        .corner-top-right {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 12px;
            height: 12px;
            border: 2px solid rgba(255, 255, 255, 1);
            border-left: none;
            border-bottom: none;
            z-index: 1;
        }

        .corner-bottom-left {
            position: absolute;
            bottom: -4px;
            left: -4px;
            width: 12px;
            height: 12px;
            border: 2px solid rgba(255, 255, 255, 1);
            border-right: none;
            border-top: none;
            z-index: 1;
        }

        /* Header Section */
        .tictactoe-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px 12px 24px;
            border-bottom: 1px solid rgba(0, 255, 136, 0.3);
            position: relative;
        }

        .tictactoe-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .tictactoe-logo {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.8) 0%, rgba(0, 200, 120, 1) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #041529;
            font-size: 16px;
            font-weight: 700;
            box-shadow: 0 4px 12px rgba(0, 255, 136, 0.4);
        }

        .tictactoe-brand {
            font-size: 18px;
            font-weight: 700;
            color: white;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            letter-spacing: 1px;
        }

        .tictactoe-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            background: #00FF88;
            border-radius: 50%;
            animation: pulse 2s infinite;
            box-shadow: 0 0 6px #00FF88;
        }

        /* Widget Controls */
        .tictactoe-controls {
            position: absolute;
            top: 16px;
            right: 24px;
            display: flex;
            gap: 8px;
        }

        .widget-control-btn {
            width: 28px;
            height: 28px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: rgba(255, 255, 255, 0.8);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .widget-control-btn:hover {
            background: rgba(0, 255, 136, 0.2);
            border-color: rgba(0, 255, 136, 0.4);
            color: white;
            transform: scale(1.1);
        }

        /* Content Area */
        .tictactoe-content {
            padding: 20px 24px 24px 24px;
            min-height: 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        @keyframes tictactoeGlow {
            0%, 100% {
                box-shadow: 
                    0 0 20px rgba(0, 255, 136, 0.3),
                    inset 0 0 15px rgba(0, 255, 136, 0.1);
            }
            50% {
                box-shadow: 
                    0 0 30px rgba(0, 255, 136, 0.5),
                    inset 0 0 20px rgba(0, 255, 136, 0.2);
            }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Enhanced Game Styles */
        .game-mode-selection {
            text-align: center;
            color: rgba(0, 255, 136, 0.9);
            animation: fadeInUp 0.5s ease-out;
        }

        .game-mode-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 30px;
            text-shadow: 0 0 15px rgba(0, 255, 136, 0.8);
            letter-spacing: 1px;
        }

        .mode-buttons {
            display: flex;
            flex-direction: column;
            gap: 16px;
            align-items: center;
        }

        .mode-btn {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.1) 0%, rgba(0, 200, 120, 0.05) 100%);
            border: 2px solid rgba(0, 255, 136, 0.4);
            border-radius: 12px;
            color: rgba(0, 255, 136, 0.9);
            padding: 16px 32px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            text-shadow: 0 0 8px rgba(0, 255, 136, 0.6);
            min-width: 200px;
            font-family: 'Orbitron', monospace;
        }

        .mode-btn:hover {
            border-color: rgba(0, 255, 136, 0.8);
            box-shadow: 0 0 25px rgba(0, 255, 136, 0.4);
            transform: translateY(-3px) scale(1.02);
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.2) 0%, rgba(0, 200, 120, 0.1) 100%);
        }

        .mode-btn:active {
            transform: translateY(-1px) scale(0.98);
        }

        /* AI Difficulty Selection */
        .ai-difficulty-selection {
            display: none;
            text-align: center;
            color: rgba(0, 255, 136, 0.9);
            animation: fadeInUp 0.5s ease-out;
        }

        .difficulty-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 24px;
            text-shadow: 0 0 12px rgba(0, 255, 136, 0.8);
        }

        .difficulty-buttons {
            display: flex;
            flex-direction: column;
            gap: 12px;
            align-items: center;
            margin-bottom: 24px;
        }

        .difficulty-btn {
            background: linear-gradient(135deg, rgba(255, 136, 0, 0.1) 0%, rgba(255, 100, 0, 0.05) 100%);
            border: 2px solid rgba(255, 136, 0, 0.4);
            border-radius: 10px;
            color: rgba(255, 136, 0, 0.9);
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            min-width: 180px;
            font-family: 'Orbitron', monospace;
        }

        .difficulty-btn:hover {
            border-color: rgba(255, 136, 0, 0.8);
            box-shadow: 0 0 20px rgba(255, 136, 0, 0.4);
            transform: translateY(-2px);
        }

        .difficulty-btn.selected {
            background: linear-gradient(135deg, rgba(255, 136, 0, 0.3) 0%, rgba(255, 100, 0, 0.2) 100%);
            border-color: rgba(255, 136, 0, 0.8);
            box-shadow: 0 0 15px rgba(255, 136, 0, 0.5);
        }

        .symbol-selection {
            margin-bottom: 20px;
        }

        .symbol-title {
            font-size: 16px;
            margin-bottom: 16px;
            color: rgba(0, 255, 136, 0.9);
        }

        .symbol-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
        }

        .symbol-btn {
            background: linear-gradient(135deg, rgba(0, 200, 255, 0.1) 0%, rgba(0, 150, 255, 0.05) 100%);
            border: 2px solid rgba(0, 200, 255, 0.4);
            border-radius: 12px;
            color: rgba(0, 200, 255, 0.9);
            padding: 16px;
            font-size: 24px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .symbol-btn:hover {
            border-color: rgba(0, 200, 255, 0.8);
            box-shadow: 0 0 20px rgba(0, 200, 255, 0.4);
            transform: scale(1.1);
        }

        .symbol-btn.selected {
            background: linear-gradient(135deg, rgba(0, 200, 255, 0.3) 0%, rgba(0, 150, 255, 0.2) 100%);
            border-color: rgba(0, 200, 255, 0.8);
            box-shadow: 0 0 15px rgba(0, 200, 255, 0.5);
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        .action-btn {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.2) 0%, rgba(0, 200, 120, 0.1) 100%);
            border: 1px solid rgba(0, 255, 136, 0.4);
            border-radius: 8px;
            color: rgba(0, 255, 136, 0.9);
            padding: 10px 20px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Orbitron', monospace;
        }

        .action-btn:hover {
            border-color: rgba(0, 255, 136, 0.6);
            box-shadow: 0 0 12px rgba(0, 255, 136, 0.3);
            transform: translateY(-1px);
        }

        /* Responsive scaling system */
        .widget-draggable {
            resize: both;
            overflow: hidden;
        }

        .widget-draggable * {
            transition: font-size 0.2s ease, padding 0.2s ease, margin 0.2s ease !important;
        }

        .widget-dragging * {
            transition: none !important;
        }

        /* Game Board Styles */
        .game-container {
            display: none;
            flex-direction: column;
            align-items: center;
            width: 100%;
            animation: fadeInUp 0.5s ease-out;
        }

        .game-info {
            color: rgba(0, 255, 136, 0.9);
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
            text-shadow: 0 0 12px rgba(0, 255, 136, 0.8);
            min-height: 24px;
        }

        .game-board {
            position: relative;
            width: 300px;
            height: 300px;
            margin-bottom: 24px;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 0;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            overflow: hidden;
            box-shadow:
                0 0 20px rgba(0, 255, 136, 0.2),
                inset 0 0 10px rgba(0, 255, 136, 0.1);
        }

        .game-board::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                /* Vertical lines */
                linear-gradient(90deg, transparent calc(33.33% - 1px), rgba(0, 255, 136, 0.8) 33.33%, rgba(0, 255, 136, 0.8) calc(33.33% + 1px), transparent calc(33.33% + 1px)),
                linear-gradient(90deg, transparent calc(66.66% - 1px), rgba(0, 255, 136, 0.8) 66.66%, rgba(0, 255, 136, 0.8) calc(66.66% + 1px), transparent calc(66.66% + 1px)),
                /* Horizontal lines */
                linear-gradient(0deg, transparent calc(33.33% - 1px), rgba(0, 255, 136, 0.8) 33.33%, rgba(0, 255, 136, 0.8) calc(33.33% + 1px), transparent calc(33.33% + 1px)),
                linear-gradient(0deg, transparent calc(66.66% - 1px), rgba(0, 255, 136, 0.8) 66.66%, rgba(0, 255, 136, 0.8) calc(66.66% + 1px), transparent calc(66.66% + 1px));
            filter: drop-shadow(0 0 6px rgba(0, 255, 136, 0.6));
            pointer-events: none;
            z-index: 1;
        }

        .cell {
            background: transparent;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
            font-family: 'Orbitron', monospace;
        }

        .cell:hover {
            background: rgba(0, 255, 136, 0.1);
            transform: scale(1.05);
            box-shadow: inset 0 0 15px rgba(0, 255, 136, 0.3);
        }

        .cell.x {
            color: rgba(0, 255, 136, 1);
            text-shadow: 0 0 20px rgba(0, 255, 136, 1);
            animation: cellAppear 0.4s ease-out;
        }

        .cell.o {
            color: rgba(0, 200, 255, 1);
            text-shadow: 0 0 20px rgba(0, 200, 255, 1);
            animation: cellAppear 0.4s ease-out;
        }

        .cell.disabled {
            cursor: not-allowed;
            opacity: 0.6;
        }

        .cell.winning {
            animation: winningCell 1s ease-in-out infinite;
        }

        .game-controls {
            display: flex;
            gap: 16px;
            margin-top: 16px;
        }

        .control-btn {
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.2) 0%, rgba(0, 200, 120, 0.1) 100%);
            border: 1px solid rgba(0, 255, 136, 0.4);
            border-radius: 8px;
            color: rgba(0, 255, 136, 0.9);
            padding: 10px 20px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Orbitron', monospace;
        }

        .control-btn:hover {
            border-color: rgba(0, 255, 136, 0.6);
            box-shadow: 0 0 15px rgba(0, 255, 136, 0.3);
            transform: translateY(-1px);
        }

        /* Score Display */
        .score-display {
            display: flex;
            gap: 24px;
            margin-bottom: 16px;
            font-family: 'Orbitron', monospace;
        }

        .score-item {
            text-align: center;
            padding: 8px 16px;
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 8px;
        }

        .score-label {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 4px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .score-value {
            font-size: 18px;
            font-weight: 700;
            color: rgba(0, 255, 136, 0.9);
            text-shadow: 0 0 8px rgba(0, 255, 136, 0.6);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes cellAppear {
            from {
                opacity: 0;
                transform: scale(0.5) rotate(180deg);
            }
            to {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
        }

        @keyframes winningCell {
            0%, 100% {
                transform: scale(1);
                text-shadow: 0 0 20px rgba(0, 255, 136, 1);
            }
            50% {
                transform: scale(1.1);
                text-shadow: 0 0 30px rgba(0, 255, 136, 1);
            }
        }
    </style>
</head>
<body>
    <div class="tictactoe-widget widget-draggable" id="tictactoeWidget">
        <div class="corner-top-right"></div>
        <div class="corner-bottom-left"></div>
        
        <!-- Header -->
        <div class="tictactoe-header">
            <div class="tictactoe-title">
                <div class="tictactoe-logo"><i class="fa-solid fa-gamepad"></i></div>
                <div class="tictactoe-brand">Tic-Tac-Toe</div>
            </div>
            <div class="tictactoe-status">
                <div class="status-indicator"></div>
                <span>Ready</span>
            </div>
            <div class="tictactoe-controls">
                <div class="widget-control-btn" onclick="minimizeTicTacToeWidget()" title="Minimize">
                    <i class="fa-solid fa-minus"></i>
                </div>
                <div class="widget-control-btn" onclick="closeTicTacToeWidget()" title="Close">
                    <i class="fa-solid fa-times"></i>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="tictactoe-content" id="tictactoeContent">
            <!-- Game content will be populated here -->
        </div>
    </div>

    <script>
        // ================= ENHANCED TIC-TAC-TOE GAME ENGINE =================

        // Game state variables
        let gameMode = null; // 'ai' or 'multiplayer'
        let aiDifficulty = 'medium'; // 'easy', 'medium', 'hard', 'impossible'
        let currentPlayer = 'X';
        let gameBoard = Array(9).fill('');
        let gameActive = true;
        let playerSymbol = 'X';
        let aiSymbol = 'O';
        let selectedSymbol = null;
        let gameStats = {
            playerWins: 0,
            aiWins: 0,
            draws: 0,
            gamesPlayed: 0
        };

        // Widget management functions
        function showTicTacToeWidget() {
            const widget = document.getElementById('tictactoeWidget');
            widget.style.display = 'block';
            initializeGame();
            console.log('🎮 Enhanced Tic-Tac-Toe widget shown');
        }

        function hideTicTacToeWidget() {
            const widget = document.getElementById('tictactoeWidget');
            widget.style.display = 'none';
            console.log('🎮 Enhanced Tic-Tac-Toe widget hidden');
        }

        function minimizeTicTacToeWidget() {
            const widget = document.getElementById('tictactoeWidget');
            widget.style.height = '60px';
            widget.style.overflow = 'hidden';
            console.log('🎮 Tic-Tac-Toe widget minimized');
        }

        function closeTicTacToeWidget() {
            hideTicTacToeWidget();
        }

        // Game initialization
        function initializeGame() {
            const content = document.getElementById('tictactoeContent');
            content.innerHTML = `
                <div class="game-mode-selection" id="gameModeSelection">
                    <div class="game-mode-title">Choose Game Mode</div>
                    <div class="mode-buttons">
                        <button class="mode-btn" onclick="selectGameMode('ai')">
                            <i class="fa-solid fa-robot"></i> Play vs AI
                        </button>
                        <button class="mode-btn" onclick="selectGameMode('multiplayer')">
                            <i class="fa-solid fa-users"></i> Play Multiplayer
                        </button>
                    </div>
                </div>

                <div class="ai-difficulty-selection" id="aiDifficultySelection">
                    <div class="difficulty-title">Choose AI Difficulty</div>
                    <div class="difficulty-buttons">
                        <button class="difficulty-btn" data-difficulty="easy" onclick="selectDifficulty('easy')">
                            Easy - Random moves
                        </button>
                        <button class="difficulty-btn selected" data-difficulty="medium" onclick="selectDifficulty('medium')">
                            Medium - Basic strategy
                        </button>
                        <button class="difficulty-btn" data-difficulty="hard" onclick="selectDifficulty('hard')">
                            Hard - Advanced strategy
                        </button>
                        <button class="difficulty-btn" data-difficulty="impossible" onclick="selectDifficulty('impossible')">
                            Impossible - Perfect play
                        </button>
                    </div>

                    <div class="symbol-selection">
                        <div class="symbol-title">Choose Your Symbol</div>
                        <div class="symbol-buttons">
                            <button class="symbol-btn selected" data-symbol="X" onclick="selectSymbol('X')">X</button>
                            <button class="symbol-btn" data-symbol="O" onclick="selectSymbol('O')">O</button>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="action-btn" onclick="startAIGame()">Start Game</button>
                        <button class="action-btn" onclick="backToModeSelection()">Back</button>
                    </div>
                </div>

                <div class="game-container" id="gameContainer">
                    <div class="score-display" id="scoreDisplay">
                        <div class="score-item">
                            <div class="score-label">Player</div>
                            <div class="score-value" id="playerScore">0</div>
                        </div>
                        <div class="score-item">
                            <div class="score-label">AI/P2</div>
                            <div class="score-value" id="opponentScore">0</div>
                        </div>
                        <div class="score-item">
                            <div class="score-label">Draws</div>
                            <div class="score-value" id="drawScore">0</div>
                        </div>
                    </div>

                    <div class="game-info" id="gameInfo">Player X's Turn</div>

                    <div class="game-board" id="gameBoard">
                        ${Array(9).fill(0).map((_, i) => `<div class="cell" data-index="${i}" onclick="makeMove(${i})"></div>`).join('')}
                    </div>

                    <div class="game-controls">
                        <button class="control-btn" onclick="newGame()">
                            <i class="fa-solid fa-refresh"></i> New Game
                        </button>
                        <button class="control-btn" onclick="backToModeSelection()">
                            <i class="fa-solid fa-arrow-left"></i> Back to Menu
                        </button>
                        <button class="control-btn" onclick="resetStats()">
                            <i class="fa-solid fa-chart-bar"></i> Reset Stats
                        </button>
                    </div>
                </div>
            `;

            // Set default symbol selection
            selectedSymbol = 'X';
            updateGameStatus('Ready to play!');
        }

        // Game mode selection
        function selectGameMode(mode) {
            gameMode = mode;
            document.getElementById('gameModeSelection').style.display = 'none';

            if (mode === 'ai') {
                document.getElementById('aiDifficultySelection').style.display = 'block';
            } else {
                startMultiplayerGame();
            }
        }

        function selectDifficulty(difficulty) {
            aiDifficulty = difficulty;

            // Update UI
            document.querySelectorAll('.difficulty-btn').forEach(btn => {
                btn.classList.remove('selected');
            });
            document.querySelector(`[data-difficulty="${difficulty}"]`).classList.add('selected');
        }

        function selectSymbol(symbol) {
            selectedSymbol = symbol;

            // Update UI
            document.querySelectorAll('.symbol-btn').forEach(btn => {
                btn.classList.remove('selected');
            });
            document.querySelector(`[data-symbol="${symbol}"]`).classList.add('selected');
        }

        // Game start functions
        function startAIGame() {
            if (!selectedSymbol) {
                alert('Please choose your symbol first!');
                return;
            }

            playerSymbol = selectedSymbol;
            aiSymbol = selectedSymbol === 'X' ? 'O' : 'X';
            currentPlayer = 'X';

            document.getElementById('aiDifficultySelection').style.display = 'none';
            document.getElementById('gameContainer').style.display = 'flex';

            updateGameInfo();
            resetBoard();
            updateScoreDisplay();

            // If AI goes first (X), make AI move
            if (aiSymbol === 'X') {
                setTimeout(makeAIMove, 800);
            }
        }

        function startMultiplayerGame() {
            gameMode = 'multiplayer';
            currentPlayer = 'X';

            document.getElementById('gameModeSelection').style.display = 'none';
            document.getElementById('gameContainer').style.display = 'flex';

            updateGameInfo();
            resetBoard();
            updateScoreDisplay();
        }

        function backToModeSelection() {
            document.getElementById('aiDifficultySelection').style.display = 'none';
            document.getElementById('gameContainer').style.display = 'none';
            document.getElementById('gameModeSelection').style.display = 'block';

            // Reset game state
            gameMode = null;
            currentPlayer = 'X';
            gameBoard = Array(9).fill('');
            gameActive = true;
            selectedSymbol = 'X';

            // Reset UI
            document.querySelectorAll('.symbol-btn').forEach(btn => {
                btn.classList.remove('selected');
            });
            document.querySelector('[data-symbol="X"]').classList.add('selected');
        }

        // Core game logic
        function makeMove(index) {
            if (!gameActive || gameBoard[index] !== '') return;

            // In AI mode, prevent moves when it's AI's turn
            if (gameMode === 'ai' && currentPlayer === aiSymbol) return;

            gameBoard[index] = currentPlayer;
            updateBoard();

            if (checkWin()) {
                gameActive = false;
                highlightWinningCells();
                updateStats(currentPlayer);
                updateGameInfo();
                updateScoreDisplay();
                return;
            }

            if (checkDraw()) {
                gameActive = false;
                updateStats('draw');
                updateGameInfo();
                updateScoreDisplay();
                return;
            }

            currentPlayer = currentPlayer === 'X' ? 'O' : 'X';
            updateGameInfo();

            // Make AI move after player move
            if (gameMode === 'ai' && gameActive && currentPlayer === aiSymbol) {
                setTimeout(makeAIMove, 600);
            }
        }

        function makeAIMove() {
            if (!gameActive) return;

            updateGameStatus('AI is thinking...');

            const bestMove = getBestMove();
            if (bestMove !== -1) {
                gameBoard[bestMove] = aiSymbol;
                updateBoard();

                if (checkWin()) {
                    gameActive = false;
                    highlightWinningCells();
                    updateStats(aiSymbol);
                    updateGameInfo();
                    updateScoreDisplay();
                    return;
                }

                if (checkDraw()) {
                    gameActive = false;
                    updateStats('draw');
                    updateGameInfo();
                    updateScoreDisplay();
                    return;
                }

                currentPlayer = currentPlayer === 'X' ? 'O' : 'X';
                updateGameInfo();
            }
        }

        // Enhanced AI with multiple difficulty levels
        function getBestMove() {
            switch (aiDifficulty) {
                case 'easy':
                    return getRandomMove();
                case 'medium':
                    return getMediumMove();
                case 'hard':
                    return getHardMove();
                case 'impossible':
                    return getMinimaxMove();
                default:
                    return getMediumMove();
            }
        }

        function getRandomMove() {
            const availableMoves = [];
            for (let i = 0; i < 9; i++) {
                if (gameBoard[i] === '') {
                    availableMoves.push(i);
                }
            }
            return availableMoves.length > 0 ? availableMoves[Math.floor(Math.random() * availableMoves.length)] : -1;
        }

        function getMediumMove() {
            // Try to win first
            for (let i = 0; i < 9; i++) {
                if (gameBoard[i] === '') {
                    gameBoard[i] = aiSymbol;
                    if (checkWin()) {
                        gameBoard[i] = '';
                        return i;
                    }
                    gameBoard[i] = '';
                }
            }

            // Block player win
            for (let i = 0; i < 9; i++) {
                if (gameBoard[i] === '') {
                    gameBoard[i] = playerSymbol;
                    if (checkWin()) {
                        gameBoard[i] = '';
                        return i;
                    }
                    gameBoard[i] = '';
                }
            }

            // Take center if available
            if (gameBoard[4] === '') return 4;

            // Take random available spot
            return getRandomMove();
        }

        function getHardMove() {
            // Try to win first
            for (let i = 0; i < 9; i++) {
                if (gameBoard[i] === '') {
                    gameBoard[i] = aiSymbol;
                    if (checkWin()) {
                        gameBoard[i] = '';
                        return i;
                    }
                    gameBoard[i] = '';
                }
            }

            // Block player win
            for (let i = 0; i < 9; i++) {
                if (gameBoard[i] === '') {
                    gameBoard[i] = playerSymbol;
                    if (checkWin()) {
                        gameBoard[i] = '';
                        return i;
                    }
                    gameBoard[i] = '';
                }
            }

            // Strategic moves
            if (gameBoard[4] === '') return 4; // Center

            // Corners
            const corners = [0, 2, 6, 8];
            for (let corner of corners) {
                if (gameBoard[corner] === '') return corner;
            }

            // Edges
            const edges = [1, 3, 5, 7];
            for (let edge of edges) {
                if (gameBoard[edge] === '') return edge;
            }

            return -1;
        }

        // Minimax algorithm for impossible difficulty
        function getMinimaxMove() {
            let bestScore = -Infinity;
            let bestMove = -1;

            for (let i = 0; i < 9; i++) {
                if (gameBoard[i] === '') {
                    gameBoard[i] = aiSymbol;
                    let score = minimax(gameBoard, 0, false);
                    gameBoard[i] = '';

                    if (score > bestScore) {
                        bestScore = score;
                        bestMove = i;
                    }
                }
            }

            return bestMove;
        }

        function minimax(board, depth, isMaximizing) {
            let result = checkWinForMinimax();
            if (result !== null) {
                return result;
            }

            if (isMaximizing) {
                let bestScore = -Infinity;
                for (let i = 0; i < 9; i++) {
                    if (board[i] === '') {
                        board[i] = aiSymbol;
                        let score = minimax(board, depth + 1, false);
                        board[i] = '';
                        bestScore = Math.max(score, bestScore);
                    }
                }
                return bestScore;
            } else {
                let bestScore = Infinity;
                for (let i = 0; i < 9; i++) {
                    if (board[i] === '') {
                        board[i] = playerSymbol;
                        let score = minimax(board, depth + 1, true);
                        board[i] = '';
                        bestScore = Math.min(score, bestScore);
                    }
                }
                return bestScore;
            }
        }

        function checkWinForMinimax() {
            const winPatterns = [
                [0, 1, 2], [3, 4, 5], [6, 7, 8], // Rows
                [0, 3, 6], [1, 4, 7], [2, 5, 8], // Columns
                [0, 4, 8], [2, 4, 6] // Diagonals
            ];

            for (let pattern of winPatterns) {
                const [a, b, c] = pattern;
                if (gameBoard[a] && gameBoard[a] === gameBoard[b] && gameBoard[a] === gameBoard[c]) {
                    if (gameBoard[a] === aiSymbol) return 1;
                    if (gameBoard[a] === playerSymbol) return -1;
                }
            }

            if (gameBoard.every(cell => cell !== '')) return 0; // Draw
            return null; // Game not finished
        }

        // Game utility functions
        function updateBoard() {
            const cells = document.querySelectorAll('.cell');
            cells.forEach((cell, index) => {
                cell.textContent = gameBoard[index];
                cell.className = 'cell';
                if (gameBoard[index] === 'X') cell.classList.add('x');
                if (gameBoard[index] === 'O') cell.classList.add('o');
                if (gameBoard[index] !== '') cell.classList.add('disabled');
            });
        }

        function updateGameInfo() {
            const gameInfo = document.getElementById('gameInfo');

            if (!gameActive) {
                if (checkWin()) {
                    if (gameMode === 'ai') {
                        const winner = currentPlayer === playerSymbol ? 'You' : 'AI';
                        const emoji = currentPlayer === playerSymbol ? '🎉' : '🤖';
                        gameInfo.textContent = `${winner} Win! ${emoji}`;
                    } else {
                        gameInfo.textContent = `Player ${currentPlayer} Wins! 🎉`;
                    }
                } else {
                    gameInfo.textContent = "It's a Draw! 🤝";
                }
            } else {
                if (gameMode === 'ai') {
                    gameInfo.textContent = currentPlayer === playerSymbol ? 'Your Turn' : 'AI is thinking...';
                } else {
                    gameInfo.textContent = `Player ${currentPlayer}'s Turn`;
                }
            }
        }

        function updateGameStatus(status) {
            const statusElement = document.querySelector('.tictactoe-status span');
            if (statusElement) {
                statusElement.textContent = status;
            }
        }

        function checkWin() {
            const winPatterns = [
                [0, 1, 2], [3, 4, 5], [6, 7, 8], // Rows
                [0, 3, 6], [1, 4, 7], [2, 5, 8], // Columns
                [0, 4, 8], [2, 4, 6] // Diagonals
            ];

            return winPatterns.some(pattern => {
                const [a, b, c] = pattern;
                return gameBoard[a] && gameBoard[a] === gameBoard[b] && gameBoard[a] === gameBoard[c];
            });
        }

        function checkDraw() {
            return gameBoard.every(cell => cell !== '');
        }

        function highlightWinningCells() {
            const winPatterns = [
                [0, 1, 2], [3, 4, 5], [6, 7, 8],
                [0, 3, 6], [1, 4, 7], [2, 5, 8],
                [0, 4, 8], [2, 4, 6]
            ];

            winPatterns.forEach(pattern => {
                const [a, b, c] = pattern;
                if (gameBoard[a] && gameBoard[a] === gameBoard[b] && gameBoard[a] === gameBoard[c]) {
                    const cells = document.querySelectorAll('.cell');
                    cells[a].classList.add('winning');
                    cells[b].classList.add('winning');
                    cells[c].classList.add('winning');
                }
            });
        }

        function newGame() {
            gameBoard = Array(9).fill('');
            gameActive = true;
            currentPlayer = 'X';

            resetBoard();
            updateGameInfo();
            updateGameStatus('Playing');

            // If AI goes first in new game
            if (gameMode === 'ai' && aiSymbol === 'X') {
                setTimeout(makeAIMove, 800);
            }
        }

        function resetBoard() {
            const cells = document.querySelectorAll('.cell');
            cells.forEach(cell => {
                cell.textContent = '';
                cell.className = 'cell';
            });
        }

        function updateStats(winner) {
            gameStats.gamesPlayed++;

            if (winner === 'draw') {
                gameStats.draws++;
            } else if (gameMode === 'ai') {
                if (winner === playerSymbol) {
                    gameStats.playerWins++;
                } else {
                    gameStats.aiWins++;
                }
            } else {
                // Multiplayer mode - track as player vs player
                if (winner === 'X') {
                    gameStats.playerWins++;
                } else {
                    gameStats.aiWins++; // Using aiWins for Player 2
                }
            }
        }

        function updateScoreDisplay() {
            document.getElementById('playerScore').textContent = gameStats.playerWins;
            document.getElementById('opponentScore').textContent = gameStats.aiWins;
            document.getElementById('drawScore').textContent = gameStats.draws;
        }

        function resetStats() {
            if (confirm('Are you sure you want to reset all game statistics?')) {
                gameStats = {
                    playerWins: 0,
                    aiWins: 0,
                    draws: 0,
                    gamesPlayed: 0
                };
                updateScoreDisplay();
                console.log('🎮 Game statistics reset');
            }
        }

        // Initialize widget
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎮 Enhanced Tic-Tac-Toe widget initialized');
        });
    </script>
</body>
</html>
