<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Animation Test</title>
    <style>
        body {
            background: #000;
            color: #fff;
            font-family: 'Orbitron', monospace;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
        }

        .test-container {
            text-align: center;
            padding: 20px;
        }

        .test-button {
            background: linear-gradient(45deg, #00ffff, #0080ff);
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 25px;
            color: #000;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: scale(1.05);
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }

        .animation-preview {
            width: 200px;
            height: 200px;
            border: 2px solid #00ffff;
            border-radius: 50%;
            margin: 20px auto;
            position: relative;
            transition: all 0.3s ease;
        }

        .animation-preview.voice-low {
            animation: testPulse 2s ease-in-out infinite;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }

        .animation-preview.voice-medium {
            animation: testPulse 1.5s ease-in-out infinite;
            box-shadow: 0 0 30px rgba(0, 255, 255, 0.7);
        }

        .animation-preview.voice-high {
            animation: testPulse 1s ease-in-out infinite;
            box-shadow: 0 0 40px rgba(0, 255, 255, 0.9);
        }

        .animation-preview.voice-bass {
            box-shadow: 0 0 30px rgba(255, 0, 100, 0.8);
        }

        .animation-preview.voice-treble {
            box-shadow: 0 0 30px rgba(255, 255, 0, 0.8);
        }

        .animation-preview.voice-excited {
            box-shadow: 0 0 35px rgba(255, 100, 0, 0.9);
        }

        .animation-preview.voice-question {
            box-shadow: 0 0 35px rgba(150, 0, 255, 0.8);
        }

        @keyframes testPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .status {
            margin: 20px 0;
            padding: 10px;
            background: rgba(0, 255, 255, 0.1);
            border-radius: 10px;
            min-height: 50px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 Nova AI Voice Animation Test</h1>
        
        <div class="animation-preview" id="animationPreview"></div>
        
        <div class="status" id="status">
            Click buttons below to test different voice animation styles
        </div>
        
        <div>
            <button class="test-button" onclick="testAnimation('low', 'mid', 'calm')">
                🔉 Low Intensity
            </button>
            <button class="test-button" onclick="testAnimation('medium', 'mid', 'calm')">
                🔊 Medium Intensity
            </button>
            <button class="test-button" onclick="testAnimation('high', 'treble', 'excited')">
                📢 High Intensity
            </button>
        </div>
        
        <div>
            <button class="test-button" onclick="testAnimation('medium', 'bass', 'calm')">
                🎵 Bass Frequency
            </button>
            <button class="test-button" onclick="testAnimation('medium', 'treble', 'calm')">
                🎶 Treble Frequency
            </button>
            <button class="test-button" onclick="testAnimation('high', 'mid', 'question')">
                ❓ Question Tone
            </button>
        </div>
        
        <div>
            <button class="test-button" onclick="testTextAnalysis()">
                📝 Test Text Analysis
            </button>
            <button class="test-button" onclick="stopAnimation()">
                ⏹️ Stop Animation
            </button>
        </div>
        
        <div style="margin-top: 30px;">
            <h3>Test Phrases:</h3>
            <button class="test-button" onclick="testPhrase('Hello! How are you doing today?')">
                "Hello! How are you doing today?"
            </button>
            <button class="test-button" onclick="testPhrase('That sounds absolutely amazing and incredible!')">
                "That sounds absolutely amazing!"
            </button>
            <button class="test-button" onclick="testPhrase('What do you think about this interesting question?')">
                "What do you think about this?"
            </button>
        </div>
    </div>

    <script>
        const preview = document.getElementById('animationPreview');
        const status = document.getElementById('status');

        function testAnimation(intensity, frequency, emotion) {
            // Clear existing classes
            preview.className = 'animation-preview';
            
            // Add new classes
            preview.classList.add(`voice-${intensity}`);
            preview.classList.add(`voice-${frequency}`);
            preview.classList.add(`voice-${emotion}`);
            
            status.innerHTML = `
                🎨 Animation Active<br>
                Intensity: ${intensity}<br>
                Frequency: ${frequency}<br>
                Emotion: ${emotion}
            `;
            
            console.log(`Testing: ${intensity} intensity, ${frequency} frequency, ${emotion} emotion`);
        }

        function stopAnimation() {
            preview.className = 'animation-preview';
            status.innerHTML = 'Animation stopped';
        }

        function testTextAnalysis() {
            const testTexts = [
                "This is amazing!",
                "What do you think?",
                "Hello there, how are you doing today?",
                "WOW! That's incredible!",
                "I understand your question."
            ];
            
            const randomText = testTexts[Math.floor(Math.random() * testTexts.length)];
            testPhrase(randomText);
        }

        function testPhrase(text) {
            const analysis = analyzeText(text);
            testAnimation(analysis.intensity, analysis.frequency, analysis.emotion);
            
            status.innerHTML = `
                📝 Text: "${text}"<br>
                🎨 Analysis:<br>
                Intensity: ${analysis.intensity}<br>
                Frequency: ${analysis.frequency}<br>
                Emotion: ${analysis.emotion}
            `;
        }

        function analyzeText(text) {
            const words = text.toLowerCase().split(' ');
            let bassCount = 0;
            let midCount = 0;
            let trebleCount = 0;
            
            // Analyze frequency characteristics
            words.forEach(word => {
                if (word.includes('oo') || word.includes('oh') || word.includes('ow') || word.length > 8) {
                    bassCount++;
                } else if (word.includes('ee') || word.includes('i') || word.includes('s') || word.includes('t') || word.includes('k')) {
                    trebleCount++;
                } else {
                    midCount++;
                }
            });
            
            // Determine frequency
            let frequency = 'mid';
            if (bassCount > midCount && bassCount > trebleCount) {
                frequency = 'bass';
            } else if (trebleCount > midCount && trebleCount > bassCount) {
                frequency = 'treble';
            }
            
            // Analyze emotion
            let emotion = 'calm';
            const exclamationCount = (text.match(/!/g) || []).length;
            const questionCount = (text.match(/\?/g) || []).length;
            const capsCount = (text.match(/[A-Z]/g) || []).length;
            const excitedWords = ['amazing', 'awesome', 'incredible', 'fantastic', 'wow', 'great', 'excellent'];
            const hasExcitedWords = excitedWords.some(word => text.toLowerCase().includes(word));
            
            if (exclamationCount > 0 || capsCount > text.length * 0.3 || hasExcitedWords) {
                emotion = 'excited';
            } else if (questionCount > 0) {
                emotion = 'question';
            }
            
            // Determine intensity
            let intensity = 'low';
            if (exclamationCount > 0 || capsCount > text.length * 0.3) {
                intensity = 'high';
            } else if (questionCount > 0 || text.length > 100 || hasExcitedWords) {
                intensity = 'medium';
            }
            
            return { intensity, frequency, emotion };
        }
    </script>
</body>
</html>
