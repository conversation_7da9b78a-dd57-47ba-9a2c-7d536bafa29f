<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tic-Tac-<PERSON>e Widget</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .widget {
            background: rgba(15, 15, 35, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 20px;
            box-shadow: 0 0 40px rgba(0, 255, 255, 0.2);
            position: relative;
            width: 400px;
            height: 500px;
            cursor: move;
            overflow: hidden;
        }

        .widget-header {
            background: linear-gradient(90deg, #00ffff, #0080ff);
            color: #000;
            font-weight: bold;
            font-size: 18px;
            padding: 12px 20px;
            text-align: center;
            letter-spacing: 1px;
            position: relative;
        }

        .close-btn {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #000;
            font-size: 20px;
            cursor: pointer;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .close-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .widget-content {
            padding: 20px;
            height: calc(100% - 54px);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .mode-selection {
            text-align: center;
            color: #00ffff;
        }

        .mode-selection h2 {
            margin-bottom: 30px;
            font-size: 24px;
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
        }

        .mode-btn {
            background: linear-gradient(45deg, rgba(0, 255, 255, 0.1), rgba(0, 128, 255, 0.1));
            border: 2px solid rgba(0, 255, 255, 0.5);
            color: #00ffff;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 15px;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s;
            backdrop-filter: blur(10px);
            text-shadow: 0 0 5px rgba(0, 255, 255, 0.8);
        }

        .mode-btn:hover {
            border-color: #00ffff;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .game-container {
            display: none;
            flex-direction: column;
            align-items: center;
            width: 100%;
            height: 100%;
        }

        .game-info {
            color: #00ffff;
            font-size: 18px;
            margin-bottom: 20px;
            text-align: center;
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
        }

        .game-board {
            position: relative;
            width: 280px;
            height: 280px;
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 0;
        }

        .game-board::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                /* Vertical lines */
                linear-gradient(90deg, transparent calc(33.33% - 1px), rgba(0, 255, 255, 0.8) 33.33%, rgba(0, 255, 255, 0.8) calc(33.33% + 1px), transparent calc(33.33% + 1px)),
                linear-gradient(90deg, transparent calc(66.66% - 1px), rgba(0, 255, 255, 0.8) 66.66%, rgba(0, 255, 255, 0.8) calc(66.66% + 1px), transparent calc(66.66% + 1px)),
                /* Horizontal lines */
                linear-gradient(0deg, transparent calc(33.33% - 1px), rgba(0, 255, 255, 0.8) 33.33%, rgba(0, 255, 255, 0.8) calc(33.33% + 1px), transparent calc(33.33% + 1px)),
                linear-gradient(0deg, transparent calc(66.66% - 1px), rgba(0, 255, 255, 0.8) 66.66%, rgba(0, 255, 255, 0.8) calc(66.66% + 1px), transparent calc(66.66% + 1px));
            filter: drop-shadow(0 0 4px rgba(0, 255, 255, 0.6));
            pointer-events: none;
            z-index: 1;
        }

        .cell {
            background: transparent;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            z-index: 2;
        }

        .cell:hover {
            background: rgba(0, 255, 255, 0.1);
            transform: scale(1.05);
        }

        .cell.x {
            color: #00ffff;
            text-shadow: 0 0 20px rgba(0, 255, 255, 1);
        }

        .cell.o {
            color: #00ffff;
            text-shadow: 0 0 20px rgba(0, 255, 255, 1);
        }

        .cell.disabled {
            cursor: not-allowed;
            opacity: 0.6;
        }

        .game-controls {
            display: flex;
            gap: 15px;
            margin-top: 10px;
        }

        .control-btn {
            background: linear-gradient(45deg, rgba(0, 255, 255, 0.1), rgba(0, 128, 255, 0.1));
            border: 2px solid rgba(0, 255, 255, 0.5);
            color: #00ffff;
            padding: 10px 20px;
            border-radius: 10px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            backdrop-filter: blur(10px);
            text-shadow: 0 0 5px rgba(0, 255, 255, 0.8);
        }

        .control-btn:hover {
            border-color: #00ffff;
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .ai-setup {
            display: none;
            text-align: center;
            color: #00ffff;
        }

        .ai-setup h3 {
            margin-bottom: 20px;
            font-size: 20px;
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
        }

        .symbol-choice {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 30px;
        }

        .symbol-btn {
            background: linear-gradient(45deg, rgba(0, 255, 255, 0.1), rgba(0, 128, 255, 0.1));
            border: 2px solid rgba(0, 255, 255, 0.5);
            color: #00ffff;
            padding: 20px;
            border-radius: 15px;
            font-size: 24px;
            cursor: pointer;
            transition: all 0.3s;
            backdrop-filter: blur(10px);
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .symbol-btn:hover {
            border-color: #00ffff;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
            transform: scale(1.1);
        }

        .symbol-btn.selected {
            background: linear-gradient(45deg, rgba(0, 255, 255, 0.3), rgba(0, 128, 255, 0.3));
            border-color: #00ffff;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }

        .resize-handle {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 20px;
            height: 20px;
            cursor: se-resize;
            background: linear-gradient(135deg, transparent 0%, rgba(0, 255, 255, 0.3) 100%);
        }

        .resize-handle::after {
            content: '';
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-bottom: 8px solid rgba(0, 255, 255, 0.6);
        }

        .win-animation {
            animation: pulse 1s ease-in-out infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="widget" id="widget">
        <div class="widget-header">
            <span>TIC-TAC-TOE</span>
            <button class="close-btn" onclick="closeWidget()">×</button>
        </div>
        <div class="widget-content">
            <!-- Mode Selection Screen -->
            <div class="mode-selection" id="modeSelection">
                <h2>Choose Game Mode</h2>
                <button class="mode-btn" onclick="selectMode('ai')">Play vs AI</button>
                <button class="mode-btn" onclick="selectMode('multiplayer')">Play Multiplayer</button>
            </div>

            <!-- AI Setup Screen -->
            <div class="ai-setup" id="aiSetup">
                <h3>Choose Your Symbol</h3>
                <div class="symbol-choice">
                    <button class="symbol-btn" id="chooseX" onclick="chooseSymbol('X')">X</button>
                    <button class="symbol-btn" id="chooseO" onclick="chooseSymbol('O')">O</button>
                </div>
                <button class="control-btn" onclick="startAIGame()">Start Game</button>
                <button class="control-btn" onclick="backToModeSelection()">Back</button>
            </div>

            <!-- Game Screen -->
            <div class="game-container" id="gameContainer">
                <div class="game-info" id="gameInfo">Player X's Turn</div>
                <div class="game-board" id="gameBoard">
                    <div class="cell" data-index="0" onclick="makeMove(0)"></div>
                    <div class="cell" data-index="1" onclick="makeMove(1)"></div>
                    <div class="cell" data-index="2" onclick="makeMove(2)"></div>
                    <div class="cell" data-index="3" onclick="makeMove(3)"></div>
                    <div class="cell" data-index="4" onclick="makeMove(4)"></div>
                    <div class="cell" data-index="5" onclick="makeMove(5)"></div>
                    <div class="cell" data-index="6" onclick="makeMove(6)"></div>
                    <div class="cell" data-index="7" onclick="makeMove(7)"></div>
                    <div class="cell" data-index="8" onclick="makeMove(8)"></div>
                </div>
                <div class="game-controls">
                    <button class="control-btn" onclick="newGame()">New Game</button>
                    <button class="control-btn" onclick="backToModeSelection()">Back to Menu</button>
                </div>
            </div>
        </div>
        <div class="resize-handle" id="resizeHandle"></div>
    </div>

    <script>
        // Game state variables
        let gameMode = null;
        let currentPlayer = 'X';
        let gameBoard = Array(9).fill('');
        let gameActive = true;
        let playerSymbol = 'X';
        let aiSymbol = 'O';
        let selectedSymbol = null;

        // Widget dragging functionality
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };
        let isResizing = false;
        let resizeStart = { x: 0, y: 0, width: 0, height: 0 };

        const widget = document.getElementById('widget');
        const resizeHandle = document.getElementById('resizeHandle');

        // Make widget draggable
        widget.addEventListener('mousedown', (e) => {
            if (e.target === resizeHandle || e.target.closest('.resize-handle')) return;
            if (e.target.closest('.widget-content')) return;
            
            isDragging = true;
            const rect = widget.getBoundingClientRect();
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;
            widget.style.cursor = 'grabbing';
        });

        // Make widget resizable
        resizeHandle.addEventListener('mousedown', (e) => {
            e.stopPropagation();
            isResizing = true;
            const rect = widget.getBoundingClientRect();
            resizeStart.x = e.clientX;
            resizeStart.y = e.clientY;
            resizeStart.width = rect.width;
            resizeStart.height = rect.height;
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                const x = e.clientX - dragOffset.x;
                const y = e.clientY - dragOffset.y;
                widget.style.left = `${x}px`;
                widget.style.top = `${y}px`;
                widget.style.position = 'fixed';
            }
            
            if (isResizing) {
                const deltaX = e.clientX - resizeStart.x;
                const deltaY = e.clientY - resizeStart.y;
                const newWidth = Math.max(350, resizeStart.width + deltaX);
                const newHeight = Math.max(450, resizeStart.height + deltaY);
                
                widget.style.width = `${newWidth}px`;
                widget.style.height = `${newHeight}px`;
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
            isResizing = false;
            widget.style.cursor = 'move';
        });

        // Game functions
        function selectMode(mode) {
            gameMode = mode;
            document.getElementById('modeSelection').style.display = 'none';
            
            if (mode === 'ai') {
                document.getElementById('aiSetup').style.display = 'block';
            } else {
                startMultiplayerGame();
            }
        }

        function chooseSymbol(symbol) {
            selectedSymbol = symbol;
            document.querySelectorAll('.symbol-btn').forEach(btn => btn.classList.remove('selected'));
            document.getElementById(symbol === 'X' ? 'chooseX' : 'chooseO').classList.add('selected');
        }

        function startAIGame() {
            if (!selectedSymbol) {
                alert('Please choose your symbol first!');
                return;
            }
            
            playerSymbol = selectedSymbol;
            aiSymbol = selectedSymbol === 'X' ? 'O' : 'X';
            currentPlayer = 'X';
            
            document.getElementById('aiSetup').style.display = 'none';
            document.getElementById('gameContainer').style.display = 'flex';
            
            updateGameInfo();
            resetBoard();
            
            // If AI goes first (X), make AI move
            if (aiSymbol === 'X') {
                setTimeout(makeAIMove, 500);
            }
        }

        function startMultiplayerGame() {
            gameMode = 'multiplayer';
            currentPlayer = 'X';
            
            document.getElementById('modeSelection').style.display = 'none';
            document.getElementById('gameContainer').style.display = 'flex';
            
            updateGameInfo();
            resetBoard();
        }

        function backToModeSelection() {
            document.getElementById('aiSetup').style.display = 'none';
            document.getElementById('gameContainer').style.display = 'none';
            document.getElementById('modeSelection').style.display = 'block';
            
            // Reset game state
            gameMode = null;
            currentPlayer = 'X';
            gameBoard = Array(9).fill('');
            gameActive = true;
            selectedSymbol = null;
            document.querySelectorAll('.symbol-btn').forEach(btn => btn.classList.remove('selected'));
        }

        function makeMove(index) {
            if (!gameActive || gameBoard[index] !== '') return;
            
            // In AI mode, prevent moves when it's AI's turn
            if (gameMode === 'ai' && currentPlayer === aiSymbol) return;
            
            gameBoard[index] = currentPlayer;
            updateBoard();
            
            if (checkWin()) {
                gameActive = false;
                highlightWinningCells();
                updateGameInfo();
                return;
            }
            
            if (checkDraw()) {
                gameActive = false;
                updateGameInfo();
                return;
            }
            
            currentPlayer = currentPlayer === 'X' ? 'O' : 'X';
            updateGameInfo();
            
            // Make AI move after player move
            if (gameMode === 'ai' && gameActive && currentPlayer === aiSymbol) {
                setTimeout(makeAIMove, 500);
            }
        }

        function makeAIMove() {
            if (!gameActive) return;
            
            const bestMove = getBestMove();
            if (bestMove !== -1) {
                gameBoard[bestMove] = aiSymbol;
                updateBoard();
                
                if (checkWin()) {
                    gameActive = false;
                    highlightWinningCells();
                    updateGameInfo();
                    return;
                }
                
                if (checkDraw()) {
                    gameActive = false;
                    updateGameInfo();
                    return;
                }
                
                currentPlayer = currentPlayer === 'X' ? 'O' : 'X';
                updateGameInfo();
            }
        }

        function getBestMove() {
            // Simple AI strategy: Try to win, block player win, or take center/corner
            
            // Check if AI can win
            for (let i = 0; i < 9; i++) {
                if (gameBoard[i] === '') {
                    gameBoard[i] = aiSymbol;
                    if (checkWin()) {
                        gameBoard[i] = '';
                        return i;
                    }
                    gameBoard[i] = '';
                }
            }
            
            // Check if player can win and block
            for (let i = 0; i < 9; i++) {
                if (gameBoard[i] === '') {
                    gameBoard[i] = playerSymbol;
                    if (checkWin()) {
                        gameBoard[i] = '';
                        return i;
                    }
                    gameBoard[i] = '';
                }
            }
            
            // Take center if available
            if (gameBoard[4] === '') return 4;
            
            // Take corners
            const corners = [0, 2, 6, 8];
            for (let corner of corners) {
                if (gameBoard[corner] === '') return corner;
            }
            
            // Take any available spot
            for (let i = 0; i < 9; i++) {
                if (gameBoard[i] === '') return i;
            }
            
            return -1;
        }

        function updateBoard() {
            const cells = document.querySelectorAll('.cell');
            cells.forEach((cell, index) => {
                cell.textContent = gameBoard[index];
                cell.className = 'cell';
                if (gameBoard[index] === 'X') cell.classList.add('x');
                if (gameBoard[index] === 'O') cell.classList.add('o');
                if (gameBoard[index] !== '') cell.classList.add('disabled');
            });
        }

        function updateGameInfo() {
            const gameInfo = document.getElementById('gameInfo');
            
            if (!gameActive) {
                if (checkWin()) {
                    if (gameMode === 'ai') {
                        gameInfo.textContent = currentPlayer === playerSymbol ? 'You Win! 🎉' : 'AI Wins! 🤖';
                    } else {
                        gameInfo.textContent = `Player ${currentPlayer} Wins! 🎉`;
                    }
                } else {
                    gameInfo.textContent = "It's a Draw! 🤝";
                }
            } else {
                if (gameMode === 'ai') {
                    gameInfo.textContent = currentPlayer === playerSymbol ? 'Your Turn' : 'AI is thinking...';
                } else {
                    gameInfo.textContent = `Player ${currentPlayer}'s Turn`;
                }
            }
        }

        function checkWin() {
            const winPatterns = [
                [0, 1, 2], [3, 4, 5], [6, 7, 8], // Rows
                [0, 3, 6], [1, 4, 7], [2, 5, 8], // Columns
                [0, 4, 8], [2, 4, 6] // Diagonals
            ];
            
            return winPatterns.some(pattern => {
                const [a, b, c] = pattern;
                return gameBoard[a] && gameBoard[a] === gameBoard[b] && gameBoard[a] === gameBoard[c];
            });
        }

        function checkDraw() {
            return gameBoard.every(cell => cell !== '');
        }

        function highlightWinningCells() {
            const winPatterns = [
                [0, 1, 2], [3, 4, 5], [6, 7, 8],
                [0, 3, 6], [1, 4, 7], [2, 5, 8],
                [0, 4, 8], [2, 4, 6]
            ];
            
            winPatterns.forEach(pattern => {
                const [a, b, c] = pattern;
                if (gameBoard[a] && gameBoard[a] === gameBoard[b] && gameBoard[a] === gameBoard[c]) {
                    const cells = document.querySelectorAll('.cell');
                    cells[a].classList.add('win-animation');
                    cells[b].classList.add('win-animation');
                    cells[c].classList.add('win-animation');
                }
            });
        }

        function newGame() {
            gameBoard = Array(9).fill('');
            gameActive = true;
            currentPlayer = 'X';
            
            resetBoard();
            updateGameInfo();
            
            // If AI goes first in new game
            if (gameMode === 'ai' && aiSymbol === 'X') {
                setTimeout(makeAIMove, 500);
            }
        }

        function resetBoard() {
            const cells = document.querySelectorAll('.cell');
            cells.forEach(cell => {
                cell.textContent = '';
                cell.className = 'cell';
            });
        }

        function closeWidget() {
            // In a real application, this would close/hide the widget
            alert('Widget would be closed in a real application');
        }

        // Initialize the widget
        document.addEventListener('DOMContentLoaded', () => {
            // Center the widget initially
            const widget = document.getElementById('widget');
            widget.style.position = 'fixed';
            widget.style.left = '50%';
            widget.style.top = '50%';
            widget.style.transform = 'translate(-50%, -50%)';
        });
    </script>
</body>
</html>