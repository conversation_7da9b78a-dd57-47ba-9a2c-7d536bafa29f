"""
Enhanced Logging Utility for Nova AI Server
Provides comprehensive logging setup with rotation, formatting, and multiple handlers
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional
import json
from datetime import datetime

class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'getMessage', 'exc_info', 
                          'exc_text', 'stack_info']:
                log_entry[key] = value
        
        return json.dumps(log_entry)

class ColoredFormatter(logging.Formatter):
    """Colored formatter for console output"""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',      # Cyan
        'INFO': '\033[32m',       # Green
        'WARNING': '\033[33m',    # Yellow
        'ERROR': '\033[31m',      # Red
        'CRITICAL': '\033[35m',   # Magenta
        'RESET': '\033[0m'        # Reset
    }
    
    def format(self, record):
        # Add color to level name
        level_color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        record.levelname = f"{level_color}{record.levelname}{self.COLORS['RESET']}"
        
        # Format the message
        formatted = super().format(record)
        
        return formatted

def setup_logging(config: Dict[str, Any]) -> logging.Logger:
    """
    Setup comprehensive logging configuration
    
    Args:
        config: Logging configuration dictionary
        
    Returns:
        Configured root logger
    """
    
    # Get configuration values
    log_level = config.get('level', 'INFO').upper()
    log_format = config.get('format', '%(asctime)s [%(levelname)s] %(name)s: %(message)s')
    log_file = config.get('file', 'logs/nova_ai.log')
    max_size = config.get('max_size', '10MB')
    backup_count = config.get('backup_count', 5)
    json_logging = config.get('json_format', False)
    console_logging = config.get('console', True)
    colored_console = config.get('colored_console', True)
    
    # Create logs directory
    log_dir = Path(log_file).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # File handler with rotation
    if log_file:
        # Parse max_size
        if isinstance(max_size, str):
            if max_size.endswith('MB'):
                max_bytes = int(max_size[:-2]) * 1024 * 1024
            elif max_size.endswith('KB'):
                max_bytes = int(max_size[:-2]) * 1024
            else:
                max_bytes = int(max_size)
        else:
            max_bytes = max_size
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        
        if json_logging:
            file_formatter = JSONFormatter()
        else:
            file_formatter = logging.Formatter(log_format)
        
        file_handler.setFormatter(file_formatter)
        file_handler.setLevel(getattr(logging, log_level))
        root_logger.addHandler(file_handler)
    
    # Console handler
    if console_logging:
        console_handler = logging.StreamHandler(sys.stdout)
        
        if colored_console and sys.stdout.isatty():
            console_formatter = ColoredFormatter(log_format)
        else:
            console_formatter = logging.Formatter(log_format)
        
        console_handler.setFormatter(console_formatter)
        console_handler.setLevel(getattr(logging, log_level))
        root_logger.addHandler(console_handler)
    
    # Error file handler (separate file for errors)
    error_file = config.get('error_file')
    if error_file:
        error_dir = Path(error_file).parent
        error_dir.mkdir(parents=True, exist_ok=True)
        
        error_handler = logging.handlers.RotatingFileHandler(
            error_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        
        if json_logging:
            error_formatter = JSONFormatter()
        else:
            error_formatter = logging.Formatter(log_format)
        
        error_handler.setFormatter(error_formatter)
        error_handler.setLevel(logging.ERROR)
        root_logger.addHandler(error_handler)
    
    # Configure specific loggers
    logger_configs = config.get('loggers', {})
    for logger_name, logger_config in logger_configs.items():
        logger = logging.getLogger(logger_name)
        logger.setLevel(getattr(logging, logger_config.get('level', log_level)))
        
        # Add specific handlers if needed
        if logger_config.get('file'):
            specific_handler = logging.handlers.RotatingFileHandler(
                logger_config['file'],
                maxBytes=max_bytes,
                backupCount=backup_count,
                encoding='utf-8'
            )
            specific_handler.setFormatter(file_formatter)
            logger.addHandler(specific_handler)
    
    # Suppress noisy loggers
    noisy_loggers = [
        'urllib3.connectionpool',
        'requests.packages.urllib3.connectionpool',
        'werkzeug',
        'flask.app'
    ]
    
    for logger_name in noisy_loggers:
        logging.getLogger(logger_name).setLevel(logging.WARNING)
    
    # Log startup message
    logger = logging.getLogger(__name__)
    logger.info("Enhanced logging system initialized")
    logger.info(f"Log level: {log_level}")
    logger.info(f"Log file: {log_file}")
    logger.info(f"JSON logging: {json_logging}")
    logger.info(f"Console logging: {console_logging}")
    
    return root_logger

class LoggerMixin:
    """Mixin class to add logging capabilities to any class"""
    
    @property
    def logger(self):
        """Get logger for this class"""
        if not hasattr(self, '_logger'):
            self._logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        return self._logger

class ContextLogger:
    """Context manager for adding context to log messages"""
    
    def __init__(self, logger: logging.Logger, **context):
        self.logger = logger
        self.context = context
        self.old_factory = None
    
    def __enter__(self):
        self.old_factory = logging.getLogRecordFactory()
        
        def record_factory(*args, **kwargs):
            record = self.old_factory(*args, **kwargs)
            for key, value in self.context.items():
                setattr(record, key, value)
            return record
        
        logging.setLogRecordFactory(record_factory)
        return self.logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        logging.setLogRecordFactory(self.old_factory)

def get_logger(name: str, **context) -> logging.Logger:
    """
    Get a logger with optional context
    
    Args:
        name: Logger name
        **context: Additional context to add to log records
        
    Returns:
        Logger instance
    """
    logger = logging.getLogger(name)
    
    if context:
        return ContextLogger(logger, **context)
    
    return logger

def log_function_call(logger: Optional[logging.Logger] = None, level: int = logging.DEBUG):
    """
    Decorator to log function calls
    
    Args:
        logger: Logger to use (defaults to function's module logger)
        level: Log level to use
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            func_logger = logger or logging.getLogger(func.__module__)
            
            # Log function entry
            func_logger.log(level, f"Calling {func.__name__} with args={args}, kwargs={kwargs}")
            
            try:
                result = func(*args, **kwargs)
                func_logger.log(level, f"{func.__name__} completed successfully")
                return result
            except Exception as e:
                func_logger.error(f"{func.__name__} failed with error: {e}")
                raise
        
        return wrapper
    return decorator

def log_performance(logger: Optional[logging.Logger] = None, level: int = logging.INFO):
    """
    Decorator to log function performance
    
    Args:
        logger: Logger to use (defaults to function's module logger)
        level: Log level to use
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            import time
            
            func_logger = logger or logging.getLogger(func.__module__)
            
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                func_logger.log(level, f"{func.__name__} executed in {execution_time:.4f} seconds")
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                func_logger.error(f"{func.__name__} failed after {execution_time:.4f} seconds: {e}")
                raise
        
        return wrapper
    return decorator

class LogCapture:
    """Utility class to capture log messages for testing"""
    
    def __init__(self, logger_name: str = None, level: int = logging.DEBUG):
        self.logger_name = logger_name
        self.level = level
        self.handler = None
        self.records = []
    
    def __enter__(self):
        self.handler = logging.Handler()
        self.handler.setLevel(self.level)
        self.handler.emit = self.records.append
        
        if self.logger_name:
            logger = logging.getLogger(self.logger_name)
        else:
            logger = logging.getLogger()
        
        logger.addHandler(self.handler)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.logger_name:
            logger = logging.getLogger(self.logger_name)
        else:
            logger = logging.getLogger()
        
        logger.removeHandler(self.handler)
    
    def get_messages(self, level: int = None) -> list:
        """Get captured log messages"""
        if level is None:
            return [record.getMessage() for record in self.records]
        else:
            return [record.getMessage() for record in self.records if record.levelno >= level]
