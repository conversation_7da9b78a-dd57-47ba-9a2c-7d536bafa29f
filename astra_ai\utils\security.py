"""
Security Manager for Enhanced Nova AI Server
Handles security headers, input validation, and security policies
"""

import logging
import re
import hashlib
import secrets
from typing import Dict, Any, List, Optional
from flask import request, Response
import html
import urllib.parse

class SecurityManager:
    """
    Security manager for handling various security aspects
    """
    
    def __init__(self, config):
        """Initialize security manager"""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Security configuration
        security_config = config.get('security', {})
        self.cors_origins = security_config.get('cors_origins', [])
        self.ssl_enabled = security_config.get('ssl_enabled', False)
        
        # Content Security Policy
        self.csp_policy = self._build_csp_policy()
        
        # Input validation patterns
        self.validation_patterns = {
            'email': re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'),
            'username': re.compile(r'^[a-zA-Z0-9_-]{3,50}$'),
            'session_id': re.compile(r'^[a-zA-Z0-9-]{8,128}$'),
            'api_key': re.compile(r'^nova_ai_[a-zA-Z0-9_-]{32,}$'),
            'safe_string': re.compile(r'^[a-zA-Z0-9\s\-_.,:;!?()]+$')
        }
        
        # Blocked patterns (potential security threats)
        self.blocked_patterns = [
            re.compile(r'<script[^>]*>.*?</script>', re.IGNORECASE | re.DOTALL),
            re.compile(r'javascript:', re.IGNORECASE),
            re.compile(r'on\w+\s*=', re.IGNORECASE),
            re.compile(r'eval\s*\(', re.IGNORECASE),
            re.compile(r'expression\s*\(', re.IGNORECASE),
            re.compile(r'vbscript:', re.IGNORECASE),
            re.compile(r'data:text/html', re.IGNORECASE)
        ]
        
        self.logger.info("Security Manager initialized")
    
    def _build_csp_policy(self) -> str:
        """Build Content Security Policy"""
        policy_parts = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
            "style-src 'self' 'unsafe-inline'",
            "img-src 'self' data: blob:",
            "font-src 'self'",
            "connect-src 'self' ws: wss:",
            "media-src 'self'",
            "object-src 'none'",
            "base-uri 'self'",
            "form-action 'self'"
        ]
        
        return "; ".join(policy_parts)
    
    def add_security_headers(self, response: Response) -> Response:
        """Add security headers to response"""
        try:
            # Content Security Policy
            response.headers['Content-Security-Policy'] = self.csp_policy
            
            # XSS Protection
            response.headers['X-XSS-Protection'] = '1; mode=block'
            
            # Content Type Options
            response.headers['X-Content-Type-Options'] = 'nosniff'
            
            # Frame Options
            response.headers['X-Frame-Options'] = 'DENY'
            
            # Referrer Policy
            response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
            
            # Permissions Policy
            response.headers['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'
            
            # HSTS (if SSL is enabled)
            if self.ssl_enabled:
                response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
            
            # Remove server information
            response.headers.pop('Server', None)
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error adding security headers: {e}")
            return response
    
    def validate_request(self) -> bool:
        """Validate incoming request for security threats"""
        try:
            # Check request size
            if request.content_length and request.content_length > 50 * 1024 * 1024:  # 50MB limit
                self.logger.warning(f"Request too large: {request.content_length} bytes")
                return False
            
            # Check for suspicious headers
            suspicious_headers = ['x-forwarded-for', 'x-real-ip', 'x-originating-ip']
            for header in suspicious_headers:
                if header in request.headers:
                    value = request.headers[header]
                    if not self._validate_ip_address(value):
                        self.logger.warning(f"Suspicious header {header}: {value}")
                        # Don't block, just log
            
            # Check User-Agent
            user_agent = request.headers.get('User-Agent', '')
            if self._is_suspicious_user_agent(user_agent):
                self.logger.warning(f"Suspicious User-Agent: {user_agent}")
                # Don't block, just log
            
            # Check for SQL injection patterns in query parameters
            for key, value in request.args.items():
                if self._contains_sql_injection(value):
                    self.logger.warning(f"Potential SQL injection in query param {key}: {value}")
                    return False
            
            # Check for XSS patterns in query parameters
            for key, value in request.args.items():
                if self._contains_xss(value):
                    self.logger.warning(f"Potential XSS in query param {key}: {value}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating request: {e}")
            return True  # Allow request on error to avoid blocking legitimate traffic
    
    def sanitize_input(self, input_string: str, input_type: str = 'general') -> str:
        """Sanitize user input"""
        try:
            if not isinstance(input_string, str):
                return str(input_string)
            
            # HTML escape
            sanitized = html.escape(input_string)
            
            # URL decode to prevent double encoding attacks
            sanitized = urllib.parse.unquote(sanitized)
            
            # Remove null bytes
            sanitized = sanitized.replace('\x00', '')
            
            # Type-specific sanitization
            if input_type == 'filename':
                # Remove path traversal attempts
                sanitized = sanitized.replace('..', '').replace('/', '').replace('\\', '')
                # Remove dangerous characters
                sanitized = re.sub(r'[<>:"|?*]', '', sanitized)
            
            elif input_type == 'sql':
                # Basic SQL injection prevention
                sanitized = sanitized.replace("'", "''")
                sanitized = re.sub(r'\b(union|select|insert|update|delete|drop|create|alter)\b', '', sanitized, flags=re.IGNORECASE)
            
            elif input_type == 'command':
                # Command injection prevention
                dangerous_chars = ['&', '|', ';', '$', '`', '(', ')', '<', '>', '\n', '\r']
                for char in dangerous_chars:
                    sanitized = sanitized.replace(char, '')
            
            return sanitized
            
        except Exception as e:
            self.logger.error(f"Error sanitizing input: {e}")
            return ""
    
    def validate_input(self, input_string: str, pattern_name: str) -> bool:
        """Validate input against predefined patterns"""
        try:
            if pattern_name not in self.validation_patterns:
                self.logger.warning(f"Unknown validation pattern: {pattern_name}")
                return False
            
            pattern = self.validation_patterns[pattern_name]
            return bool(pattern.match(input_string))
            
        except Exception as e:
            self.logger.error(f"Error validating input: {e}")
            return False
    
    def _validate_ip_address(self, ip_string: str) -> bool:
        """Validate IP address format"""
        try:
            import ipaddress
            ipaddress.ip_address(ip_string)
            return True
        except ValueError:
            return False
    
    def _is_suspicious_user_agent(self, user_agent: str) -> bool:
        """Check if User-Agent is suspicious"""
        suspicious_patterns = [
            'sqlmap',
            'nikto',
            'nmap',
            'masscan',
            'curl',  # Be careful with this one
            'wget',
            'python-requests',
            'bot',
            'crawler',
            'spider'
        ]
        
        user_agent_lower = user_agent.lower()
        return any(pattern in user_agent_lower for pattern in suspicious_patterns)
    
    def _contains_sql_injection(self, input_string: str) -> bool:
        """Check for SQL injection patterns"""
        sql_patterns = [
            r'\bunion\b.*\bselect\b',
            r'\bselect\b.*\bfrom\b',
            r'\binsert\b.*\binto\b',
            r'\bupdate\b.*\bset\b',
            r'\bdelete\b.*\bfrom\b',
            r'\bdrop\b.*\btable\b',
            r';\s*--',
            r'/\*.*\*/',
            r'\bor\b.*=.*\bor\b',
            r'\band\b.*=.*\band\b'
        ]
        
        input_lower = input_string.lower()
        return any(re.search(pattern, input_lower, re.IGNORECASE) for pattern in sql_patterns)
    
    def _contains_xss(self, input_string: str) -> bool:
        """Check for XSS patterns"""
        return any(pattern.search(input_string) for pattern in self.blocked_patterns)
    
    def generate_csrf_token(self) -> str:
        """Generate CSRF token"""
        return secrets.token_urlsafe(32)
    
    def validate_csrf_token(self, token: str, expected_token: str) -> bool:
        """Validate CSRF token"""
        try:
            return secrets.compare_digest(token, expected_token)
        except Exception:
            return False
    
    def hash_password(self, password: str, salt: Optional[str] = None) -> tuple:
        """Hash password with salt"""
        try:
            if salt is None:
                salt = secrets.token_hex(16)
            
            # Use PBKDF2 for password hashing
            password_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
            return password_hash.hex(), salt
            
        except Exception as e:
            self.logger.error(f"Error hashing password: {e}")
            return None, None
    
    def verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        """Verify password against hash"""
        try:
            computed_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
            return secrets.compare_digest(computed_hash.hex(), password_hash)
        except Exception as e:
            self.logger.error(f"Error verifying password: {e}")
            return False
    
    def check_rate_limit(self, identifier: str, limit: int, window: int) -> bool:
        """Check if request is within rate limit"""
        try:
            # This is a simple in-memory implementation
            # In production, use Redis or database
            if not hasattr(self, 'rate_limit_storage'):
                self.rate_limit_storage = {}
            
            current_time = int(time.time())
            window_start = current_time - window
            
            # Clean old entries
            if identifier in self.rate_limit_storage:
                self.rate_limit_storage[identifier] = [
                    timestamp for timestamp in self.rate_limit_storage[identifier]
                    if timestamp > window_start
                ]
            else:
                self.rate_limit_storage[identifier] = []
            
            # Check limit
            if len(self.rate_limit_storage[identifier]) >= limit:
                return False
            
            # Add current request
            self.rate_limit_storage[identifier].append(current_time)
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking rate limit: {e}")
            return True  # Allow on error
    
    def get_client_ip(self) -> str:
        """Get client IP address"""
        try:
            # Check for forwarded headers (be careful with these in production)
            forwarded_headers = [
                'X-Forwarded-For',
                'X-Real-IP',
                'X-Originating-IP',
                'CF-Connecting-IP'  # Cloudflare
            ]
            
            for header in forwarded_headers:
                if header in request.headers:
                    ip = request.headers[header].split(',')[0].strip()
                    if self._validate_ip_address(ip):
                        return ip
            
            # Fallback to remote address
            return request.remote_addr or 'unknown'
            
        except Exception as e:
            self.logger.error(f"Error getting client IP: {e}")
            return 'unknown'
    
    def log_security_event(self, event_type: str, details: Dict[str, Any]):
        """Log security event"""
        try:
            security_log = {
                'event_type': event_type,
                'timestamp': time.time(),
                'client_ip': self.get_client_ip(),
                'user_agent': request.headers.get('User-Agent', ''),
                'request_path': request.path,
                'request_method': request.method,
                'details': details
            }
            
            self.logger.warning(f"Security Event: {event_type}", extra=security_log)
            
        except Exception as e:
            self.logger.error(f"Error logging security event: {e}")
    
    def health_check(self) -> Dict[str, Any]:
        """Perform security manager health check"""
        try:
            # Test basic functionality
            test_input = "<script>alert('test')</script>"
            sanitized = self.sanitize_input(test_input)
            
            if "<script>" in sanitized:
                return {'status': 'unhealthy', 'error': 'Input sanitization failed'}
            
            # Test validation
            if not self.validate_input("<EMAIL>", "email"):
                return {'status': 'unhealthy', 'error': 'Input validation failed'}
            
            return {
                'status': 'healthy',
                'csp_enabled': bool(self.csp_policy),
                'ssl_enabled': self.ssl_enabled,
                'validation_patterns': len(self.validation_patterns)
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }
