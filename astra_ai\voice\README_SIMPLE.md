# Simple Unified Voice System

A clean, simple voice synthesis system that uses only **two JSON files** to eliminate duplicate processing.

## Overview

This system replaces the complex dual-file approach with a simple, reliable solution:

- **`unified_messages.json`** - Contains ALL AI responses (chat + camera)
- **`processed.json`** - Tracks which messages have been read by voice system

## How It Works

```
1. Chat/Camera → Add message to unified_messages.json
2. Voice Monitor → Reads unified_messages.json
3. Voice Monitor → Checks processed.json for already-read messages
4. Voice Monitor → Synthesizes ONLY new messages
5. Voice Monitor → Adds message_id to processed.json after speaking
6. Voice Monitor → Waits for next new message
```

## File Structure

### unified_messages.json
```json
[
  {
    "message_id": "chat_1753094417333_0c19175c",
    "message_type": "chat",
    "timestamp": 1753094417.333,
    "user_message": "Hello",
    "ai_response": "Hi there!",
    "session_id": "session1",
    "voice_required": true
  },
  {
    "message_id": "camera_1753094417334_14b31d27", 
    "message_type": "camera",
    "timestamp": 1753094417.334,
    "user_message": "what do you see?",
    "ai_response": "I see a computer screen",
    "session_id": "camera_session",
    "voice_required": true
  }
]
```

### processed.json
```json
{
  "processed_message_ids": [
    "chat_1753094417333_0c19175c",
    "camera_1753094417334_14b31d27"
  ],
  "last_updated": 1753094420.123,
  "total_processed": 2
}
```

## Key Features

### ✅ **No Duplicates**
- Each message gets unique ID: `{type}_{timestamp}_{uuid}`
- Voice system checks `processed.json` before speaking
- Once spoken, message ID added to `processed.json`
- Never speaks the same message twice

### ✅ **Simple Integration**
- Chat responses: `add_chat_message(user_msg, ai_response, session_id)`
- Camera analysis: `add_camera_message(ai_response, session_id)`
- Automatic voice synthesis for all new messages

### ✅ **Rate Limiting**
- 3-second minimum between voice synthesis
- Automatic retry for API rate limits
- Graceful error handling

### ✅ **Clean Monitoring**
- Single thread monitors `unified_messages.json`
- Processes only unread messages
- Updates `processed.json` after successful synthesis

## Usage in Nova AI

### Chat Integration
```python
# In chat endpoint
if SIMPLE_VOICE_AVAILABLE:
    message_id = add_chat_message(user_message, ai_response, session_id)
    if message_id:
        print(f"Chat message added: {message_id}")
```

### Camera Integration  
```python
# In camera processing
if SIMPLE_VOICE_AVAILABLE:
    message_id = add_camera_message(analysis_text, session_id)
    if message_id:
        print(f"Camera message added: {message_id}")
```

## Benefits

### 🚫 **Eliminates Duplicate Processing**
- Old system: Messages processed multiple times
- New system: Each message processed exactly once

### ⚡ **Better Performance**
- Old system: Multiple JSON files, complex tracking
- New system: Two simple files, clean logic

### 🔒 **Reliable Operation**
- Old system: Race conditions, duplicate synthesis
- New system: Thread-safe, predictable behavior

### 📊 **Easy Monitoring**
- Check `unified_messages.json` for all messages
- Check `processed.json` for synthesis status
- Simple status reporting

## File Locations

```
astra_ai/ui/
├── unified_messages.json    # All AI responses
└── processed.json          # Processed message tracking
```

## Voice System Status

The system provides real-time status:

```python
status = get_voice_status()
# Returns:
{
    'running': True,
    'cartesia_available': True, 
    'audio_available': True,
    'total_messages': 10,
    'processed_messages': 8,
    'pending_messages': 2
}
```

## Error Handling

### Rate Limiting
- Automatic 3-second delays between synthesis
- Retry logic for 429 rate limit errors
- Graceful degradation on API failures

### File Operations
- Thread-safe JSON file operations
- Automatic recovery from file corruption
- Backup and restore capabilities

### Audio System
- Automatic audio device detection
- Fallback to system default audio
- Error recovery and reconnection

## Testing

Run the test suite:
```bash
cd astra_ai/voice
python test_simple_voice.py
```

Tests verify:
- ✅ Message creation and storage
- ✅ JSON file structure validation
- ✅ Duplicate prevention logic
- ✅ Processing state tracking

## Migration from Old System

The new system automatically replaces:
- ❌ `ai_responses.json` → ✅ `unified_messages.json`
- ❌ `camera.json` → ✅ `unified_messages.json`
- ❌ Multiple tracking files → ✅ `processed.json`

## Troubleshooting

### Voice Not Working
1. Check Cartesia API key
2. Verify audio system initialization
3. Check `processed.json` for stuck messages

### Messages Not Being Spoken
1. Check `unified_messages.json` contains messages
2. Verify `voice_required: true` in messages
3. Check `processed.json` - message might already be processed

### Duplicate Voice Synthesis
- This should NOT happen with the new system
- If it does, check for multiple voice system instances

## Performance

### Memory Usage
- Keeps only last 100 messages in `unified_messages.json`
- Automatic cleanup of old processed messages
- Minimal memory footprint

### Processing Speed
- Near-instant message addition
- 1-second monitoring interval
- Immediate voice synthesis for new messages

### Scalability
- Handles hundreds of messages efficiently
- Automatic file size management
- Thread-safe concurrent operations

## Summary

The Simple Unified Voice System provides:

1. **Two JSON files only**: `unified_messages.json` + `processed.json`
2. **No duplicate processing**: Each message spoken exactly once
3. **Simple integration**: Easy to add chat and camera messages
4. **Reliable operation**: Thread-safe, error-resistant
5. **Easy monitoring**: Clear status and file structure

This replaces the complex previous system with a clean, maintainable solution that eliminates duplicate voice synthesis while providing all the same functionality.
