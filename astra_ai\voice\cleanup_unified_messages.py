#!/usr/bin/env python3
"""
Cleanup script for unified_messages.json
Removes duplicates and standardizes format
"""

import json
import time
from pathlib import Path


def cleanup_unified_messages(ui_directory: str):
    """Clean up unified_messages.json file"""
    ui_dir = Path(ui_directory)
    unified_file = ui_dir / 'unified_messages.json'
    
    if not unified_file.exists():
        print("✅ No unified_messages.json file found - nothing to clean")
        return
    
    print(f"🧹 Cleaning up {unified_file}")
    
    try:
        # Load existing messages
        with open(unified_file, 'r', encoding='utf-8') as f:
            messages = json.load(f)
        
        print(f"📄 Found {len(messages)} messages")
        
        # Clean and deduplicate messages
        cleaned_messages = []
        seen_content = set()
        
        for msg in messages:
            # Extract key fields
            ai_response = msg.get('ai_response', '')
            message_type = msg.get('message_type', 'unknown')
            user_message = msg.get('user_message', '')
            session_id = msg.get('session_id', 'unknown')
            
            # Skip empty responses
            if not ai_response or len(ai_response.strip()) < 5:
                continue
            
            # Create content signature for duplicate detection (more aggressive)
            # Use just the AI response for duplicate detection to catch identical responses
            content_sig = f"{ai_response.strip()}"

            # Skip duplicates
            if content_sig in seen_content:
                print(f"🔄 Skipping duplicate: {ai_response[:30]}...")
                continue

            seen_content.add(content_sig)
            
            # Standardize message type
            if message_type in ['chat_response', 'chat']:
                standardized_type = 'chat'
            elif message_type in ['camera_analysis', 'camera']:
                standardized_type = 'camera'
            else:
                standardized_type = message_type

            # Standardize message format
            cleaned_message = {
                'message_id': msg.get('message_id', f"{standardized_type}_{int(time.time() * 1000)}"),
                'message_type': standardized_type,
                'timestamp': msg.get('timestamp', time.time()),
                'user_message': user_message,
                'ai_response': ai_response,
                'session_id': session_id,
                'voice_required': True
            }
            
            cleaned_messages.append(cleaned_message)
        
        print(f"✅ Cleaned: {len(messages)} → {len(cleaned_messages)} messages")
        
        # Keep only last 30 messages
        if len(cleaned_messages) > 30:
            cleaned_messages = cleaned_messages[-30:]
            print(f"📦 Kept last 30 messages")
        
        # Save cleaned messages
        with open(unified_file, 'w', encoding='utf-8') as f:
            json.dump(cleaned_messages, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Saved {len(cleaned_messages)} cleaned messages")
        
        # Show sample of cleaned messages
        print("\n📋 Sample of cleaned messages:")
        for i, msg in enumerate(cleaned_messages[-3:]):  # Show last 3
            print(f"   {i+1}. {msg['message_type']}: {msg['ai_response'][:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error cleaning unified messages: {e}")
        return False


def reset_processed_file(ui_directory: str):
    """Reset the processed.json file"""
    ui_dir = Path(ui_directory)
    processed_file = ui_dir / 'processed.json'
    
    if processed_file.exists():
        print(f"🗑️ Removing old processed.json")
        processed_file.unlink()
    
    # Create fresh processed.json
    fresh_data = {
        'processed_message_ids': [],
        'last_updated': time.time(),
        'total_processed': 0
    }
    
    with open(processed_file, 'w', encoding='utf-8') as f:
        json.dump(fresh_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Created fresh processed.json")


if __name__ == "__main__":
    # Clean up the UI directory
    ui_directory = str(Path(__file__).parent.parent / 'ui')
    
    print("🚀 Starting unified messages cleanup...")
    print("=" * 50)
    
    # Clean unified messages
    success = cleanup_unified_messages(ui_directory)
    
    if success:
        # Reset processed file
        reset_processed_file(ui_directory)
        
        print("=" * 50)
        print("✅ Cleanup completed successfully!")
        print("🎉 Voice system ready with clean files")
    else:
        print("=" * 50)
        print("❌ Cleanup failed")
