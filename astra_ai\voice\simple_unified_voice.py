#!/usr/bin/env python3
"""
Simple Unified Voice System
Two JSON files: unified_messages.json (all messages) + processed.json (read messages)
"""

import json
import time
import threading
import queue
from pathlib import Path
from typing import Dict, List, Set
import uuid

# Import voice synthesis components
try:
    from cartesia import Cartesia
    import pyaudio
    import base64
    import numpy as np
    CARTESIA_AVAILABLE = True
except ImportError as e:
    CARTESIA_AVAILABLE = False
    print(f"⚠️ Cartesia not available: {e}")


class SimpleUnifiedVoice:
    """Simple unified voice system with two JSON files"""
    
    def __init__(self, ui_directory: str):
        self.ui_dir = Path(ui_directory)
        self.ui_dir.mkdir(exist_ok=True)
        
        # File paths
        self.unified_messages_file = self.ui_dir / 'unified_messages.json'
        self.processed_file = self.ui_dir / 'processed.json'
        
        # Voice configuration
        self.voice_config = {
            'api_key': 'sk_car_quA8Xego3FiXMwXtDuyLcR',
            'voice_id': 'f114a467-c40a-4db8-964d-aaba89cd08fa',
            'model_id': 'sonic-english',
            'sample_rate': 48000,
            'volume_multiplier': 1.2
        }
        
        # Threading
        self.is_running = False
        self.monitor_thread = None
        self.audio_queue = queue.Queue()
        self.synthesis_lock = threading.Lock()
        
        # Rate limiting
        self.last_synthesis_time = 0
        self.min_synthesis_interval = 3.0
        
        # Tracking
        self.processed_messages: Set[str] = set()
        
        # Audio system
        self.cartesia_client = None
        self.audio_stream = None
        
        # Initialize
        self._load_processed_messages()
        self._initialize_cartesia()
        self._initialize_audio()
        
        print(f"🎤 Simple Unified Voice System initialized")
        print(f"   📄 Messages: {self.unified_messages_file}")
        print(f"   📋 Processed: {self.processed_file}")
    
    def _load_processed_messages(self):
        """Load processed message IDs"""
        try:
            if self.processed_file.exists():
                with open(self.processed_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.processed_messages = set(data.get('processed_message_ids', []))
                print(f"📋 Loaded {len(self.processed_messages)} processed messages")
            else:
                self.processed_messages = set()
                print("📋 No processed messages file found, starting fresh")
        except Exception as e:
            print(f"⚠️ Error loading processed messages: {e}")
            self.processed_messages = set()
    
    def _save_processed_messages(self):
        """Save processed message IDs"""
        try:
            data = {
                'processed_message_ids': list(self.processed_messages),
                'last_updated': time.time(),
                'total_processed': len(self.processed_messages)
            }
            with open(self.processed_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"⚠️ Error saving processed messages: {e}")
    
    def _initialize_cartesia(self):
        """Initialize Cartesia client"""
        try:
            if CARTESIA_AVAILABLE:
                self.cartesia_client = Cartesia(api_key=self.voice_config['api_key'])
                print("✅ Cartesia voice synthesis initialized")
                return True
            else:
                print("⚠️ Cartesia not available")
                return False
        except Exception as e:
            print(f"❌ Failed to initialize Cartesia: {e}")
            return False
    
    def _initialize_audio(self):
        """Initialize audio system"""
        try:
            import pyaudio
            self.audio = pyaudio.PyAudio()
            self.audio_stream = self.audio.open(
                format=pyaudio.paFloat32,
                channels=1,
                rate=self.voice_config['sample_rate'],
                output=True,
                frames_per_buffer=1024
            )
            print("✅ Audio system initialized")
            return True
        except Exception as e:
            print(f"❌ Failed to initialize audio: {e}")
            return False
    
    def add_message(self, message_type: str, user_message: str, ai_response: str, session_id: str) -> str:
        """Add a message to unified_messages.json with duplicate prevention"""
        try:
            # Check for duplicate content to prevent same message being added multiple times
            content_hash = hash(f"{message_type}_{ai_response}_{session_id}")

            # Load existing messages to check for duplicates
            messages = []
            if self.unified_messages_file.exists():
                try:
                    with open(self.unified_messages_file, 'r', encoding='utf-8') as f:
                        messages = json.load(f)
                except:
                    messages = []

            # Check for recent duplicate (within last 30 seconds) or exact duplicate
            current_time = time.time()
            for existing_msg in messages:
                # Check for exact AI response match (regardless of time)
                if existing_msg.get('ai_response', '').strip() == ai_response.strip():
                    print(f"🔄 Skipping duplicate AI response: {existing_msg.get('message_id', 'unknown')}")
                    return existing_msg.get('message_id')

                # Also check for recent similar messages
                if (existing_msg.get('ai_response') == ai_response and
                    existing_msg.get('message_type') == message_type and
                    current_time - existing_msg.get('timestamp', 0) < 30):
                    print(f"🔄 Skipping recent duplicate: {existing_msg.get('message_id', 'unknown')}")
                    return existing_msg.get('message_id')

            # Generate unique message ID
            message_id = f"{message_type}_{int(time.time() * 1000)}_{str(uuid.uuid4())[:8]}"

            # Create standardized message
            message = {
                'message_id': message_id,
                'message_type': message_type,  # 'chat' or 'camera'
                'timestamp': time.time(),
                'user_message': user_message,
                'ai_response': ai_response,
                'session_id': session_id,
                'voice_required': True
            }

            # Add new message
            messages.append(message)

            # Keep only last 50 messages to prevent file from growing too large
            if len(messages) > 50:
                messages = messages[-50:]

            # Save back to file
            with open(self.unified_messages_file, 'w', encoding='utf-8') as f:
                json.dump(messages, f, indent=2, ensure_ascii=False)

            print(f"✅ Message added: {message_id} ({message_type})")
            return message_id

        except Exception as e:
            print(f"❌ Error adding message: {e}")
            return None
    
    def start_voice_monitoring(self):
        """Start monitoring unified_messages.json for new messages"""
        if self.is_running:
            print("⚠️ Voice monitoring already running")
            return
        
        self.is_running = True
        self.monitor_thread = threading.Thread(target=self._monitor_messages, daemon=True)
        self.monitor_thread.start()
        
        # Start audio playback thread
        threading.Thread(target=self._audio_playback_loop, daemon=True).start()
        
        print("🎤 Voice monitoring started")
    
    def stop_voice_monitoring(self):
        """Stop voice monitoring"""
        self.is_running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        if self.audio_stream:
            self.audio_stream.close()
        if hasattr(self, 'audio'):
            self.audio.terminate()
        print("🔇 Voice monitoring stopped")
    
    def _monitor_messages(self):
        """Monitor unified_messages.json for new messages"""
        print("👁️ Starting message monitoring...")
        
        while self.is_running:
            try:
                if not self.unified_messages_file.exists():
                    time.sleep(1)
                    continue
                
                # Load messages
                with open(self.unified_messages_file, 'r', encoding='utf-8') as f:
                    messages = json.load(f)
                
                # Process unread messages
                for message in messages:
                    if not self.is_running:
                        break
                    
                    message_id = message.get('message_id')
                    if not message_id or message_id in self.processed_messages:
                        continue
                    
                    # Check if voice is required
                    if not message.get('voice_required', True):
                        continue
                    
                    ai_response = message.get('ai_response', '')
                    if len(ai_response.strip()) < 5:
                        continue
                    
                    # Process voice synthesis
                    success = self._synthesize_voice(ai_response, message_id)
                    
                    if success:
                        # Mark as processed
                        self.processed_messages.add(message_id)
                        self._save_processed_messages()
                        print(f"✅ Voice synthesis completed: {message_id}")
                    else:
                        print(f"❌ Voice synthesis failed: {message_id}")
                
                time.sleep(1)  # Check every second
                
            except Exception as e:
                print(f"❌ Error in message monitoring: {e}")
                time.sleep(5)
    
    def _synthesize_voice(self, text: str, message_id: str) -> bool:
        """Synthesize voice for text"""
        if not self.cartesia_client:
            return False
        
        try:
            with self.synthesis_lock:
                # Rate limiting
                current_time = time.time()
                time_since_last = current_time - self.last_synthesis_time
                
                if time_since_last < self.min_synthesis_interval:
                    sleep_time = self.min_synthesis_interval - time_since_last
                    print(f"⏳ Rate limiting: waiting {sleep_time:.1f}s...")
                    time.sleep(sleep_time)
                
                self.last_synthesis_time = time.time()
                print(f"🎤 Synthesizing: {text[:50]}...")
                
                # Clean text
                clean_text = self._clean_text(text)
                
                # Generate speech
                response = self.cartesia_client.tts.sse(
                    model_id=self.voice_config['model_id'],
                    transcript=clean_text,
                    voice={"mode": "id", "id": self.voice_config['voice_id']},
                    output_format={
                        "container": "raw",
                        "encoding": "pcm_f32le",
                        "sample_rate": self.voice_config['sample_rate']
                    }
                )
                
                # Process audio chunks
                chunk_count = 0
                for output in response:
                    if hasattr(output, 'data') and output.data:
                        audio_data = base64.b64decode(output.data)
                        audio_array = np.frombuffer(audio_data, dtype=np.float32).copy()
                        audio_array *= self.voice_config['volume_multiplier']
                        audio_array = np.clip(audio_array, -1.0, 1.0)
                        self.audio_queue.put(audio_array.tobytes())
                        chunk_count += 1
                
                print(f"🔊 Processed {chunk_count} audio chunks")
                return True
                
        except Exception as e:
            print(f"❌ Voice synthesis error: {e}")
            return False
    
    def _audio_playback_loop(self):
        """Audio playback loop"""
        while self.is_running:
            try:
                audio_data = self.audio_queue.get(timeout=1.0)
                if self.audio_stream and audio_data:
                    self.audio_stream.write(audio_data)
            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ Audio playback error: {e}")
                time.sleep(0.1)
    
    def _clean_text(self, text: str) -> str:
        """Clean text for TTS"""
        clean = text.replace('📹', '').replace('🔍', '').replace('Camera shows:', '').strip()
        clean = ' '.join(clean.split())
        if clean and not clean.endswith(('.', '!', '?')):
            clean += '.'
        return clean
    
    def get_status(self) -> Dict:
        """Get system status"""
        try:
            total_messages = 0
            if self.unified_messages_file.exists():
                with open(self.unified_messages_file, 'r', encoding='utf-8') as f:
                    messages = json.load(f)
                    total_messages = len(messages)
        except:
            total_messages = 0
        
        return {
            'running': self.is_running,
            'cartesia_available': self.cartesia_client is not None,
            'audio_available': self.audio_stream is not None,
            'total_messages': total_messages,
            'processed_messages': len(self.processed_messages),
            'pending_messages': max(0, total_messages - len(self.processed_messages))
        }


# Global instance
unified_voice = None


def initialize_simple_voice_system(ui_directory: str):
    """Initialize the simple unified voice system"""
    global unified_voice
    try:
        unified_voice = SimpleUnifiedVoice(ui_directory)
        unified_voice.start_voice_monitoring()
        return True
    except Exception as e:
        print(f"❌ Failed to initialize simple voice system: {e}")
        return False


def add_chat_message(user_message: str, ai_response: str, session_id: str) -> str:
    """Add a chat message"""
    global unified_voice
    if unified_voice:
        return unified_voice.add_message('chat', user_message, ai_response, session_id)
    return None


def add_camera_message(ai_response: str, session_id: str) -> str:
    """Add a camera message"""
    global unified_voice
    if unified_voice:
        return unified_voice.add_message('camera', 'what do you see?', ai_response, session_id)
    return None


def stop_voice_system():
    """Stop the voice system"""
    global unified_voice
    if unified_voice:
        unified_voice.stop_voice_monitoring()


def get_voice_status() -> Dict:
    """Get voice system status"""
    global unified_voice
    if unified_voice:
        return unified_voice.get_status()
    return {'running': False}
