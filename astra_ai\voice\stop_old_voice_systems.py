#!/usr/bin/env python3
"""
Stop Old Voice Systems Script
Stops any running old voice systems that monitor ai_responses.json
"""

import psutil
import os
import sys
from pathlib import Path


def stop_old_voice_processes():
    """Stop any old voice system processes"""
    print("🔍 Searching for old voice system processes...")
    
    # Process names to stop
    old_voice_processes = [
        "text_to_speech.py",
        "Ai vioce.py", 
        "ai_voice.py"
    ]
    
    stopped_count = 0
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            # Check if this is a python process
            if proc.info['cmdline'] and len(proc.info['cmdline']) > 1:
                cmdline = ' '.join(proc.info['cmdline'])
                
                # Check if it's running one of the old voice scripts
                for voice_script in old_voice_processes:
                    if voice_script in cmdline:
                        print(f"🛑 Stopping old voice process: {voice_script} (PID: {proc.info['pid']})")
                        try:
                            proc.terminate()
                            proc.wait(timeout=5)
                            stopped_count += 1
                            print(f"✅ Stopped {voice_script}")
                        except psutil.TimeoutExpired:
                            print(f"⚠️ Force killing {voice_script}")
                            proc.kill()
                            stopped_count += 1
                        except Exception as e:
                            print(f"❌ Failed to stop {voice_script}: {e}")
                        break
                        
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    if stopped_count > 0:
        print(f"✅ Stopped {stopped_count} old voice system process(es)")
    else:
        print("✅ No old voice system processes found")


def remove_old_voice_files():
    """Remove old voice system files"""
    print("\n🗑️ Removing old voice system files...")
    
    ui_dir = Path(__file__).parent.parent / 'ui'
    old_files = [
        ui_dir / 'ai_responses.json',
        ui_dir / 'processed_messages.json', 
        ui_dir / 'processed_responses.json',
        ui_dir / 'camera.json'
    ]
    
    removed_count = 0
    for file_path in old_files:
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"✅ Removed: {file_path.name}")
                removed_count += 1
            except Exception as e:
                print(f"❌ Failed to remove {file_path.name}: {e}")
    
    if removed_count > 0:
        print(f"✅ Removed {removed_count} old voice system file(s)")
    else:
        print("✅ No old voice system files found")


def check_current_system():
    """Check current voice system status"""
    print("\n📊 Checking current voice system status...")
    
    ui_dir = Path(__file__).parent.parent / 'ui'
    unified_messages = ui_dir / 'unified_messages.json'
    processed_file = ui_dir / 'processed.json'
    
    if unified_messages.exists():
        print("✅ unified_messages.json exists")
        try:
            import json
            with open(unified_messages, 'r', encoding='utf-8') as f:
                messages = json.load(f)
            print(f"   📄 Contains {len(messages)} messages")
        except Exception as e:
            print(f"   ⚠️ Error reading file: {e}")
    else:
        print("⚠️ unified_messages.json not found")
    
    if processed_file.exists():
        print("✅ processed.json exists")
        try:
            import json
            with open(processed_file, 'r', encoding='utf-8') as f:
                processed_data = json.load(f)
            processed_count = len(processed_data.get('processed_message_ids', []))
            print(f"   📋 {processed_count} messages processed")
        except Exception as e:
            print(f"   ⚠️ Error reading file: {e}")
    else:
        print("⚠️ processed.json not found")


def main():
    """Main function"""
    print("🚀 Old Voice System Cleanup")
    print("=" * 50)
    
    # Stop old processes
    stop_old_voice_processes()
    
    # Remove old files
    remove_old_voice_files()
    
    # Check current system
    check_current_system()
    
    print("\n" + "=" * 50)
    print("✅ Cleanup completed!")
    print("🎤 Only Simple Unified Voice System should be active now")
    print("📄 Voice system will only read from unified_messages.json")
    print("🚫 No more ai_responses.json monitoring")


if __name__ == "__main__":
    main()
