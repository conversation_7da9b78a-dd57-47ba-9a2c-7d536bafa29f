#!/usr/bin/env python3
"""
Test Duplicate Prevention in Simple Unified Voice System
"""

import json
import time
import tempfile
from pathlib import Path
from simple_unified_voice import SimpleUnifiedVoice


def test_duplicate_prevention():
    """Test that duplicate messages are properly prevented"""
    print("🧪 Testing Duplicate Prevention...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        voice_system = SimpleUnifiedVoice(temp_dir)
        
        # Test 1: Add same message multiple times
        print("\n📝 Test 1: Adding same message multiple times")
        same_response = "This is a test response that should only appear once"
        
        id1 = voice_system.add_message('chat', 'Hello', same_response, 'session1')
        id2 = voice_system.add_message('chat', 'Hello', same_response, 'session1')
        id3 = voice_system.add_message('chat', 'Hello', same_response, 'session1')
        
        print(f"   First add: {id1}")
        print(f"   Second add: {id2}")
        print(f"   Third add: {id3}")
        
        # Check unified_messages.json
        unified_file = Path(temp_dir) / 'unified_messages.json'
        with open(unified_file, 'r', encoding='utf-8') as f:
            messages = json.load(f)
        
        # Should only have 1 message
        unique_responses = set(msg['ai_response'] for msg in messages)
        print(f"   Messages in file: {len(messages)}")
        print(f"   Unique responses: {len(unique_responses)}")
        
        assert len(messages) == 1, f"Expected 1 message, got {len(messages)}"
        assert len(unique_responses) == 1, f"Expected 1 unique response, got {len(unique_responses)}"
        print("   ✅ Duplicate prevention working correctly")
        
        # Test 2: Add different messages
        print("\n📝 Test 2: Adding different messages")
        id4 = voice_system.add_message('chat', 'How are you?', 'I am doing well', 'session2')
        id5 = voice_system.add_message('camera', 'what do you see?', 'I see a computer screen', 'session3')
        
        print(f"   Different message 1: {id4}")
        print(f"   Different message 2: {id5}")
        
        # Check file again
        with open(unified_file, 'r', encoding='utf-8') as f:
            messages = json.load(f)
        
        print(f"   Total messages now: {len(messages)}")
        unique_responses = set(msg['ai_response'] for msg in messages)
        print(f"   Unique responses: {len(unique_responses)}")
        
        assert len(messages) == 3, f"Expected 3 messages, got {len(messages)}"
        assert len(unique_responses) == 3, f"Expected 3 unique responses, got {len(unique_responses)}"
        print("   ✅ Different messages added correctly")
        
        # Test 3: Add same response with different message types
        print("\n📝 Test 3: Same response, different message types")
        same_response_2 = "This response appears in both chat and camera"
        
        id6 = voice_system.add_message('chat', 'Test', same_response_2, 'session4')
        id7 = voice_system.add_message('camera', 'what do you see?', same_response_2, 'session5')
        
        print(f"   Chat message: {id6}")
        print(f"   Camera message: {id7}")
        
        # Check file
        with open(unified_file, 'r', encoding='utf-8') as f:
            messages = json.load(f)
        
        # Should still only have one instance of this response
        same_response_count = sum(1 for msg in messages if msg['ai_response'] == same_response_2)
        print(f"   Messages with same response: {same_response_count}")
        
        assert same_response_count == 1, f"Expected 1 message with same response, got {same_response_count}"
        print("   ✅ Cross-type duplicate prevention working")
        
        print("\n🎉 All duplicate prevention tests passed!")
        return True


def test_time_based_duplicates():
    """Test time-based duplicate prevention"""
    print("\n🧪 Testing Time-Based Duplicate Prevention...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        voice_system = SimpleUnifiedVoice(temp_dir)
        
        # Add a message
        response = "Time-based test response"
        id1 = voice_system.add_message('chat', 'Test', response, 'session1')
        print(f"   First message: {id1}")
        
        # Try to add same message immediately (should be blocked)
        id2 = voice_system.add_message('chat', 'Test', response, 'session1')
        print(f"   Immediate duplicate: {id2}")
        
        # Check that it's the same ID (duplicate was detected)
        assert id1 == id2, "Immediate duplicate should return same ID"
        print("   ✅ Immediate duplicate correctly detected")
        
        # Wait a bit and try again (should still be blocked within 30 seconds)
        time.sleep(1)
        id3 = voice_system.add_message('chat', 'Test', response, 'session1')
        print(f"   After 1 second: {id3}")
        
        assert id1 == id3, "Duplicate within time window should return same ID"
        print("   ✅ Time-window duplicate correctly detected")
        
        # Check file has only one message
        unified_file = Path(temp_dir) / 'unified_messages.json'
        with open(unified_file, 'r', encoding='utf-8') as f:
            messages = json.load(f)
        
        assert len(messages) == 1, f"Expected 1 message, got {len(messages)}"
        print("   ✅ File contains only one message")
        
        print("\n🎉 Time-based duplicate prevention tests passed!")
        return True


def test_cleanup_and_format():
    """Test that messages have correct format"""
    print("\n🧪 Testing Message Format...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        voice_system = SimpleUnifiedVoice(temp_dir)
        
        # Add messages
        chat_id = voice_system.add_message('chat', 'Hello', 'Hi there!', 'session1')
        camera_id = voice_system.add_message('camera', 'what do you see?', 'I see a screen', 'session2')
        
        # Check format
        unified_file = Path(temp_dir) / 'unified_messages.json'
        with open(unified_file, 'r', encoding='utf-8') as f:
            messages = json.load(f)
        
        print(f"   Total messages: {len(messages)}")
        
        for i, msg in enumerate(messages):
            print(f"   Message {i+1}:")
            print(f"     ID: {msg['message_id']}")
            print(f"     Type: {msg['message_type']}")
            print(f"     User: {msg['user_message']}")
            print(f"     AI: {msg['ai_response'][:30]}...")
            print(f"     Session: {msg['session_id']}")
            print(f"     Voice Required: {msg['voice_required']}")
            
            # Verify required fields
            required_fields = ['message_id', 'message_type', 'timestamp', 'user_message', 'ai_response', 'session_id', 'voice_required']
            for field in required_fields:
                assert field in msg, f"Missing field: {field}"
            
            # Verify types
            assert msg['message_type'] in ['chat', 'camera'], f"Invalid message type: {msg['message_type']}"
            assert isinstance(msg['voice_required'], bool), "voice_required should be boolean"
            assert isinstance(msg['timestamp'], (int, float)), "timestamp should be numeric"
        
        print("   ✅ All messages have correct format")
        
        print("\n🎉 Message format tests passed!")
        return True


def run_all_tests():
    """Run all duplicate prevention tests"""
    print("🚀 Starting Duplicate Prevention Tests")
    print("=" * 50)
    
    try:
        test_duplicate_prevention()
        test_time_based_duplicates()
        test_cleanup_and_format()
        
        print("=" * 50)
        print("✅ All duplicate prevention tests passed!")
        print("🎉 Simple Unified Voice System duplicate prevention is working correctly!")
        
        return True
        
    except Exception as e:
        print("=" * 50)
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
