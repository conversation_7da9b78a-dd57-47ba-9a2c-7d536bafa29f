#!/usr/bin/env python3
"""
Test Simple Unified Voice System
"""

import json
import time
import tempfile
from pathlib import Path
from simple_unified_voice import SimpleUnifiedVoice


def test_simple_voice_system():
    """Test the simple unified voice system"""
    print("🧪 Testing Simple Unified Voice System...")
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Initialize system
        voice_system = SimpleUnifiedVoice(temp_dir)
        
        # Test adding messages
        chat_id = voice_system.add_message('chat', 'Hello', 'Hi there!', 'session1')
        camera_id = voice_system.add_message('camera', 'what do you see?', 'I see a computer', 'session2')
        
        print(f"✅ Added chat message: {chat_id}")
        print(f"✅ Added camera message: {camera_id}")
        
        # Check unified_messages.json
        unified_file = Path(temp_dir) / 'unified_messages.json'
        if unified_file.exists():
            with open(unified_file, 'r', encoding='utf-8') as f:
                messages = json.load(f)
            print(f"✅ Unified messages file contains {len(messages)} messages")
            
            for msg in messages:
                print(f"   📄 {msg['message_type']}: {msg['ai_response'][:30]}...")
        
        # Check processed.json (should be empty initially)
        processed_file = Path(temp_dir) / 'processed.json'
        if processed_file.exists():
            with open(processed_file, 'r', encoding='utf-8') as f:
                processed_data = json.load(f)
            print(f"✅ Processed file contains {len(processed_data.get('processed_message_ids', []))} processed messages")
        else:
            print("✅ Processed file doesn't exist yet (expected)")
        
        # Get status
        status = voice_system.get_status()
        print(f"✅ System status: {status}")
        
        # Test voice monitoring (without actually starting audio)
        print("✅ Voice monitoring test would start here (audio disabled for test)")
        
        print("✅ Simple Unified Voice System test completed successfully!")
        return True


def test_json_structure():
    """Test the JSON file structure"""
    print("🧪 Testing JSON Structure...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        voice_system = SimpleUnifiedVoice(temp_dir)
        
        # Add test messages
        voice_system.add_message('chat', 'Test user message', 'Test AI response', 'test_session')
        voice_system.add_message('camera', 'what do you see?', 'I see a test environment', 'camera_session')
        
        # Check unified_messages.json structure
        unified_file = Path(temp_dir) / 'unified_messages.json'
        with open(unified_file, 'r', encoding='utf-8') as f:
            messages = json.load(f)
        
        # Verify structure
        for msg in messages:
            required_fields = ['message_id', 'message_type', 'timestamp', 'user_message', 'ai_response', 'session_id', 'voice_required']
            for field in required_fields:
                assert field in msg, f"Missing field: {field}"
            
            assert msg['message_type'] in ['chat', 'camera'], f"Invalid message type: {msg['message_type']}"
            assert isinstance(msg['timestamp'], (int, float)), "Timestamp should be numeric"
            assert isinstance(msg['voice_required'], bool), "voice_required should be boolean"
        
        print("✅ JSON structure validation passed")
        
        # Simulate processing a message
        voice_system.processed_messages.add(messages[0]['message_id'])
        voice_system._save_processed_messages()
        
        # Check processed.json structure
        processed_file = Path(temp_dir) / 'processed.json'
        with open(processed_file, 'r', encoding='utf-8') as f:
            processed_data = json.load(f)
        
        required_processed_fields = ['processed_message_ids', 'last_updated', 'total_processed']
        for field in required_processed_fields:
            assert field in processed_data, f"Missing processed field: {field}"
        
        assert len(processed_data['processed_message_ids']) == 1, "Should have 1 processed message"
        assert processed_data['total_processed'] == 1, "Total processed should be 1"
        
        print("✅ Processed JSON structure validation passed")
        return True


def test_duplicate_prevention():
    """Test duplicate message prevention"""
    print("🧪 Testing Duplicate Prevention...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        voice_system = SimpleUnifiedVoice(temp_dir)
        
        # Add same message multiple times
        id1 = voice_system.add_message('chat', 'Hello', 'Hi there!', 'session1')
        id2 = voice_system.add_message('chat', 'Hello', 'Hi there!', 'session1')
        id3 = voice_system.add_message('chat', 'Hello', 'Hi there!', 'session1')
        
        # All should get unique IDs
        assert id1 != id2 != id3, "Message IDs should be unique"
        
        # Check file contains all messages
        unified_file = Path(temp_dir) / 'unified_messages.json'
        with open(unified_file, 'r', encoding='utf-8') as f:
            messages = json.load(f)
        
        assert len(messages) == 3, "Should have 3 messages (duplicates allowed at storage level)"
        
        # Test processed tracking prevents duplicate voice synthesis
        voice_system.processed_messages.add(id1)
        voice_system.processed_messages.add(id2)
        
        # Simulate monitoring - only id3 should be "new"
        unprocessed = []
        for msg in messages:
            if msg['message_id'] not in voice_system.processed_messages:
                unprocessed.append(msg['message_id'])
        
        assert len(unprocessed) == 1, "Should have 1 unprocessed message"
        assert unprocessed[0] == id3, "Unprocessed message should be id3"
        
        print("✅ Duplicate prevention test passed")
        return True


def run_all_tests():
    """Run all tests"""
    print("🚀 Starting Simple Unified Voice System Tests")
    print("=" * 50)
    
    try:
        test_simple_voice_system()
        test_json_structure()
        test_duplicate_prevention()
        
        print("=" * 50)
        print("✅ All tests passed successfully!")
        print("🎉 Simple Unified Voice System is ready!")
        
        return True
        
    except Exception as e:
        print("=" * 50)
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
