#!/usr/bin/env python3
"""
Verify Single JSON System
Confirms that ai_responses.json is completely removed and only unified_messages.json is used
"""

import os
import json
import time
import requests
from pathlib import Path


def check_file_system():
    """Check that only unified_messages.json exists"""
    print("📁 CHECKING FILE SYSTEM")
    print("=" * 50)
    
    ui_dir = Path(__file__).parent.parent / 'ui'
    
    # Files that should NOT exist
    forbidden_files = [
        ui_dir / 'ai_responses.json',
        ui_dir / 'processed_messages.json',
        ui_dir / 'processed_responses.json',
        ui_dir / 'camera.json'
    ]
    
    # Files that SHOULD exist
    required_files = [
        ui_dir / 'unified_messages.json',
        ui_dir / 'processed.json'
    ]
    
    print("🚫 Checking forbidden files (should NOT exist):")
    forbidden_found = False
    for file_path in forbidden_files:
        if file_path.exists():
            print(f"   ❌ FOUND: {file_path.name} (should be deleted!)")
            forbidden_found = True
        else:
            print(f"   ✅ ABSENT: {file_path.name}")
    
    print("\n✅ Checking required files (should exist):")
    required_missing = False
    for file_path in required_files:
        if file_path.exists():
            print(f"   ✅ EXISTS: {file_path.name}")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                if file_path.name == 'unified_messages.json':
                    print(f"      📄 Contains {len(data)} messages")
                elif file_path.name == 'processed.json':
                    processed_count = len(data.get('processed_message_ids', []))
                    print(f"      📋 {processed_count} messages processed")
            except Exception as e:
                print(f"      ⚠️ Error reading: {e}")
        else:
            print(f"   ❌ MISSING: {file_path.name}")
            required_missing = True
    
    if forbidden_found:
        print("\n❌ FAILED: Forbidden files still exist!")
        return False
    elif required_missing:
        print("\n❌ FAILED: Required files missing!")
        return False
    else:
        print("\n✅ PASSED: File system is clean!")
        return True


def check_code_references():
    """Check that code no longer references ai_responses.json"""
    print("\n🔍 CHECKING CODE REFERENCES")
    print("=" * 50)
    
    # Key files to check
    key_files = [
        Path(__file__).parent.parent / 'core' / 'nova_ai.py',
        Path(__file__).parent.parent / 'scripts' / 'run_desktop_nova.py',
        Path(__file__).parent.parent / 'speech' / 'text_to_speech.py',
        Path(__file__).parent.parent / 'speech' / 'Ai vioce.py'
    ]
    
    issues_found = False
    
    for file_path in key_files:
        if file_path.exists():
            print(f"📄 Checking {file_path.name}...")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for problematic references
                if 'ai_responses.json' in content and 'removed' not in content.lower():
                    print(f"   ⚠️ Found ai_responses.json reference")
                    issues_found = True
                
                if 'save_ai_response(' in content:
                    print(f"   ⚠️ Found save_ai_response function call")
                    issues_found = True
                
                if not issues_found:
                    print(f"   ✅ Clean - no problematic references")
                    
            except Exception as e:
                print(f"   ❌ Error reading file: {e}")
        else:
            print(f"📄 {file_path.name} not found")
    
    if issues_found:
        print("\n❌ FAILED: Code still references ai_responses.json!")
        return False
    else:
        print("\n✅ PASSED: Code is clean!")
        return True


def test_server_behavior():
    """Test that server only uses unified_messages.json"""
    print("\n🌐 TESTING SERVER BEHAVIOR")
    print("=" * 50)
    
    try:
        # Check server status
        response = requests.get('http://127.0.0.1:64880/api/status', timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Server is running")
            
            voice_available = data.get('voice', {}).get('available', False)
            print(f"🎤 Voice system: {voice_available}")
            
            if voice_available:
                print("   📄 Should be using Simple Unified Voice System")
                print("   📋 Should monitor unified_messages.json only")
                print("   🚫 Should NOT monitor ai_responses.json")
            
            return True
            
        else:
            print(f"⚠️ Server not responding: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"⚠️ Server not accessible: {e}")
        print("   (This is OK if server is not running)")
        return True


def test_voice_system_integration():
    """Test that voice system only uses unified_messages.json"""
    print("\n🎤 TESTING VOICE SYSTEM INTEGRATION")
    print("=" * 50)
    
    ui_dir = Path(__file__).parent.parent / 'ui'
    unified_file = ui_dir / 'unified_messages.json'
    processed_file = ui_dir / 'processed.json'
    
    if not unified_file.exists():
        print("⚠️ unified_messages.json not found - creating test file")
        test_message = {
            "message_id": f"test_{int(time.time())}",
            "message_type": "chat",
            "timestamp": time.time(),
            "user_message": "test message",
            "ai_response": "This is a test response for verification",
            "session_id": "verification_test",
            "voice_required": True
        }
        
        with open(unified_file, 'w', encoding='utf-8') as f:
            json.dump([test_message], f, indent=2, ensure_ascii=False)
        
        print("✅ Created test unified_messages.json")
    
    # Check that processed.json exists
    if processed_file.exists():
        print("✅ processed.json exists")
        try:
            with open(processed_file, 'r', encoding='utf-8') as f:
                processed_data = json.load(f)
            processed_count = len(processed_data.get('processed_message_ids', []))
            print(f"   📋 {processed_count} messages processed")
        except Exception as e:
            print(f"   ⚠️ Error reading processed.json: {e}")
    else:
        print("⚠️ processed.json not found")
    
    print("✅ Voice system integration check complete")
    return True


def run_verification():
    """Run complete verification"""
    print("🚀 VERIFYING SINGLE JSON SYSTEM")
    print("=" * 60)
    print("🎯 Goal: Confirm ai_responses.json is completely removed")
    print("📄 Expected: Only unified_messages.json used for voice synthesis")
    print("=" * 60)
    
    results = []
    
    # Run all checks
    results.append(("File System", check_file_system()))
    results.append(("Code References", check_code_references()))
    results.append(("Server Behavior", test_server_behavior()))
    results.append(("Voice Integration", test_voice_system_integration()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VERIFICATION RESULTS")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{status} - {test_name}")
        if not passed:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 SUCCESS: Single JSON system verified!")
        print("📄 Only unified_messages.json is used")
        print("🚫 ai_responses.json completely removed")
        print("🎤 Voice system clean and working")
    else:
        print("❌ FAILURE: Issues found!")
        print("🔧 Manual cleanup may be required")
    
    return all_passed


if __name__ == "__main__":
    success = run_verification()
    exit(0 if success else 1)
