ai:
  caching:
    enabled: true
    max_size: 1000
    ttl: 3600
  load_balancing:
    enabled: true
    health_check_interval: 60
    strategy: round_robin
  providers:
    anthropic:
      api_key: ''
      enabled: false
      max_tokens: 4000
      model: claude-3-sonnet-20240229
    google:
      api_key: ''
      enabled: true
      max_tokens: 4000
      model: gemini-pro
    groq:
      api_key: ''
      enabled: true
      max_tokens: 4000
      model: mixtral-8x7b-32768
    openai:
      api_key: ''
      enabled: false
      max_tokens: 4000
      model: gpt-4
cache:
  max_size: 1000
  ttl: 3600
  type: memory
database:
  max_overflow: 20
  path: data/nova_ai.db
  pool_size: 10
  type: sqlite
features:
  analytics: true
  background_tasks: true
  file_upload: true
  health_checks: true
  monitoring: true
  websockets: true
logging:
  backup_count: 5
  console: true
  file: logs/nova_ai.log
  format: '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
  level: INFO
  max_size: 10MB
security:
  bcrypt_rounds: 12
  cors_origins:
  - http://localhost:*
  - http://127.0.0.1:*
  jwt_expiry: 3600
  rate_limit_enabled: true
  secret_key: ''
server:
  api_port: 8081
  debug: false
  host: 127.0.0.1
  max_connections: 1000
  timeout: 30
  ui_port: 8080
  workers: 4
widgets:
  camera:
    default_resolution:
    - 640
    - 480
    enabled: true
  news:
    cache_duration: 1800
    enabled: true
    max_articles: 20
  notepad:
    auto_save: true
    enabled: true
    max_notes: 1000
  object_identification:
    confidence_threshold: 0.7
    enabled: true
  search:
    cache_results: true
    enabled: true
    max_results: 50
