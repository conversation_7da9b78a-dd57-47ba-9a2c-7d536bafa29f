#!/usr/bin/env python3
"""
Quick Bark Dependencies Installer
"""
import subprocess
import sys

def install_bark_deps():
    print("🎵 Installing Bark TTS Dependencies")
    print("=" * 40)
    
    # Core packages needed for Bark
    packages = [
        "torch",
        "torchaudio", 
        "transformers",
        "scipy",
        "numpy",
        "encodec",
        "funcy"
    ]
    
    # Install core packages first
    for package in packages:
        print(f"📦 Installing {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package, "--quiet"])
            print(f"✅ {package} installed")
        except Exception as e:
            print(f"❌ Failed to install {package}: {e}")
    
    # Install Bark from GitHub
    print("\n📦 Installing Bark TTS from GitHub...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "git+https://github.com/suno-ai/bark.git", 
            "--quiet"
        ])
        print("✅ Bark TTS installed successfully!")
    except Exception as e:
        print(f"❌ Failed to install Bark: {e}")
        return False
    
    # Try to install PyAudio for audio playback
    print("\n📦 Installing PyAudio for audio playback...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyaudio", "--quiet"])
        print("✅ PyAudio installed")
    except Exception as e:
        print(f"⚠️ PyAudio failed (audio playback will be disabled): {e}")
    
    print("\n🎉 Installation complete!")
    print("🚀 You can now run: python bark_voice_system.py")
    return True

if __name__ == "__main__":
    install_bark_deps()