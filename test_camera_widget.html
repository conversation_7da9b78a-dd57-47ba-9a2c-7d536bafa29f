<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Widget Test</title>
    <style>
        body {
            background: #000;
            color: white;
            font-family: 'Orbitron', monospace;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #00FFFF;
            border-radius: 10px;
        }
        .test-button {
            background: rgba(0, 255, 255, 0.2);
            border: 1px solid rgba(0, 255, 255, 0.4);
            color: rgba(0, 255, 255, 0.9);
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
        }
        .test-button:hover {
            background: rgba(0, 255, 255, 0.3);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            background: rgba(0, 255, 255, 0.1);
            border-radius: 5px;
            min-height: 50px;
        }
        .success { color: #00FF00; }
        .error { color: #FF0000; }
        .warning { color: #FFAA00; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Camera Widget AI Vision Test</h1>
        
        <div class="test-section">
            <h2>🔧 Direct Gemini API Test</h2>
            <p>This test verifies that the Gemini AI API is working correctly.</p>
            <button class="test-button" onclick="testDirectGeminiAPI()">Test Direct Gemini API</button>
            <div id="geminiResult" class="test-result">Click the button to test...</div>
        </div>

        <div class="test-section">
            <h2>📹 Camera Widget Simulation</h2>
            <p>This simulates the camera widget AI connection test.</p>
            <button class="test-button" onclick="testCameraWidget()">Test Camera Widget AI</button>
            <div id="cameraResult" class="test-result">Click the button to test...</div>
        </div>

        <div class="test-section">
            <h2>🔍 Vision Analysis Simulation</h2>
            <p>This simulates a vision analysis request with a test image using the new concise response format.</p>
            <button class="test-button" onclick="testVisionAnalysis()">Test Concise Vision Analysis</button>
            <div id="visionResult" class="test-result">Click the button to test...</div>
        </div>

        <div class="test-section">
            <h2>📊 Response Format Comparison</h2>
            <p>Compare old verbose vs new concise response formats:</p>
            <button class="test-button" onclick="showResponseComparison()">Show Format Examples</button>
            <div id="comparisonResult" class="test-result">Click the button to see examples...</div>
        </div>
    </div>

    <script>
        // Gemini API configuration
        const geminiApiKey = 'AIzaSyBe7dZj5mN_40Yo9t2xIeNgEcKdJWSe42w';
        const geminiModel = 'gemini-1.5-flash';

        async function testDirectGeminiAPI() {
            const resultDiv = document.getElementById('geminiResult');
            resultDiv.innerHTML = '🔄 Testing direct Gemini API connection...';
            
            try {
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${geminiApiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: "Hello, this is a connection test from the camera widget."
                            }]
                        }],
                        generationConfig: {
                            maxOutputTokens: 50,
                            temperature: 0.1
                        }
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const analysis = data.candidates[0].content.parts[0].text;
                    resultDiv.innerHTML = `<span class="success">✅ Success!</span><br>Response: ${analysis}`;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ Error:</span> ${error.message}`;
                console.error('Direct API test failed:', error);
            }
        }

        async function testCameraWidget() {
            const resultDiv = document.getElementById('cameraResult');
            resultDiv.innerHTML = '🔄 Testing camera widget AI connection...';
            
            // Simulate the camera widget's testGeminiConnection method
            try {
                const testResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${geminiApiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: "Camera widget connection test."
                            }]
                        }],
                        generationConfig: {
                            maxOutputTokens: 10,
                            temperature: 0.1
                        }
                    })
                });

                if (testResponse.ok) {
                    const responseData = await testResponse.json();
                    resultDiv.innerHTML = `<span class="success">✅ Camera Widget AI Ready!</span><br>Status: AI Ready - Ask me what I see!`;
                } else {
                    throw new Error(`HTTP ${testResponse.status}: ${testResponse.statusText}`);
                }
            } catch (error) {
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    resultDiv.innerHTML = `<span class="warning">⚠️ CORS Issue Detected</span><br>This is expected in some browsers. Fallback mode would be enabled.`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ Connection Failed:</span> ${error.message}`;
                }
                console.error('Camera widget test failed:', error);
            }
        }

        async function testVisionAnalysis() {
            const resultDiv = document.getElementById('visionResult');
            resultDiv.innerHTML = '🔄 Testing vision analysis...';
            
            // Create a simple test image (1x1 white pixel as base64)
            const canvas = document.createElement('canvas');
            canvas.width = 100;
            canvas.height = 100;
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, 100, 100);
            ctx.fillStyle = 'black';
            ctx.font = '20px Arial';
            ctx.fillText('TEST', 30, 50);
            
            const imageData = canvas.toDataURL('image/jpeg', 0.8);
            const base64Image = imageData.split(',')[1];
            
            try {
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${geminiModel}:generateContent?key=${geminiApiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [
                                {
                                    text: `Analyze this camera image and describe what is visible. Provide a brief, factual response with:

Response format:
- Scene/environment description
- Objects and people present
- Lighting conditions
- Any text, signs, or notable details (IGNORE timestamps, time displays, or camera UI elements)
- Current activity if apparent

IMPORTANT: Do NOT mention timestamps, time displays, camera UI elements, or any time-related information unless specifically asked about time. Focus only on actual objects, people, and scenes in the image.

Be concise and factual. Start with "📹 Camera shows:" and focus only on what is clearly visible. Avoid speculation, conversational language, or unnecessary commentary.`
                                },
                                {
                                    inline_data: {
                                        mime_type: "image/jpeg",
                                        data: base64Image
                                    }
                                }
                            ]
                        }],
                        generationConfig: {
                            temperature: 0.2,
                            maxOutputTokens: 200,
                            topP: 0.9,
                            topK: 20
                        }
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const analysis = data.candidates[0].content.parts[0].text;
                    resultDiv.innerHTML = `<span class="success">✅ Vision Analysis Success!</span><br>Analysis: ${analysis}`;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    resultDiv.innerHTML = `<span class="warning">⚠️ CORS Issue</span><br>Vision analysis blocked by browser security. Fallback would provide basic camera status.`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ Analysis Failed:</span> ${error.message}`;
                }
                console.error('Vision analysis test failed:', error);
            }
        }

        function showResponseComparison() {
            const resultDiv = document.getElementById('comparisonResult');

            const comparisonHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 10px;">
                    <div>
                        <h3 style="color: #FF6666;">❌ Old Verbose Format</h3>
                        <div style="background: rgba(255, 102, 102, 0.1); padding: 15px; border-radius: 8px; font-size: 12px; line-height: 1.4;">
                            "📹 Camera shows: A young, dark-skinned person wearing a blue Nike jersey, seated in a bedroom. The walls are peach-colored with a decorative border. A bed with pillows and blankets is visible in the background. Lighting appears to be indoor, somewhat dim. A framed picture hangs on the wall."
                            <br><br>
                            <span style="color: #FF6666;">❌ Problem: Too much detail, mentions everything visible!</span>
                        </div>
                    </div>
                    <div>
                        <h3 style="color: #00FF00;">✅ New Ultra-Concise Format</h3>
                        <div style="background: rgba(0, 255, 0, 0.1); padding: 15px; border-radius: 8px; font-size: 12px; line-height: 1.4;">
                            "📹 Camera shows: Person in blue Nike jersey, seated in bedroom."
                            <br><br>
                            <span style="color: #00FF00;">✅ Fixed: Only the most important elements, under 25 words!</span>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 20px; padding: 15px; background: rgba(0, 255, 255, 0.1); border-radius: 8px;">
                    <h4 style="color: #00FFFF; margin-bottom: 10px;">🎯 Key Improvements:</h4>
                    <ul style="font-size: 12px; line-height: 1.6; margin: 0; padding-left: 20px;">
                        <li><strong>Ultra-Concise:</strong> Under 25 words, only essential information</li>
                        <li><strong>Selective:</strong> Focuses on most important elements only</li>
                        <li><strong>Efficient:</strong> Eliminates background details and minor elements</li>
                        <li><strong>Essential-Only:</strong> Main subject and key characteristics</li>
                        <li><strong>UI-Filtered:</strong> Ignores camera timestamps and interface elements</li>
                        <li><strong>Priority-Based:</strong> Highlights what truly matters in the scene</li>
                        <li><strong>Streamlined:</strong> Maximum information in minimum words</li>
                    </ul>
                </div>
            `;

            resultDiv.innerHTML = comparisonHTML;
        }
    </script>
</body>
</html>
