#!/usr/bin/env python3
"""
Demo script showing Gini 1.5 Terminal Screen Analyzer in action
"""

import os
import sys
from pathlib import Path

# Set API key directly for demo
os.environ['GOOGLE_AI_API_KEY'] = 'AIzaSyBe7dZj5mN_40Yo9t2xIeNgEcKdJWSe42w'

def demo_screen_analysis():
    """Demonstrate screen analysis functionality"""
    print("🚀 GINI 1.5 - TERMINAL SCREEN ANALYZER DEMO")
    print("="*60)
    print("🧠 Powered by Google Gemini 1.5 Pro Vision")
    print("📸 AI-powered screen analysis in your terminal")
    print("="*60)
    print()
    
    try:
        from terminal_screen_analyzer import TerminalScreenAnalyzer
        
        print("✅ Initializing Gini 1.5 Terminal Analyzer...")
        analyzer = TerminalScreenAnalyzer()
        
        print("\n💡 DEMO COMMANDS YOU CAN TRY:")
        print("-" * 40)
        print("🖥️  Screen Analysis:")
        print("   • what do you see on my screen?")
        print("   • whats on my screen?")
        print("   • describe my screen")
        print()
        print("❓ Custom Questions:")
        print("   • What errors do you see on my screen?")
        print("   • What programming language is being used?")
        print("   • Summarize the document on my screen")
        print("   • What's the main content of this screen?")
        print()
        print("📁 Image Analysis:")
        print("   • analyze path/to/image.jpg")
        print("   • quick screenshot.png")
        print()
        print("🎮 Controls:")
        print("   • help - Show help")
        print("   • quit - Exit")
        print()
        print("="*60)
        print("🎯 Ready! Type your command below:")
        print("="*60)
        
        # Start interactive mode
        analyzer.interactive_mode()
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all packages are installed:")
        print("   pip install google-generativeai mss pillow python-dotenv")
    except Exception as e:
        print(f"❌ Error: {e}")

def quick_test():
    """Quick test of the system"""
    print("🧪 QUICK SYSTEM TEST")
    print("="*30)
    
    # Test imports
    try:
        import mss
        print("✅ mss (screen capture)")
    except ImportError:
        print("❌ mss - install with: pip install mss")
        return False
    
    try:
        import google.generativeai as genai
        print("✅ google-generativeai")
    except ImportError:
        print("❌ google-generativeai - install with: pip install google-generativeai")
        return False
    
    try:
        from PIL import Image
        print("✅ PIL (image processing)")
    except ImportError:
        print("❌ PIL - install with: pip install pillow")
        return False
    
    # Test API key
    api_key = os.getenv('GOOGLE_AI_API_KEY')
    if api_key and api_key != 'demo_key_replace_with_real_key':
        print("✅ API key configured")
    else:
        print("❌ API key not configured")
        return False
    
    print("\n🎉 All tests passed! System ready.")
    return True

def main():
    """Main demo function"""
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        if quick_test():
            print("\n🚀 Starting demo...")
            demo_screen_analysis()
    else:
        demo_screen_analysis()

if __name__ == "__main__":
    main()
