#!/usr/bin/env python3
"""
GINI 1.5 - Complete AI Screen Analyzer
All-in-one script with offline mode, quota management, and full AI analysis
"""

import os
import sys
import base64
import time
import json
from pathlib import Path
from io import BytesIO
from datetime import datetime

# Set API key directly
os.environ['GOOGLE_AI_API_KEY'] = 'AIzaSyBe7dZj5mN_40Yo9t2xIeNgEcKdJWSe42w'

# Try to import required packages
try:
    import mss
    MSS_AVAILABLE = True
except ImportError:
    MSS_AVAILABLE = False

try:
    import pygetwindow as gw
    import pyautogui
    WINDOW_CAPTURE_AVAILABLE = True
except ImportError:
    WINDOW_CAPTURE_AVAILABLE = False

try:
    import cv2
    import numpy as np
    CAMERA_AVAILABLE = True
except ImportError:
    CAMERA_AVAILABLE = False

try:
    import threading
    import queue
    THREADING_AVAILABLE = True
except ImportError:
    THREADING_AVAILABLE = False

try:
    import tkinter as tk
    from tkinter import ttk, messagebox
    from PIL import ImageTk
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    import google.generativeai as genai
    from google.generativeai.types import HarmCategory, HarmBlockThreshold
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False

class LiveCameraWindow:
    """Live camera preview window with real-time interaction"""

    def __init__(self, analyzer):
        """Initialize the live camera window"""
        self.analyzer = analyzer
        self.root = None
        self.video_label = None
        self.status_label = None
        self.command_entry = None
        self.response_text = None
        self.is_running = False
        self.current_frame = None
        self.frame_queue = queue.Queue(maxsize=2)

    def create_window(self):
        """Create the live camera preview window"""
        if not GUI_AVAILABLE:
            print("❌ GUI not available - install tkinter")
            return False

        try:
            import tkinter as tk
            from tkinter import ttk, messagebox

            self.root = tk.Tk()
            self.root.title("GINI 1.5 - Live Camera Analysis")
            self.root.geometry("1000x800")
            self.root.configure(bg='#0a0a0a')

            # Main frame
            main_frame = ttk.Frame(self.root)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Video frame
            video_frame = ttk.LabelFrame(main_frame, text="📷 Live Camera Feed", padding=10)
            video_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            self.video_label = ttk.Label(video_frame, text="Initializing camera...",
                                       background='black', foreground='white')
            self.video_label.pack(expand=True)

            # Status frame
            status_frame = ttk.LabelFrame(main_frame, text="📊 Status", padding=5)
            status_frame.pack(fill=tk.X, pady=(0, 10))

            self.status_label = ttk.Label(status_frame, text="🔴 Camera starting...",
                                        foreground='red')
            self.status_label.pack()

            # Control frame
            control_frame = ttk.LabelFrame(main_frame, text="🎮 Interactive Controls", padding=10)
            control_frame.pack(fill=tk.X, pady=(0, 10))

            # Command entry
            cmd_frame = ttk.Frame(control_frame)
            cmd_frame.pack(fill=tk.X, pady=(0, 5))

            ttk.Label(cmd_frame, text="Ask AI:").pack(side=tk.LEFT, padx=(0, 5))
            self.command_entry = ttk.Entry(cmd_frame, font=('Arial', 12))
            self.command_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
            self.command_entry.bind('<Return>', self.process_command)

            analyze_btn = ttk.Button(cmd_frame, text="🧠 Analyze Now",
                                   command=self.analyze_current_frame)
            analyze_btn.pack(side=tk.RIGHT)

            # Quick action buttons
            btn_frame = ttk.Frame(control_frame)
            btn_frame.pack(fill=tk.X, pady=(5, 0))

            ttk.Button(btn_frame, text="👋 What do you see?",
                      command=lambda: self.quick_command("what do you see right now?")).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(btn_frame, text="✋ What am I holding?",
                      command=lambda: self.quick_command("what am I holding in my hand?")).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(btn_frame, text="📸 Screenshot",
                      command=self.take_screenshot).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(btn_frame, text="❌ Close",
                      command=self.close_window).pack(side=tk.RIGHT)

            # Response frame
            response_frame = ttk.LabelFrame(main_frame, text="🤖 AI Response", padding=10)
            response_frame.pack(fill=tk.BOTH, expand=True)

            # Scrollable text widget
            text_frame = ttk.Frame(response_frame)
            text_frame.pack(fill=tk.BOTH, expand=True)

            self.response_text = tk.Text(text_frame, height=8, wrap=tk.WORD,
                                       bg='#1a1a1a', fg='#00ff88',
                                       font=('Consolas', 10))
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.response_text.yview)
            self.response_text.configure(yscrollcommand=scrollbar.set)

            self.response_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # Initial message
            self.add_response("🚀 GINI 1.5 Live Camera Analysis Ready!\n" +
                            "💡 Commands: Ask questions, hold objects up to camera, or use quick buttons\n" +
                            "🎯 Examples: 'what do you see?', 'what am I holding?', 'describe this object'\n")

            # Bind window close event
            self.root.protocol("WM_DELETE_WINDOW", self.close_window)

            return True

        except Exception as e:
            print(f"❌ Failed to create camera window: {e}")
            return False

    def start_camera_feed(self):
        """Start the live camera feed"""
        if not self.analyzer.init_camera():
            self.add_response("❌ Failed to initialize camera")
            return False

        self.is_running = True
        self.status_label.config(text="🟢 Camera active - Live feed running", foreground='green')

        # Start video feed thread
        video_thread = threading.Thread(target=self.video_feed_loop, daemon=True)
        video_thread.start()

        # Start GUI update thread
        self.root.after(30, self.update_video_display)

        return True

    def video_feed_loop(self):
        """Continuous video feed capture loop"""
        import cv2

        while self.is_running and self.analyzer.camera_active:
            try:
                ret, frame = self.analyzer.camera.read()
                if ret:
                    # Resize frame for display
                    display_frame = cv2.resize(frame, (640, 480))

                    # Convert BGR to RGB
                    rgb_frame = cv2.cvtColor(display_frame, cv2.COLOR_BGR2RGB)

                    # Store current frame for analysis
                    self.current_frame = frame.copy()

                    # Add to queue for display (non-blocking)
                    try:
                        self.frame_queue.put_nowait(rgb_frame)
                    except queue.Full:
                        # Remove old frame and add new one
                        try:
                            self.frame_queue.get_nowait()
                            self.frame_queue.put_nowait(rgb_frame)
                        except queue.Empty:
                            pass

                time.sleep(0.033)  # ~30 FPS

            except Exception as e:
                print(f"⚠️  Video feed error: {e}")
                time.sleep(0.1)

    def update_video_display(self):
        """Update the video display in GUI"""
        if not self.is_running:
            return

        try:
            # Get latest frame from queue
            rgb_frame = self.frame_queue.get_nowait()

            # Convert to PIL Image and then to PhotoImage
            from PIL import ImageTk
            pil_image = Image.fromarray(rgb_frame)
            photo = ImageTk.PhotoImage(pil_image)

            # Update video label
            self.video_label.configure(image=photo, text="")
            self.video_label.image = photo  # Keep a reference

        except queue.Empty:
            pass  # No new frame available
        except Exception as e:
            print(f"⚠️  Display update error: {e}")

        # Schedule next update
        if self.is_running:
            self.root.after(30, self.update_video_display)

    def process_command(self, event=None):
        """Process user command from entry field"""
        command = self.command_entry.get().strip()
        if command:
            self.command_entry.delete(0, tk.END)
            self.analyze_with_question(command)

    def quick_command(self, command):
        """Execute a quick command"""
        self.analyze_with_question(command)

    def analyze_current_frame(self):
        """Analyze the current camera frame"""
        self.analyze_with_question("what do you see right now?")

    def take_screenshot(self):
        """Take a screenshot of current frame"""
        if self.current_frame is not None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = Path(f"gini_live_screenshot_{timestamp}.jpg")

            import cv2
            cv2.imwrite(str(screenshot_path), self.current_frame)

            self.add_response(f"📸 Screenshot saved: {screenshot_path}")
            self.analyze_with_question("analyze this screenshot I just took")
        else:
            self.add_response("❌ No frame available for screenshot")

    def analyze_with_question(self, question):
        """Analyze current frame with specific question"""
        if self.current_frame is None:
            self.add_response("❌ No camera frame available")
            return

        self.add_response(f"🎤 You: {question}")
        self.add_response("🧠 Analyzing current frame...")

        # Run analysis in background thread to avoid blocking GUI
        analysis_thread = threading.Thread(
            target=self.perform_analysis,
            args=(question,),
            daemon=True
        )
        analysis_thread.start()

    def perform_analysis(self, question):
        """Perform AI analysis in background thread"""
        try:
            # Convert current frame to base64
            import cv2

            # Convert BGR to RGB
            rgb_frame = cv2.cvtColor(self.current_frame, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(rgb_frame)

            # Convert to base64
            buffer = BytesIO()
            pil_image.save(buffer, format='JPEG')
            img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

            # Enhance question for live analysis
            enhanced_question = f"Looking at this live camera feed frame captured right now, {question}"

            # Analyze with AI
            if self.analyzer.online_mode:
                result = self.analyzer.analyze_online(img_base64, enhanced_question)
            else:
                result = self.analyzer.analyze_offline(enhanced_question)

            # Update GUI with result
            self.root.after(0, lambda: self.display_analysis_result(result))

        except Exception as e:
            error_msg = f"❌ Analysis error: {e}"
            self.root.after(0, lambda: self.add_response(error_msg))

    def display_analysis_result(self, result):
        """Display analysis result in GUI"""
        if result and result.get('success'):
            response = f"🤖 AI: {result['analysis']}\n"
            if result.get('mode') == 'offline':
                response += "💡 (Demo mode - for real AI analysis, check API quota)\n"
        else:
            response = f"❌ Analysis failed: {result.get('error', 'Unknown error') if result else 'No result'}\n"

        self.add_response(response)

    def add_response(self, text):
        """Add text to response area"""
        self.response_text.insert(tk.END, text + "\n")
        self.response_text.see(tk.END)
        self.response_text.update()

    def close_window(self):
        """Close the camera window"""
        self.is_running = False
        if self.root:
            self.root.destroy()
        self.analyzer.close_camera()

    def run(self):
        """Run the live camera window"""
        if not self.create_window():
            return False

        if not self.start_camera_feed():
            self.close_window()
            return False

        # Focus on command entry
        self.command_entry.focus_set()

        # Start GUI main loop
        self.root.mainloop()
        return True

class GiniScreenAnalyzer:
    """Complete screen analyzer with online and offline modes"""

    def __init__(self):
        """Initialize the analyzer"""
        self.online_mode = False
        self.analyzer = None
        self.camera = None
        self.camera_active = False
        self.tracking_active = False
        self.last_frame = None
        self.detected_objects = []
        self.live_window = None
        self.demo_responses = [
            """I can see a terminal/command prompt window with code and text. The main content shows:

1. **Main Content**: A command line interface with Python script execution
2. **UI Elements**: Dark terminal background, white/colored text, command prompt
3. **Text Content**: Python code output, file paths, system messages, and error logs
4. **Activity**: Software development, debugging, or system administration work
5. **Context**: Programming environment on Windows with active development
6. **Notable Details**: Multiple lines of output, file operations, script execution results

This appears to be a developer working with Python scripts and terminal commands.""",

            """I can see a code editor or development environment. The screen shows:

1. **Main Content**: Text editor or IDE with programming files open
2. **UI Elements**: Menu bars, file explorer panel, editor panes, syntax highlighting
3. **Text Content**: Source code with proper formatting and color coding
4. **Activity**: Active software development and code editing
5. **Context**: Professional development workflow with organized project structure
6. **Notable Details**: Multiple files open, tabbed interface, development tools visible

This looks like an active programming session with code editing in progress.""",

            """I can see a desktop environment with various applications. The content includes:

1. **Main Content**: Desktop with multiple windows and applications running
2. **UI Elements**: Taskbar, window frames, application interfaces, system tray
3. **Text Content**: Various text content across different applications
4. **Activity**: Multitasking with several programs and windows open
5. **Context**: General computer usage and productivity work
6. **Notable Details**: Well-organized workspace, multiple active applications

This appears to be a typical work session with multiple applications running simultaneously."""
        ]
        self.response_index = 0
        self.setup_analyzer()
    
    def setup_analyzer(self):
        """Set up the Gemini Vision analyzer if available"""
        if not GEMINI_AVAILABLE:
            print("⚠️  Gemini not available - using offline mode")
            return False
        
        try:
            api_key = os.getenv('GOOGLE_AI_API_KEY')
            if not api_key:
                print("⚠️  No API key - using offline mode")
                return False
            
            genai.configure(api_key=api_key)
            
            # Try different models
            models_to_try = ['gemini-1.5-flash', 'gemini-pro-vision', 'gemini-1.5-pro']
            
            for model_name in models_to_try:
                try:
                    self.model = genai.GenerativeModel(model_name)
                    self.model_name = model_name
                    
                    # Test with minimal request
                    test_image = Image.new('RGB', (1, 1), color='white')
                    response = self.model.generate_content(
                        ["Test", test_image],
                        generation_config=genai.types.GenerationConfig(max_output_tokens=1)
                    )
                    
                    print(f"✅ Online mode: {model_name}")
                    self.online_mode = True
                    return True
                    
                except Exception as e:
                    if "429" in str(e) or "quota" in str(e).lower():
                        print(f"⚠️  {model_name}: Quota exceeded")
                        continue
                    else:
                        print(f"⚠️  {model_name}: {str(e)[:50]}...")
                        continue
            
            print("⚠️  All models quota exceeded - using offline mode")
            return False
            
        except Exception as e:
            print(f"⚠️  Setup failed: {e} - using offline mode")
            return False

    def init_camera(self):
        """Initialize camera for live feed analysis"""
        if not CAMERA_AVAILABLE:
            print("❌ Camera not available - install opencv: pip install opencv-python")
            return False

        try:
            import cv2

            # Try to open default camera (usually index 0)
            self.camera = cv2.VideoCapture(0)

            if not self.camera.isOpened():
                print("❌ Could not access camera - check permissions and connections")
                return False

            # Set camera properties for better quality
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
            self.camera.set(cv2.CAP_PROP_FPS, 30)

            print("✅ Camera initialized successfully")
            self.camera_active = True
            return True

        except Exception as e:
            print(f"❌ Camera initialization failed: {e}")
            return False

    def capture_camera_frame(self):
        """Capture a single frame from camera"""
        if not self.camera_active:
            if not self.init_camera():
                return None, None

        try:
            import cv2

            ret, frame = self.camera.read()
            if not ret:
                print("❌ Failed to capture camera frame")
                return None, None

            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # Convert to PIL Image
            pil_image = Image.fromarray(frame_rgb)

            # Save camera capture
            camera_path = Path("gini_camera_capture.jpg")
            pil_image.save(camera_path)
            print(f"📷 Camera frame saved: {camera_path}")

            # Convert to base64
            buffer = BytesIO()
            pil_image.save(buffer, format='JPEG')
            img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

            self.last_frame = frame  # Store for object tracking

            print(f"✅ Camera frame captured: {pil_image.size[0]}x{pil_image.size[1]} pixels")
            return img_base64, pil_image.size

        except Exception as e:
            print(f"❌ Camera capture failed: {e}")
            return None, None

    def detect_objects_in_frame(self, frame):
        """Detect objects in camera frame using OpenCV"""
        if not CAMERA_AVAILABLE:
            return []

        try:
            import cv2
            import numpy as np

            # Convert to grayscale for object detection
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # Use Haar cascades for face detection (built into OpenCV)
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

            # Detect faces
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)

            detected = []
            for (x, y, w, h) in faces:
                detected.append({
                    'type': 'face',
                    'confidence': 0.8,
                    'bbox': (x, y, w, h),
                    'center': (x + w//2, y + h//2)
                })

            # Simple motion detection (compare with previous frame if available)
            if hasattr(self, 'prev_gray') and self.prev_gray is not None:
                # Calculate frame difference
                diff = cv2.absdiff(self.prev_gray, gray)
                thresh = cv2.threshold(diff, 25, 255, cv2.THRESH_BINARY)[1]

                # Find contours for motion detection
                contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                for contour in contours:
                    if cv2.contourArea(contour) > 1000:  # Filter small movements
                        x, y, w, h = cv2.boundingRect(contour)
                        detected.append({
                            'type': 'motion',
                            'confidence': 0.6,
                            'bbox': (x, y, w, h),
                            'center': (x + w//2, y + h//2)
                        })

            self.prev_gray = gray.copy()
            return detected

        except Exception as e:
            print(f"⚠️  Object detection error: {e}")
            return []

    def start_object_tracking(self, duration=10):
        """Start real-time object tracking for specified duration"""
        if not self.camera_active:
            if not self.init_camera():
                return

        if not THREADING_AVAILABLE:
            print("⚠️  Threading not available - using single frame analysis")
            return self.analyze_camera_objects()

        try:
            import cv2
            import threading
            import time

            print(f"🎯 Starting object tracking for {duration} seconds...")
            print("👁️  Monitoring camera feed for objects and motion...")

            self.tracking_active = True
            self.detected_objects = []

            def tracking_loop():
                start_time = time.time()
                frame_count = 0

                while self.tracking_active and (time.time() - start_time) < duration:
                    ret, frame = self.camera.read()
                    if not ret:
                        continue

                    # Detect objects every few frames to reduce CPU usage
                    if frame_count % 5 == 0:
                        objects = self.detect_objects_in_frame(frame)
                        if objects:
                            timestamp = time.time() - start_time
                            for obj in objects:
                                obj['timestamp'] = timestamp
                            self.detected_objects.extend(objects)

                    frame_count += 1
                    time.sleep(0.1)  # Small delay to prevent excessive CPU usage

                self.tracking_active = False
                print(f"✅ Object tracking completed - {len(self.detected_objects)} detections")

            # Start tracking in background thread
            tracking_thread = threading.Thread(target=tracking_loop)
            tracking_thread.daemon = True
            tracking_thread.start()

            # Wait for completion
            tracking_thread.join()

            return self.summarize_tracking_results()

        except Exception as e:
            print(f"❌ Object tracking failed: {e}")
            self.tracking_active = False
            return None

    def summarize_tracking_results(self):
        """Summarize object tracking results"""
        if not self.detected_objects:
            return "No objects or motion detected during tracking period."

        # Count object types
        face_count = len([obj for obj in self.detected_objects if obj['type'] == 'face'])
        motion_count = len([obj for obj in self.detected_objects if obj['type'] == 'motion'])

        summary = f"🎯 OBJECT TRACKING SUMMARY:\n"
        summary += f"📊 Total detections: {len(self.detected_objects)}\n"

        if face_count > 0:
            summary += f"👤 Faces detected: {face_count} instances\n"

        if motion_count > 0:
            summary += f"🏃 Motion events: {motion_count} instances\n"

        # Timeline of events
        if len(self.detected_objects) > 0:
            summary += f"\n⏰ TIMELINE:\n"
            for i, obj in enumerate(self.detected_objects[:10]):  # Show first 10
                timestamp = obj.get('timestamp', 0)
                obj_type = obj['type']
                summary += f"   {timestamp:.1f}s: {obj_type.title()} detected\n"

            if len(self.detected_objects) > 10:
                summary += f"   ... and {len(self.detected_objects) - 10} more detections\n"

        return summary

    def analyze_camera_objects(self):
        """Analyze current camera frame for objects"""
        image_data, size = self.capture_camera_frame()
        if not image_data:
            return None

        # Detect objects in current frame
        if self.last_frame is not None:
            objects = self.detect_objects_in_frame(self.last_frame)

            if objects:
                object_summary = f"🎯 DETECTED OBJECTS:\n"
                for obj in objects:
                    obj_type = obj['type']
                    confidence = obj['confidence']
                    object_summary += f"   • {obj_type.title()} (confidence: {confidence:.1f})\n"
            else:
                object_summary = "No specific objects detected in current frame."
        else:
            object_summary = "Object detection not available."

        # Combine with AI analysis
        enhanced_question = f"Analyze this camera feed image. Also note: {object_summary}"

        # Try online analysis first
        if self.online_mode:
            result = self.analyze_online(image_data, enhanced_question)
            if result:
                result['image_size'] = f"{size[0]}x{size[1]}" if size else "unknown"
                result['capture_type'] = 'camera'
                result['objects_detected'] = len(objects) if 'objects' in locals() else 0
                return result

        # Fallback to offline analysis
        result = self.analyze_offline(enhanced_question)
        if result and size:
            result['image_size'] = f"{size[0]}x{size[1]}"
            result['capture_type'] = 'camera'
            result['objects_detected'] = len(objects) if 'objects' in locals() else 0
        return result

    def start_live_camera_mode(self):
        """Start live camera preview window with real-time interaction"""
        if not GUI_AVAILABLE:
            print("❌ Live camera mode requires GUI support")
            print("💡 Install tkinter (usually included with Python)")
            return False

        if not CAMERA_AVAILABLE:
            print("❌ Live camera mode requires camera support")
            print("💡 Install with: pip install opencv-python")
            return False

        print("🚀 Starting live camera mode...")
        print("📷 Opening camera preview window with real-time AI interaction")

        try:
            self.live_window = LiveCameraWindow(self)
            return self.live_window.run()
        except Exception as e:
            print(f"❌ Failed to start live camera mode: {e}")
            return False

    def close_camera(self):
        """Close camera and cleanup"""
        if self.camera and self.camera_active:
            self.camera.release()
            self.camera_active = False
            self.tracking_active = False
            print("📷 Camera closed")

        if self.live_window:
            self.live_window.close_window()
            self.live_window = None
    
    def find_window(self, app_name):
        """Find window by application name"""
        if not WINDOW_CAPTURE_AVAILABLE:
            return None

        try:
            import pygetwindow as gw

            # Common application name mappings
            app_mappings = {
                'chrome': ['chrome', 'google chrome', 'chromium'],
                'firefox': ['firefox', 'mozilla firefox'],
                'edge': ['edge', 'microsoft edge', 'msedge'],
                'vscode': ['visual studio code', 'code', 'vscode'],
                'notepad': ['notepad', 'notepad++'],
                'explorer': ['explorer', 'file explorer', 'windows explorer'],
                'cmd': ['command prompt', 'cmd', 'powershell', 'terminal'],
                'discord': ['discord'],
                'spotify': ['spotify'],
                'teams': ['microsoft teams', 'teams'],
                'word': ['microsoft word', 'word', 'winword'],
                'excel': ['microsoft excel', 'excel'],
                'powerpoint': ['microsoft powerpoint', 'powerpoint'],
                'outlook': ['microsoft outlook', 'outlook'],
                'steam': ['steam'],
                'zoom': ['zoom'],
            }

            # Get all windows
            windows = gw.getAllWindows()

            # Find matching window
            search_terms = app_mappings.get(app_name.lower(), [app_name.lower()])

            for window in windows:
                if window.title and any(term in window.title.lower() for term in search_terms):
                    if window.width > 100 and window.height > 100:  # Skip tiny windows
                        return window

            return None

        except Exception as e:
            print(f"⚠️  Window detection error: {e}")
            return None

    def capture_window(self, app_name):
        """Capture specific application window"""
        if not WINDOW_CAPTURE_AVAILABLE:
            print("⚠️  Window capture not available - install: pip install pygetwindow pyautogui")
            return self.capture_screen()  # Fallback to full screen

        try:
            import pygetwindow as gw
            import pyautogui

            # Find the window
            window = self.find_window(app_name)
            if not window:
                print(f"❌ Could not find '{app_name}' window")
                print("📋 Available windows:")
                windows = gw.getAllWindows()
                for w in windows[:10]:  # Show first 10
                    if w.title and w.width > 100:
                        print(f"   - {w.title[:50]}")
                print("🔄 Falling back to full screen capture...")
                return self.capture_screen()

            # Bring window to front
            try:
                window.activate()
                time.sleep(0.5)  # Wait for window to come to front
            except:
                pass  # Some windows can't be activated

            # Capture the window area
            print(f"📸 Capturing '{app_name}' window: {window.title[:50]}")
            print(f"📐 Window size: {window.width}x{window.height}")

            # Take screenshot of window area
            screenshot = pyautogui.screenshot(region=(window.left, window.top, window.width, window.height))

            # Save screenshot
            screenshot_path = Path(f"gini_{app_name}_capture.png")
            screenshot.save(screenshot_path)
            print(f"💾 Screenshot saved: {screenshot_path}")

            # Convert to base64
            buffer = BytesIO()
            screenshot.save(buffer, format='PNG')
            img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

            print(f"✅ Window captured: {screenshot.size[0]}x{screenshot.size[1]} pixels")
            return img_base64, screenshot.size

        except Exception as e:
            print(f"❌ Window capture failed: {e}")
            print("🔄 Falling back to full screen capture...")
            return self.capture_screen()

    def capture_screen(self, monitor_number=1):
        """Capture the full screen"""
        if not MSS_AVAILABLE:
            print("❌ Screen capture not available - install mss: pip install mss")
            return None, None

        try:
            with mss.mss() as sct:
                monitors = sct.monitors
                if monitor_number >= len(monitors):
                    monitor_number = 1

                monitor = monitors[monitor_number]
                print(f"📸 Capturing full screen {monitor_number}: {monitor['width']}x{monitor['height']}")

                screenshot = sct.grab(monitor)
                img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")

                # Save screenshot
                screenshot_path = Path("gini_screenshot.png")
                img.save(screenshot_path)
                print(f"💾 Screenshot saved: {screenshot_path}")

                # Convert to base64 for API
                buffer = BytesIO()
                img.save(buffer, format='PNG')
                img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

                print(f"✅ Screen captured: {img.size[0]}x{img.size[1]} pixels")
                return img_base64, img.size

        except Exception as e:
            print(f"❌ Screen capture failed: {e}")
            return None, None
    
    def analyze_online(self, image_data, question=None):
        """Analyze with real Gemini API"""
        if not self.online_mode:
            return None
        
        try:
            print("🧠 Analyzing with Gemini AI...")
            
            # Decode image
            image_bytes = base64.b64decode(image_data)
            image = Image.open(BytesIO(image_bytes))
            
            # Create prompt
            if question:
                prompt = f"Looking at this screen capture, please answer: {question}"
            else:
                prompt = """Analyze this screen capture and provide:
1. Main Content: What's the primary content/application?
2. UI Elements: Key interface elements visible
3. Text Content: Summary of visible text
4. Activity: What appears to be happening?
5. Context: Type of work/activity
6. Notable Details: Important details worth mentioning"""
            
            # Generate response
            response = self.model.generate_content(
                [prompt, image],
                generation_config=genai.types.GenerationConfig(
                    temperature=0.3,
                    max_output_tokens=500
                )
            )
            
            return {
                'success': True,
                'analysis': response.text,
                'model': self.model_name,
                'mode': 'online'
            }
            
        except Exception as e:
            error_str = str(e)
            if "429" in error_str or "quota" in error_str.lower():
                print("⚠️  Quota exceeded - switching to offline mode")
                self.online_mode = False
                return None
            else:
                print(f"❌ API error: {e}")
                return None
    
    def analyze_offline(self, question=None):
        """Analyze with mock responses"""
        print("🤖 Analyzing with AI (Offline Demo Mode)...")
        time.sleep(1.5)  # Simulate processing
        
        if question and question.strip():
            # Custom responses for specific questions
            q_lower = question.lower()
            if "error" in q_lower:
                analysis = "I don't see any obvious error messages on your screen. The terminal/code output appears to show normal program execution and development work."
            elif "language" in q_lower or "programming" in q_lower:
                analysis = "The code visible on your screen appears to be **Python**. I can see Python syntax, imports, file operations, and typical Python development patterns."
            elif "document" in q_lower:
                analysis = "I can see what appears to be code/terminal output rather than a traditional document. This looks like a development environment with programming content."
            elif "doing" in q_lower or "activity" in q_lower:
                analysis = "You appear to be doing software development work - writing code, running scripts, debugging, or working with development tools in a terminal/IDE environment."
            else:
                analysis = f"Based on your question '{question}', I can see relevant development-related content on your screen including code, terminal output, and programming tools."
        else:
            # Get rotating demo response
            analysis = self.demo_responses[self.response_index]
            self.response_index = (self.response_index + 1) % len(self.demo_responses)
        
        return {
            'success': True,
            'analysis': analysis,
            'model': 'offline-demo',
            'mode': 'offline'
        }
    
    def parse_capture_request(self, user_input):
        """Parse user input to detect capture type and target"""
        input_lower = user_input.lower()

        # Live camera mode patterns
        live_camera_patterns = [
            'live camera', 'live mode', 'camera preview', 'live preview',
            'interactive camera', 'real-time camera', 'camera window',
            'open camera', 'start live camera'
        ]

        # Camera detection patterns
        camera_patterns = [
            'camera', 'webcam', 'cam', 'camera feed', 'live feed',
            'what do you see on my camera', 'analyze my camera',
            'camera view', 'webcam view'
        ]

        # Object tracking patterns
        tracking_patterns = [
            'track', 'tracking', 'objects', 'moving', 'motion',
            'track objects', 'what objects', 'detect objects',
            'what\'s moving', 'monitor camera', 'live tracking'
        ]

        # Check for live camera mode requests first
        for pattern in live_camera_patterns:
            if pattern in input_lower:
                return 'live_camera'

        # Check for camera requests
        for pattern in camera_patterns:
            if pattern in input_lower:
                # Check if it's also a tracking request
                for track_pattern in tracking_patterns:
                    if track_pattern in input_lower:
                        return 'camera_tracking'
                return 'camera'

        # Check for tracking requests
        for pattern in tracking_patterns:
            if pattern in input_lower and any(cam in input_lower for cam in ['camera', 'cam', 'webcam']):
                return 'camera_tracking'

        # Window detection patterns
        window_patterns = {
            'chrome': ['chrome', 'browser', 'google chrome'],
            'firefox': ['firefox', 'mozilla'],
            'edge': ['edge', 'microsoft edge'],
            'vscode': ['vscode', 'visual studio code', 'code editor', 'vs code'],
            'notepad': ['notepad', 'text editor'],
            'explorer': ['explorer', 'file explorer', 'folder'],
            'cmd': ['terminal', 'command prompt', 'cmd', 'powershell'],
            'discord': ['discord'],
            'spotify': ['spotify'],
            'teams': ['teams', 'microsoft teams'],
            'word': ['word', 'microsoft word', 'document'],
            'excel': ['excel', 'spreadsheet'],
            'powerpoint': ['powerpoint', 'presentation'],
            'outlook': ['outlook', 'email'],
            'steam': ['steam', 'game'],
            'zoom': ['zoom', 'meeting'],
        }

        # Check for window-specific phrases
        for app_name, patterns in window_patterns.items():
            for pattern in patterns:
                if pattern in input_lower:
                    # Additional context clues
                    if any(phrase in input_lower for phrase in [
                        f'in {pattern}', f'on {pattern}', f'in the {pattern}',
                        f'in my {pattern}', f'{pattern} window', f'{pattern} page',
                        f'what do you see in {pattern}', f'focus on {pattern}'
                    ]):
                        return app_name

        return None

    def analyze_screen(self, question=None, target_window=None, capture_mode=None):
        """Main analysis function with multiple capture modes"""
        # Parse for capture type and target
        if question and not target_window and not capture_mode:
            capture_request = self.parse_capture_request(question)

            if capture_request == 'live_camera':
                print("🚀 Starting live camera mode...")
                success = self.start_live_camera_mode()
                if success:
                    return {
                        'success': True,
                        'analysis': 'Live camera mode started successfully! Use the interactive window to analyze your camera feed in real-time.',
                        'mode': 'live_camera',
                        'capture_type': 'live_camera'
                    }
                else:
                    return {
                        'success': False,
                        'analysis': 'Failed to start live camera mode. Check camera permissions and requirements.',
                        'mode': 'live_camera_failed',
                        'capture_type': 'live_camera'
                    }
            elif capture_request == 'camera':
                return self.analyze_camera_objects()
            elif capture_request == 'camera_tracking':
                print("🎯 Starting object tracking mode...")
                tracking_result = self.start_object_tracking(duration=10)
                if tracking_result:
                    return {
                        'success': True,
                        'analysis': tracking_result,
                        'mode': 'camera_tracking',
                        'capture_type': 'camera_tracking'
                    }
                else:
                    return self.analyze_camera_objects()  # Fallback to single frame
            else:
                # Check for window-specific request
                target_window = self.parse_window_request(question)

        # Handle different capture modes
        if capture_mode == 'camera':
            return self.analyze_camera_objects()
        elif capture_mode == 'camera_tracking':
            tracking_result = self.start_object_tracking(duration=10)
            if tracking_result:
                return {
                    'success': True,
                    'analysis': tracking_result,
                    'mode': 'camera_tracking',
                    'capture_type': 'camera_tracking'
                }
            else:
                return self.analyze_camera_objects()

        # Capture screen or specific window
        if target_window:
            print(f"🎯 Focusing on '{target_window}' window...")
            image_data, size = self.capture_window(target_window)
            capture_type = 'window'
        else:
            image_data, size = self.capture_screen()
            capture_type = 'screen'

        if not image_data:
            return None

        # Enhance question for specific analysis
        enhanced_question = question
        if target_window and question:
            enhanced_question = f"Looking specifically at the {target_window} application window, {question}"
        elif target_window:
            enhanced_question = f"Analyze what's visible in the {target_window} application window"

        # Try online analysis first
        if self.online_mode:
            result = self.analyze_online(image_data, enhanced_question)
            if result:
                result['image_size'] = f"{size[0]}x{size[1]}" if size else "unknown"
                result['target_window'] = target_window
                result['capture_type'] = capture_type
                return result

        # Fallback to offline analysis
        result = self.analyze_offline(enhanced_question)
        if result and size:
            result['image_size'] = f"{size[0]}x{size[1]}"
            result['target_window'] = target_window
            result['capture_type'] = capture_type
        return result

    def parse_window_request(self, user_input):
        """Parse user input to detect window-specific requests (legacy method)"""
        input_lower = user_input.lower()

        # Window detection patterns
        window_patterns = {
            'chrome': ['chrome', 'browser', 'google chrome'],
            'firefox': ['firefox', 'mozilla'],
            'edge': ['edge', 'microsoft edge'],
            'vscode': ['vscode', 'visual studio code', 'code editor', 'vs code'],
            'notepad': ['notepad', 'text editor'],
            'explorer': ['explorer', 'file explorer', 'folder'],
            'cmd': ['terminal', 'command prompt', 'cmd', 'powershell'],
            'discord': ['discord'],
            'spotify': ['spotify'],
            'teams': ['teams', 'microsoft teams'],
            'word': ['word', 'microsoft word', 'document'],
            'excel': ['excel', 'spreadsheet'],
            'powerpoint': ['powerpoint', 'presentation'],
            'outlook': ['outlook', 'email'],
            'steam': ['steam', 'game'],
            'zoom': ['zoom', 'meeting'],
        }

        # Check for window-specific phrases
        for app_name, patterns in window_patterns.items():
            for pattern in patterns:
                if pattern in input_lower:
                    # Additional context clues
                    if any(phrase in input_lower for phrase in [
                        f'in {pattern}', f'on {pattern}', f'in the {pattern}',
                        f'in my {pattern}', f'{pattern} window', f'{pattern} page',
                        f'what do you see in {pattern}', f'focus on {pattern}'
                    ]):
                        return app_name

        return None
    
    def print_result(self, result):
        """Print analysis result"""
        if not result:
            print("❌ Analysis failed")
            return

        print("\n" + "="*70)

        # Dynamic title based on capture type
        capture_type = result.get('capture_type', 'screen')
        if capture_type == 'camera':
            print("📷 AI CAMERA ANALYSIS")
        elif capture_type == 'camera_tracking':
            print("🎯 AI OBJECT TRACKING RESULTS")
        elif result.get('target_window'):
            print(f"🎯 AI ANALYSIS - {result['target_window'].upper()} WINDOW")
        else:
            print("🤖 AI ANALYSIS RESULTS")
        print("="*70)

        # Metadata
        meta = []
        if result.get('mode'):
            meta.append(f"Mode: {result['mode']}")
        if result.get('capture_type'):
            meta.append(f"Source: {result['capture_type']}")
        if result.get('target_window'):
            meta.append(f"Target: {result['target_window']}")
        if result.get('objects_detected') is not None:
            meta.append(f"Objects: {result['objects_detected']}")
        if result.get('image_size'):
            meta.append(f"Size: {result['image_size']}")
        if result.get('model'):
            meta.append(f"Model: {result['model']}")

        if meta:
            print(f"📊 {' | '.join(meta)}")
            print("-"*70)

        # Analysis
        print(result['analysis'])

        if result.get('mode') == 'offline':
            print("\n💡 This is a demo response. For real AI analysis:")
            print("   - Wait for quota reset (1 minute for per-minute limits)")
            print("   - Try a different API key")
            print("   - Upgrade to paid plan")

        print("="*70)
    
    def check_quota(self):
        """Check API quota status"""
        print("🔍 CHECKING QUOTA STATUS")
        print("="*40)
        
        if not GEMINI_AVAILABLE:
            print("❌ Gemini not installed: pip install google-generativeai")
            return
        
        api_key = os.getenv('GOOGLE_AI_API_KEY')
        if not api_key:
            print("❌ No API key configured")
            return
        
        print(f"✅ API Key: {api_key[:10]}...{api_key[-4:]}")
        
        models = ['gemini-1.5-flash', 'gemini-pro-vision', 'gemini-1.5-pro']
        working = []
        
        for model_name in models:
            try:
                model = genai.GenerativeModel(model_name)
                test_img = Image.new('RGB', (1, 1), 'white')
                
                response = model.generate_content(
                    ["Test", test_img],
                    generation_config=genai.types.GenerationConfig(max_output_tokens=1)
                )
                
                print(f"✅ {model_name} - Working")
                working.append(model_name)
                
            except Exception as e:
                if "429" in str(e):
                    print(f"❌ {model_name} - Quota exceeded")
                else:
                    print(f"⚠️  {model_name} - {str(e)[:30]}...")
        
        if working:
            print(f"\n🎉 Available models: {', '.join(working)}")
        else:
            print("\n⚠️  All models quota exceeded")
            self.show_quota_solutions()
    
    def show_quota_solutions(self):
        """Show quota solutions"""
        print("\n💡 QUOTA SOLUTIONS:")
        print("   1. ⏰ Wait 1-5 minutes (per-minute quota)")
        print("   2. ⏰ Wait until tomorrow (daily quota)")
        print("   3. 🔑 Create new API key: https://aistudio.google.com/app/apikey")
        print("   4. 💳 Upgrade plan: https://aistudio.google.com/")
        print("   5. 🤖 Use offline demo mode (this script)")

    def list_windows(self):
        """List all available windows"""
        if not WINDOW_CAPTURE_AVAILABLE:
            print("⚠️  Window detection not available - install: pip install pygetwindow")
            return

        try:
            import pygetwindow as gw

            print("🪟 AVAILABLE WINDOWS:")
            print("="*50)

            windows = gw.getAllWindows()
            app_windows = []

            for window in windows:
                if window.title and window.width > 100 and window.height > 100:
                    app_windows.append(window)

            if not app_windows:
                print("❌ No suitable windows found")
                return

            # Group by common applications
            grouped = {}
            for window in app_windows[:20]:  # Limit to first 20
                title = window.title.lower()
                app_type = "other"

                if any(x in title for x in ['chrome', 'chromium']):
                    app_type = "🌐 Chrome"
                elif any(x in title for x in ['firefox', 'mozilla']):
                    app_type = "🦊 Firefox"
                elif any(x in title for x in ['edge', 'msedge']):
                    app_type = "🔷 Edge"
                elif any(x in title for x in ['visual studio code', 'code']):
                    app_type = "💻 VS Code"
                elif any(x in title for x in ['notepad']):
                    app_type = "📝 Notepad"
                elif any(x in title for x in ['explorer']):
                    app_type = "📁 Explorer"
                elif any(x in title for x in ['cmd', 'powershell', 'terminal']):
                    app_type = "⚫ Terminal"
                elif any(x in title for x in ['discord']):
                    app_type = "💬 Discord"
                elif any(x in title for x in ['spotify']):
                    app_type = "🎵 Spotify"
                elif any(x in title for x in ['teams']):
                    app_type = "👥 Teams"
                elif any(x in title for x in ['word']):
                    app_type = "📄 Word"
                elif any(x in title for x in ['excel']):
                    app_type = "📊 Excel"

                if app_type not in grouped:
                    grouped[app_type] = []
                grouped[app_type].append(window.title[:60])

            for app_type, titles in grouped.items():
                print(f"\n{app_type}:")
                for title in titles[:3]:  # Show max 3 per type
                    print(f"   • {title}")
                if len(titles) > 3:
                    print(f"   ... and {len(titles)-3} more")

            print(f"\n💡 Use: 'what do you see in chrome?' to focus on specific app")

        except Exception as e:
            print(f"❌ Error listing windows: {e}")
    
    def interactive_mode(self):
        """Interactive chat mode"""
        print("\n" + "="*70)
        print("🚀 GINI 1.5 - COMPLETE AI SCREEN ANALYZER")
        print("="*70)
        print("🧠 Powered by Google Gemini 1.5 Pro Vision")
        print("📸 Screen capture + AI analysis in one script")
        if self.online_mode:
            print("🌐 Mode: Online (Real AI)")
        else:
            print("🤖 Mode: Offline (Demo AI)")
        print("="*70)
        print()
        print("💬 COMMANDS:")
        print("  🖥️  SCREEN: 'what do you see on my screen?'")
        print("  🎯 WINDOW: 'what do you see in chrome?'")
        print("  📷 CAMERA: 'what do you see on my camera?'")
        print("  � LIVE: 'start live camera' or 'live mode'")
        print("  �🎯 TRACKING: 'track objects in my camera'")
        print("  🔧 UTILS: 'capture', 'quota', 'windows', 'help', 'quit'")
        print()
        print("📷 CAMERA FEATURES:")
        if CAMERA_AVAILABLE and GUI_AVAILABLE:
            print("  ✅ Live camera preview window")
            print("  ✅ Real-time interactive analysis")
            print("  ✅ Object tracking and motion detection")
            print("  ✅ Live screenshot capture")
        elif CAMERA_AVAILABLE:
            print("  ✅ Camera analysis (install tkinter for live mode)")
            print("  ✅ Object tracking and motion detection")
        else:
            print("  ❌ Install opencv-python for camera features")
        print()
        print("🎯 SUPPORTED APPS:")
        print("  chrome, firefox, edge, vscode, notepad, terminal, discord,")
        print("  spotify, teams, word, excel, powerpoint, outlook, steam, zoom")
        print()
        
        while True:
            try:
                user_input = input("🎤 You: ").strip()
                
                if not user_input:
                    continue
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                elif user_input.lower() == 'help':
                    self.show_help()
                
                elif user_input.lower() == 'quota':
                    self.check_quota()

                elif user_input.lower() == 'windows':
                    self.list_windows()

                elif user_input.lower() == 'capture':
                    _, size = self.capture_screen()
                    if size:
                        print(f"✅ Screen captured: {size[0]}x{size[1]} pixels")

                elif user_input.lower() in ['live', 'live camera', 'live mode', 'camera preview']:
                    # Start live camera mode
                    result = self.analyze_screen("start live camera")
                    self.print_result(result)

                elif user_input.lower() in ['camera', 'cam']:
                    # Quick camera capture
                    result = self.analyze_screen(capture_mode='camera')
                    self.print_result(result)

                elif user_input.lower() in ['track', 'tracking']:
                    # Quick object tracking
                    result = self.analyze_screen(capture_mode='camera_tracking')
                    self.print_result(result)

                elif 'camera' in user_input.lower() or 'webcam' in user_input.lower():
                    # Camera-related commands
                    result = self.analyze_screen(user_input)
                    self.print_result(result)

                elif 'track' in user_input.lower() and ('object' in user_input.lower() or 'motion' in user_input.lower()):
                    # Object tracking commands
                    result = self.analyze_screen(user_input)
                    self.print_result(result)

                elif 'what do you see' in user_input.lower() or 'whats on my screen' in user_input.lower():
                    result = self.analyze_screen(user_input)
                    self.print_result(result)

                elif '?' in user_input:
                    # Custom question
                    result = self.analyze_screen(user_input)
                    self.print_result(result)
                
                else:
                    print("❓ Try: 'what do you see on my screen?' or 'help'")
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def show_help(self):
        """Show help"""
        print("\n📖 HELP - GINI 1.5 ENHANCED COMMANDS")
        print("="*60)
        print("🖥️  SCREEN ANALYSIS:")
        print("   'what do you see on my screen?'")
        print("   'whats on my screen?'")
        print("   'What errors do you see on my screen?'")
        print("   'What programming language is this?'")
        print()
        print("🎯 WINDOW-SPECIFIC ANALYSIS:")
        print("   'what do you see in chrome?'")
        print("   'what do you see in vscode?'")
        print("   'what errors do you see in terminal?'")
        print("   'focus on discord and tell me what you see'")
        print()
        print("🎥 LIVE CAMERA MODE:")
        if CAMERA_AVAILABLE and GUI_AVAILABLE:
            print("   'start live camera' - Open interactive camera window")
            print("   'live mode' - Real-time camera preview with AI")
            print("   'camera preview' - Live video feed analysis")
            print("   Features: Real-time interaction, live screenshots, instant AI responses")
        else:
            missing = []
            if not CAMERA_AVAILABLE:
                missing.append("opencv-python")
            if not GUI_AVAILABLE:
                missing.append("tkinter")
            print(f"   ❌ Install {' and '.join(missing)} for live camera mode")
        print()
        print("📷 CAMERA ANALYSIS:")
        if CAMERA_AVAILABLE:
            print("   'what do you see on my camera?'")
            print("   'analyze my camera feed'")
            print("   'what do you see on my webcam?'")
            print("   'camera' - Quick camera analysis")
        else:
            print("   ❌ Install opencv-python for camera features")
        print()
        print("🎯 OBJECT TRACKING:")
        if CAMERA_AVAILABLE:
            print("   'track objects in my camera'")
            print("   'what objects do you see?'")
            print("   'what's moving in front of my camera?'")
            print("   'monitor my camera for motion'")
            print("   'track' - Quick object tracking")
        else:
            print("   ❌ Requires camera support (opencv-python)")
        print()
        print("🔧 UTILITIES:")
        print("   'capture' - Capture full screen")
        print("   'windows' - List available windows")
        print("   'quota' - Check API quota status")
        print("   'help' - Show this help")
        print("   'quit' - Exit program")
        print()
        print("🎯 SUPPORTED APPLICATIONS:")
        print("   Chrome, Firefox, Edge, VS Code, Notepad, Terminal,")
        print("   Discord, Spotify, Teams, Word, Excel, PowerPoint,")
        print("   Outlook, Steam, Zoom, File Explorer")
        print()
        print("💡 TIPS:")
        print("   • Ask any question about screen, window, or camera content")
        print("   • Use natural language for all commands")
        print("   • Camera tracking runs for 10 seconds by default")
        print("   • Object detection includes faces and motion")
        print("   • All captures are saved as image files for reference")

def main():
    """Main function"""
    print("🚀 GINI 1.5 - ALL-IN-ONE SCREEN ANALYZER")
    print("="*50)
    
    # Check dependencies
    missing = []
    if not MSS_AVAILABLE:
        missing.append("mss")
    if not PIL_AVAILABLE:
        missing.append("pillow")
    if not GEMINI_AVAILABLE:
        missing.append("google-generativeai")

    optional_missing = []
    if not CAMERA_AVAILABLE:
        optional_missing.append("opencv-python (for camera features)")
    if not WINDOW_CAPTURE_AVAILABLE:
        optional_missing.append("pygetwindow pyautogui (for window capture)")
    if not GUI_AVAILABLE:
        optional_missing.append("tkinter (for live camera mode - usually included)")

    if missing:
        print(f"⚠️  Missing required packages: {', '.join(missing)}")
        print(f"💡 Install with: pip install {' '.join(missing)}")
        print("🤖 Continuing in limited mode...")

    if optional_missing:
        print(f"💡 Enhanced features available with: pip install {' '.join([pkg for pkg in optional_missing if 'tkinter' not in pkg])}")
        if any('tkinter' in pkg for pkg in optional_missing):
            print("💡 tkinter is usually included with Python - check your installation")

    analyzer = GiniScreenAnalyzer()

    try:
        if len(sys.argv) > 1:
            command = ' '.join(sys.argv[1:]).lower()

            if 'live' in command and 'camera' in command:
                result = analyzer.analyze_screen(command)
                analyzer.print_result(result)
            elif 'camera' in command or 'webcam' in command:
                result = analyzer.analyze_screen(command)
                analyzer.print_result(result)
            elif 'track' in command and ('object' in command or 'motion' in command):
                result = analyzer.analyze_screen(command)
                analyzer.print_result(result)
            elif 'screen' in command or 'what' in command or 'chrome' in command or 'vscode' in command:
                result = analyzer.analyze_screen(command if '?' in command else None)
                analyzer.print_result(result)
            elif command == 'quota':
                analyzer.check_quota()
            elif command == 'windows':
                analyzer.list_windows()
            elif command == 'capture':
                analyzer.capture_screen()
            else:
                print(f"❓ Unknown: {command}")
                print("💡 Examples:")
                print("   python gini_15_complete.py 'what do you see on my screen?'")
                print("   python gini_15_complete.py 'what do you see on my camera?'")
                print("   python gini_15_complete.py 'start live camera'")
                print("   python gini_15_complete.py 'track objects in my camera'")
        else:
            # Auto-start live camera mode if available
            if CAMERA_AVAILABLE and GUI_AVAILABLE:
                print("🚀 Auto-starting live camera mode...")
                print("📷 Opening interactive camera window...")
                result = analyzer.analyze_screen("start live camera")
                if not result or not result.get('success'):
                    print("⚠️  Live camera failed, falling back to interactive mode")
                    analyzer.interactive_mode()
            else:
                print("💡 Live camera mode not available - using interactive terminal mode")
                if not CAMERA_AVAILABLE:
                    print("   Install opencv-python for camera features")
                if not GUI_AVAILABLE:
                    print("   Install tkinter for GUI features")
                analyzer.interactive_mode()

    except KeyboardInterrupt:
        print("\n👋 Shutting down...")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    finally:
        # Cleanup
        analyzer.close_camera()
        print("🔧 Cleanup completed")

if __name__ == "__main__":
    main()
