<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nova AI - Auto-Refresh Enabled</title>
    <!-- Auto-refresh test marker: v1.1 - Auto-refresh is now active! -->
    <!-- FontAwesome for better icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --chat-position: 15vh; /* CHANGE THIS: 5vh = higher up, 20vh = lower down */
            --chat-spacing: 3vh;   /* CHANGE THIS: 1vh = closer to NOVA, 5vh = further from NOVA */
            --messages-to-input-spacing: 0.8vh; /* CHANGE THIS: 0.3vh = very close together, 2vh = far apart */
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body, html {
            height: 100%;
            overflow: hidden;
        }

        .grid-container {
            width: 100vw;
            height: 100vh;
            background: radial-gradient(ellipse at 30% 50%, #0C294F 0%, #061D3B 70%, #041529 100%);
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            padding-top: var(--chat-position, 10vh); /* CHANGE THIS VALUE: 5vh = higher, 15vh = lower */
        }

        .dot-grid {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
            background-image: 
                radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.8) 1px, transparent 1px);
            background-size: 40px 40px;
            background-position: 0 0;
            opacity: 0.6;
            animation: pulse 4s ease-in-out infinite alternate, gridShift 8s linear infinite;
        }

        .dot-grid::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(ellipse at 25% 40%, rgba(12, 41, 79, 0.3) 0%, transparent 50%),
                radial-gradient(ellipse at 75% 60%, rgba(6, 29, 59, 0.2) 0%, transparent 40%);
            pointer-events: none;
        }

        .glow-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 50%, rgba(0, 200, 255, 0.1) 0%, transparent 60%);
            pointer-events: none;
        }

        @keyframes pulse {
            0% {
                opacity: 0.4;
                transform: scale(1);
            }
            100% {
                opacity: 0.7;
                transform: scale(1.002);
            }
        }

        @keyframes gridShift {
            0% { background-position: 0 0; }
            50% { background-position: 2px 2px; }
            100% { background-position: 0 0; }
        }

        .jarvis-interface {
            position: relative;
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            transform: scale(0.9);
            transform-origin: center center;
            margin-bottom: var(--chat-spacing, 2vh); /* CHANGE THIS VALUE: 1vh = closer to NOVA, 4vh = further from NOVA */
        }

        .concentric-circles {
            position: relative;
            width: 300px;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .outer-ring {
            position: absolute;
            width: 280px;
            height: 280px;
            border: 3px solid rgba(0, 255, 255, 1);
            border-radius: 50%;
            box-shadow: 
                0 0 15px rgba(0, 255, 255, 0.9),
                0 0 30px rgba(0, 255, 255, 0.7),
                0 0 50px rgba(0, 255, 255, 0.5),
                0 0 80px rgba(0, 255, 255, 0.3),
                inset 0 0 15px rgba(0, 255, 255, 0.4);
            animation: outerRingPulse 3s ease-in-out infinite alternate;
        }

        .inner-ring {
            position: absolute;
            width: 200px;
            height: 200px;
            border: 2px solid rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            box-shadow: 0 0 10px rgb(255 255 255);
        }

        .jarvis-text {
            position: relative;
            color: rgb(255, 255, 255);
            font-family: 'Orbitron', sans-serif;
            font-size: 2.5rem;
            font-weight: 700;
            letter-spacing: 0.15em;
            text-transform: uppercase;
            text-shadow: 
                0 0 8px rgba(255, 255, 255, 0.8),
                0 0 15px rgba(255, 255, 255, 0.5),
                0 0 25px rgba(255, 255, 255, 0.3);
            animation: textGlow 2s ease-in-out infinite alternate;
        }

        @keyframes outerRingPulse {
            0% {
                box-shadow: 
                    0 0 15px rgba(0, 85, 255, 0.9),
                    0 0 30px rgba(0, 68, 255, 0.742),
                    0 0 50px rgba(0, 157, 255, 0.916),
                    0 0 80px rgba(0, 85, 255, 0.738),
                    inset 0 0 15px rgba(0, 149, 255, 0.764);
                transform: scale(1);
            }
            100% {
                box-shadow: 
                    0 0 20px rgba(0, 102, 255, 0.344),
                    0 0 40px rgba(0, 149, 255, 0.929),
                    0 0 70px rgba(0, 255, 255, 0.6),
                    0 0 100px rgba(0, 204, 255, 0.636),
                    inset 0 0 20px rgb(0, 195, 255);
                transform: scale(1.01);
            }
        }

        @keyframes textGlow {
            0% {
                text-shadow: 
                    0 0 10px rgb(255, 255, 255),
                    0 0 20px rgba(0, 200, 255, 0.3);
            }
            100% {
                text-shadow: 
                    0 0 15px rgb(255, 255, 255),
                    0 0 30px rgba(0, 200, 255, 0.5);
            }
        }

        /* Chat Messages Area */
        .chat-messages {
            position: relative;
            z-index: 15;
            width: 90%;
            max-width: 600px;
            height: 40vh;
            margin-bottom: var(--messages-to-input-spacing, 1vh); /* CHANGE THIS: 0.5vh = very close, 3vh = far apart */
            overflow-y: auto;
            padding: 20px;
            background: transparent;
            border: none;
            border-radius: 15px;
            backdrop-filter: none;
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 255, 255, 0.3) transparent;
        }

        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: transparent;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: rgba(0, 255, 255, 0.3);
            border-radius: 3px;
        }

        .message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 10px;
            max-width: 70%;
            animation: messageSlideIn 0.3s ease-out;
            background: transparent;
            border: 1px solid rgba(0, 255, 255, 0.15);
        }

        .user-message {
            background: transparent;
            border: 1px solid rgba(0, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.8);
            margin-left: auto;
            text-align: right;
        }

        .nova-message {
            background: transparent;
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            margin-right: auto;
        }

        .message-text {
            font-family: 'Segoe UI', system-ui, sans-serif;
            font-size: 11px;
            line-height: 1.3;
        }

        .typing-indicator {
            display: none;
            padding: 8px 12px;
            margin-bottom: 10px;
            background: transparent;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            max-width: 70%;
            margin-right: auto;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: rgba(0, 255, 255, 0.7);
            border-radius: 50%;
            animation: typingBounce 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typingBounce {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1.2); opacity: 1; }
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Chat Input Styles */
        .chat-container {
            position: relative;
            z-index: 20;
            width: 90%;
            max-width: 600px;
            padding: 0;
        }

        .chat-input-wrapper {
            position: relative;
            width: 100%;
        }

        .chat-input {
            width: 100%;
            background: transparent;
            border: 1px solid rgba(0, 255, 255, 0.2);
            border-radius: 15px;
            padding: 10px 45px 10px 14px;
            color: rgba(255, 255, 255, 0.8);
            font-family: 'Segoe UI', system-ui, sans-serif;
            font-size: 12px;
            outline: none;
            transition: all 0.3s ease;
            box-shadow: none;
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .chat-input:focus {
            border-color: rgba(0, 255, 255, 0.4);
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
            background: rgba(12, 41, 79, 0.05);
        }

        .send-button {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.6) 0%, rgba(0, 200, 255, 0.7) 100%);
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }

        .send-button:hover {
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.8) 0%, rgba(0, 200, 255, 0.9) 100%);
        }

        .send-button:active {
            transform: translateY(-50%) scale(0.95);
        }

        .send-icon {
            width: 14px;
            height: 14px;
            color: #041529;
            font-weight: bold;
        }

        .chat-input-wrapper::before {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            background: linear-gradient(45deg, 
                rgba(0, 255, 255, 0.1) 0%, 
                rgba(0, 200, 255, 0.15) 25%, 
                rgba(0, 255, 255, 0.1) 50%, 
                rgba(0, 200, 255, 0.15) 75%, 
                rgba(0, 255, 255, 0.1) 100%);
            border-radius: 16px;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
            animation: borderGlow 3s linear infinite;
        }

        .chat-input:focus + .send-button + .chat-input-wrapper::before,
        .chat-input-wrapper:hover::before {
            opacity: 1;
        }

        @keyframes borderGlow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* ================= FLOATING CHAT BUTTON ================= */
        /* 
         * CHAT BUTTON CUSTOMIZATION - CHANGE HERE:
         * - Size: 55px = perfect balance, 45px = smaller, 65px = larger
         * - Colors: Change the gradient colors for different themes
         */
        .floating-chat-button {
            position: fixed;
            bottom: 25px;
            right: 25px;
            width: 55px;    /* CHANGE THIS: Button size */
            height: 55px;   /* CHANGE THIS: Button size */
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.95) 0%, rgba(0, 200, 255, 1) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 
                0 6px 20px rgba(0, 255, 255, 0.3),
                0 0 0 0 rgba(0, 255, 255, 0.7),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            animation: chatButtonFloat 4s ease-in-out infinite alternate;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .floating-chat-button:hover {
            transform: scale(1.05) translateY(-2px);
            box-shadow: 
                0 15px 40px rgba(0, 255, 255, 0.4),
                0 0 0 8px rgba(0, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
        }

        .floating-chat-button:active {
            transform: scale(0.98);
        }

        .chat-button-icon {
            position: relative;
            z-index: 2;
            width: 24px;    /* CHANGE THIS: Icon size */
            height: 24px;   /* CHANGE THIS: Icon size */
            color: #041529;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
        }

        .chat-button-icon svg {
            width: 100%;
            height: 100%;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        .chat-button-pulse {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: rgba(0, 255, 255, 0.6);
            animation: chatButtonPulse 2s infinite;
            z-index: 1;
        }

        @keyframes chatButtonFloat {
            0% {
                transform: translateY(0px);
                box-shadow: 
                    0 8px 25px rgba(0, 255, 255, 0.4),
                    0 0 0 0 rgba(0, 255, 255, 0.7);
            }
            100% {
                transform: translateY(-6px);
                box-shadow: 
                    0 15px 35px rgba(0, 255, 255, 0.5),
                    0 0 0 5px rgba(0, 255, 255, 0.2);
            }
        }

        @keyframes chatButtonPulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.7;
            }
            100% {
                transform: scale(1.4);
                opacity: 0;
            }
        }

        /* ================= MODERN CHAT INTERFACE ================= */
        /* 
         * CHAT INTERFACE COLORS - CHANGE HERE TO CUSTOMIZE:
         * - Main Background: Same as main UI background gradient
         * - Border: Cyan accent color matching NOVA theme
         * - You can modify these colors below to match any theme
         */
        .modern-chat-interface {
            position: fixed;
            bottom: 30px;
            right: 30px;
            /* 
             * CHAT INTERFACE DIMENSIONS - CHANGE HERE TO CUSTOMIZE SIZE:
             * - Width: 360px = perfect balance, 320px = slimmer, 400px = wider
             * - Height: 650px = perfect balance, 600px = shorter, 700px = taller
             */
            width: 360px;   /* CHANGE THIS: Perfect balanced width */
            height: 650px;  /* CHANGE THIS: Perfect balanced height */
            /* MAIN BACKGROUND COLOR - Change this gradient to customize chat background */
            background: radial-gradient(ellipse at 30% 50%, #0C294F 0%, #061D3B 70%, #041529 100%);
            border-radius: 20px;
            box-shadow: 
                0 25px 50px rgba(0, 0, 0, 0.5),
                /* BORDER COLOR - Change rgba(0, 255, 255, 0.2) to customize border */
                0 0 0 1px rgba(0, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            z-index: 999;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            transform: translateY(100vh) scale(0.8);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(20px);
        }

        .modern-chat-interface.active {
            transform: translateY(0) scale(1);
            opacity: 1;
        }

        .modern-chat-interface.minimized {
            height: 60px;
            overflow: hidden;
        }

        /* Chat Header */
        .chat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            /* HEADER BACKGROUND - Change these rgba values to customize header color */
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.1) 0%, rgba(0, 200, 255, 0.05) 100%);
            /* HEADER BORDER - Change rgba(0, 255, 255, 0.2) to customize header border */
            border-bottom: 1px solid rgba(0, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .chat-header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .nova-avatar {
            position: relative;
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.8) 0%, rgba(0, 200, 255, 1) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #041529;
            font-family: 'Orbitron', sans-serif;
            font-weight: 700;
            font-size: 18px;
            box-shadow: 0 4px 15px rgba(0, 255, 255, 0.4);
        }

        .avatar-glow {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: rgba(0, 255, 255, 0.3);
            animation: avatarGlow 2s ease-in-out infinite alternate;
        }

        @keyframes avatarGlow {
            0% {
                transform: scale(1);
                opacity: 0.7;
            }
            100% {
                transform: scale(1.1);
                opacity: 0.3;
            }
        }

        .chat-header-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .chat-title {
            color: white;
            font-family: 'Orbitron', sans-serif;
            font-weight: 700;
            font-size: 16px;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .chat-status {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #00FF88;
            border-radius: 50%;
            animation: pulse 2s infinite;
            box-shadow: 0 0 6px #00FF88;
        }

        .chat-header-controls {
            display: flex;
            gap: 8px;
        }

        .chat-minimize-btn,
        .chat-close-btn {
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: rgba(255, 255, 255, 0.8);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .chat-minimize-btn:hover,
        .chat-close-btn:hover {
            background: rgba(0, 255, 255, 0.2);
            border-color: rgba(0, 255, 255, 0.4);
            color: white;
            transform: scale(1.1);
        }

        .chat-minimize-btn svg,
        .chat-close-btn svg {
            width: 16px;
            height: 16px;
        }

        /* Chat Messages Area */
        .modern-chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.1);
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 255, 255, 0.3) transparent;
        }

        .modern-chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .modern-chat-messages::-webkit-scrollbar-track {
            background: transparent;
        }

        .modern-chat-messages::-webkit-scrollbar-thumb {
            background: rgba(0, 255, 255, 0.3);
            border-radius: 3px;
        }

        .welcome-message {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
            animation: messageSlideIn 0.5s ease-out;
        }

        .welcome-avatar {
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.6) 0%, rgba(0, 200, 255, 0.8) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #041529;
            font-family: 'Orbitron', sans-serif;
            font-weight: 700;
            font-size: 14px;
            flex-shrink: 0;
        }

        .welcome-content {
            flex: 1;
        }

        .welcome-title {
            color: white;
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 6px;
            font-family: 'Orbitron', sans-serif;
        }

        .welcome-text {
            color: rgba(255, 255, 255, 0.8);
            font-size: 13px;
            line-height: 1.5;
        }

        /* Message Styles */
        .chat-message {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
            animation: messageSlideIn 0.3s ease-out;
        }

        .chat-message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Orbitron', sans-serif;
            font-weight: 700;
            font-size: 12px;
            flex-shrink: 0;
        }

        .message-avatar.nova-avatar-small {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.6) 0%, rgba(0, 200, 255, 0.8) 100%);
            color: #041529;
        }

        .message-avatar.user-avatar {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .message-content {
            flex: 1;
            max-width: 80%;
        }

        .message-bubble {
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 13px;
            line-height: 1.5;
            word-wrap: break-word;
            position: relative;
        }

        .chat-message:not(.user) .message-bubble {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.1) 0%, rgba(0, 200, 255, 0.05) 100%);
            border: 1px solid rgba(0, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.9);
            border-radius: 18px 18px 18px 4px;
        }

        .chat-message.user .message-bubble {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.9);
            border-radius: 18px 18px 4px 18px;
        }

        /* Typing indicator for modern chat */
        .chat-message.typing-message {
            opacity: 0.8;
        }

        .typing-bubble {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.08) 0%, rgba(0, 200, 255, 0.04) 100%);
            border: 1px solid rgba(0, 255, 255, 0.15);
            padding: 12px 16px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
            align-items: center;
            justify-content: center;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: rgba(0, 255, 255, 0.7);
            border-radius: 50%;
            animation: modernTypingBounce 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        .typing-dot:nth-child(3) { animation-delay: 0s; }

        @keyframes modernTypingBounce {
            0%, 80%, 100% { 
                transform: scale(0.8) translateY(0); 
                opacity: 0.5; 
            }
            40% { 
                transform: scale(1.2) translateY(-4px); 
                opacity: 1; 
            }
        }

        /* 
         * CHAT INPUT AREA CUSTOMIZATION - CHANGE HERE:
         * - Padding: 18px = comfortable, 15px = compact, 25px = spacious
         * - Colors: Change background and border colors
         */
        .modern-chat-input-area {
            padding: 18px;  /* CHANGE THIS: Space around input area */
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.04) 0%, rgba(0, 200, 255, 0.02) 100%);
            border-top: 1px solid rgba(0, 255, 255, 0.15);
        }

        .chat-input-container {
            position: relative;
        }

        .input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            background: rgba(0, 0, 0, 0.15);
            border: 1.5px solid rgba(0, 255, 255, 0.25);
            border-radius: 24px;  /* CHANGE THIS: Roundness of input */
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 
                0 2px 8px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
        }

        .input-wrapper:focus-within {
            border-color: rgba(0, 255, 255, 0.5);
            box-shadow: 
                0 0 15px rgba(0, 255, 255, 0.15),
                0 2px 12px rgba(0, 0, 0, 0.2);
            background: rgba(0, 0, 0, 0.1);
        }

        /* 
         * INPUT FIELD CUSTOMIZATION - CHANGE HERE:
         * - Padding: 15px 18px = comfortable, 12px 16px = compact
         * - Font size: 14px = perfect, 13px = smaller, 15px = larger
         */
        .modern-chat-input {
            flex: 1;
            background: transparent;
            border: none;
            padding: 15px 18px;  /* CHANGE THIS: Input padding */
            color: rgba(255, 255, 255, 0.95);
            font-size: 14px;     /* CHANGE THIS: Text size */
            outline: none;
            line-height: 1.4;
        }

        .modern-chat-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
            font-weight: 400;
        }

        /* 
         * SEND BUTTON CUSTOMIZATION - CHANGE HERE:
         * - Size: 42px = perfect, 38px = smaller, 46px = larger
         */
        .modern-send-button {
            width: 42px;   /* CHANGE THIS: Button size */
            height: 42px;  /* CHANGE THIS: Button size */
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.85) 0%, rgba(0, 200, 255, 1) 100%);
            border: none;
            border-radius: 50%;
            color: #041529;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 3px;
            transition: all 0.3s ease;
            box-shadow: 
                0 2px 8px rgba(0, 255, 255, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .modern-send-button:hover {
            transform: scale(1.05);
            box-shadow: 
                0 4px 15px rgba(0, 255, 255, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
        }

        .modern-send-button:active {
            transform: scale(0.98);
        }

        .modern-send-button svg {
            width: 16px;   /* CHANGE THIS: Icon size in send button */
            height: 16px;  /* CHANGE THIS: Icon size in send button */
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            .modern-chat-interface {
                /* MOBILE DIMENSIONS - Chat takes full screen on mobile */
                width: calc(100vw - 20px);  /* Full width minus padding */
                height: calc(100vh - 40px); /* Full height minus padding */
                right: 10px;
                bottom: 10px;
                border-radius: 15px;
            }
            
            .floating-chat-button {
                bottom: 20px;
                right: 20px;
                width: 50px;   /* Slightly larger on mobile for easier touch */
                height: 50px;  /* Slightly larger on mobile for easier touch */
            }

            .chat-button-icon {
                width: 22px;   /* Larger icon on mobile */
                height: 22px;  /* Larger icon on mobile */
            }
        }

        /* Search Widget Styles */
        .search-widget {
            /* === POSITION CONTROLS === */
            position: absolute;
            top: 12vh;                   /* CHANGE THIS: 12vh = higher, 20vh = lower */
            left: 6vh;                   /* CHANGE THIS: 6vh = left side, 50% = center */
            
            /* === SIZE CONTROLS === */
            /* REMOVED: min-width restriction - users can now resize to any size */
            /* min-width: 300px; */
            max-width: none;             /* REMOVED: max-width restriction - users can resize as large as they want */
            max-height: none;            /* REMOVED: max-height restriction - users can resize as tall as they want */
            padding: 16px 20px 20px 20px; /* INCREASED: More bottom padding for content spacing */
            
            /* === STYLING === */
            z-index: 25;
            background: linear-gradient(135deg, rgba(0, 200, 255, 0.2) 0%, rgba(0, 150, 255, 0.15) 100%);
            border: 1px solid rgba(0, 255, 255, 0.4);
            border-radius: 8px;          /* CHANGE THIS: 8px = rounded corners, 0px = sharp corners, 15px = very rounded */
            font-family: 'Orbitron', 'Segoe UI', monospace;
            color: white;
            box-shadow: 
                0 0 15px rgba(0, 255, 255, 0.3),
                inset 0 0 10px rgba(0, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            animation: searchGlow 3s ease-in-out infinite;
            display: none; /* Initially hidden */
        }

        /* Corner brackets for the widget */
        .search-widget::before,
        .search-widget::after {
            content: '';
            position: absolute;
            width: 12px;                 /* CHANGE THIS: 8px = smaller brackets, 16px = larger brackets */
            height: 12px;                /* CHANGE THIS: 8px = smaller brackets, 16px = larger brackets */
            border: 2px solid rgba(255, 255, 255, 1);
            z-index: 1;
        }

        /* Top-left corner */
        .search-widget::before {
            top: -4px;
            left: -4px;
            border-right: none;
            border-bottom: none;
        }

        /* Bottom-right corner */
        .search-widget::after {
            bottom: -4px;
            right: -4px;
            border-left: none;
            border-top: none;
        }

        /* Top-right corner */
        .search-widget .corner-top-right {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 12px;                 /* CHANGE THIS: match the size above */
            height: 12px;                /* CHANGE THIS: match the size above */
            border: 2px solid rgba(255, 255, 255, 1);
            border-left: none;
            border-bottom: none;
            z-index: 1;
        }

        /* Bottom-left corner */
        .search-widget .corner-bottom-left {
            position: absolute;
            bottom: -4px;
            left: -4px;
            width: 12px;                 /* CHANGE THIS: match the size above */
            height: 12px;                /* CHANGE THIS: match the size above */
            border: 2px solid rgba(255, 255, 255, 1);
            border-right: none;
            border-top: none;
            z-index: 1;
        }

        .search-label {
            font-size: 12px;             /* CHANGE THIS: 10px = smaller text, 14px = larger text */
            font-weight: 600;
            letter-spacing: 2px;
            color: white;
            margin-bottom: 8px;          /* CHANGE THIS: 6px = closer to content, 12px = further apart */
            text-transform: uppercase;
            position: relative;
            background: rgba(0, 150, 255, 0.8);
            padding: 3px 10px;           /* CHANGE THIS: 2px 8px = smaller label, 4px 12px = larger label */
            margin-left: -10px;          /* CHANGE THIS: adjust to match padding above */
            margin-right: -10px;         /* CHANGE THIS: adjust to match padding above */
            text-align: left;
        }

        .search-content {
            font-size: 13px;             /* CHANGE THIS: 11px = smaller text, 15px = larger text */
            font-weight: 400;
            letter-spacing: 0.5px;
            color: rgba(255, 255, 255, 0.95);
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
            margin-top: 6px;             /* CHANGE THIS: 4px = closer to label, 10px = further from label */
            position: relative;
            line-height: 1.4;            /* CHANGE THIS: 1.2 = tighter lines, 1.6 = looser lines */
            max-height: 200px;           /* INCREASED: More space for content to prevent overflow */
            overflow-y: auto;
            overflow-x: hidden;          /* ADDED: Prevent horizontal overflow */
            min-height: 60px;            /* INCREASED: Better minimum height for content area */
            padding-right: 12px;         /* INCREASED: More space for scrollbar */
            padding-left: 4px;           /* Left padding for better text positioning */
            padding-bottom: 8px;         /* ADDED: Bottom padding to prevent text cutoff */
            scroll-behavior: smooth;     /* Smooth scrolling */
            width: 100%;                 /* Full width */
            box-sizing: border-box;      /* Include padding in width calculation */
            word-wrap: break-word;       /* Break long words */
            overflow-wrap: break-word;   /* Modern word breaking */
            hyphens: auto;               /* ADDED: Enable automatic hyphenation for better text wrapping */
            white-space: pre-wrap;       /* ADDED: Preserve whitespace and enable wrapping */
            text-align: left;            /* Ensure left alignment */
            display: block;              /* Block display for full width */
        }

        /* Enhanced scrollbar for search content */
        .search-content::-webkit-scrollbar {
            width: var(--scrollbar-width, 10px); /* DYNAMIC: Uses scaled scrollbar width */
        }

        .search-content::-webkit-scrollbar-track {
            background: rgba(0, 150, 255, 0.15); /* INCREASED: More visible track */
            border-radius: 5px;
            margin: 4px 0;
            border: 1px solid rgba(0, 255, 255, 0.1); /* ADDED: Track border for better visibility */
        }

        .search-content::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, rgba(0, 200, 255, 0.9) 0%, rgba(0, 150, 255, 0.7) 100%); /* INCREASED: More opaque thumb */
            border-radius: 5px;
            border: 1px solid rgba(0, 255, 255, 0.4); /* INCREASED: More visible border */
            box-shadow: 0 0 6px rgba(0, 255, 255, 0.4); /* INCREASED: More prominent shadow */
            min-height: 20px;            /* ADDED: Minimum thumb height for better usability */
        }

        .search-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, rgba(0, 255, 255, 1) 0%, rgba(0, 200, 255, 0.9) 100%);
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.6); /* INCREASED: More prominent hover effect */
            border-color: rgba(0, 255, 255, 0.6); /* ADDED: Brighter border on hover */
        }

        .search-content::-webkit-scrollbar-thumb:active {
            background: linear-gradient(180deg, rgba(0, 255, 255, 1) 0%, rgba(0, 220, 255, 0.95) 100%);
            box-shadow: 0 0 12px rgba(0, 255, 255, 0.8); /* ADDED: Active state shadow */
        }

        /* Firefox scrollbar */
        .search-content {
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 200, 255, 0.8) rgba(0, 150, 255, 0.1);
        }

        /* White underline under the content */
        .search-content::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.8);
        }

        /* Ensure search widget content uses full width with size limits */
        .search-widget .search-content {
            margin-left: 0;
            margin-right: 0;
            width: 100% !important;
            max-width: none !important;
            /* Prevent text from becoming too large */
            max-font-size: 20px;
        }

        /* Ensure news widget content uses full width with size limits */
        .news-widget .news-content {
            margin-left: 0;
            margin-right: 0;
            width: 100% !important;
            max-width: none !important;
            /* Prevent text from becoming too large */
            max-font-size: 20px;
        }

        /* Better scaling behavior for large widgets */
        .widget-draggable[style*="width"] .search-content,
        .widget-draggable[style*="width"] .news-content {
            font-size: clamp(6px, calc(13px + 0.2vw), 20px) !important; /* REDUCED: Smaller minimum font size (6px) */
            line-height: 1.4 !important;
            /* REMOVED: Height restrictions - let content scale naturally */
            max-height: none !important;
            min-height: none !important;
        }

        /* ADDED: Enhanced scaling for resized widgets */
        .widget-draggable[style*="width"] {
            /* Ensure the widget container can accommodate larger content */
            min-height: fit-content;
        }

        .widget-draggable[style*="width"] .search-widget,
        .widget-draggable[style*="width"] .news-widget {
            /* Allow widget containers to grow with content */
            max-height: min(70vh, calc(60vh + 10px)) !important;
        }

        /* ADDED: Specific scaling for search widget container when manually resized */
        .search-widget.widget-draggable[style*="width"],
        .search-widget.widget-draggable[style*="height"] {
            /* Dynamic container height based on size */
            max-height: clamp(300px, calc(60vh + 5vw), 80vh) !important;
            /* Ensure proper content flow */
            overflow: visible;
            /* Better padding scaling - reduced minimums for tiny widgets */
            padding: clamp(4px, calc(16px + 0.5vw), 30px) clamp(6px, calc(20px + 0.8vw), 40px) clamp(4px, calc(20px + 0.5vw), 35px) clamp(6px, calc(20px + 0.8vw), 40px) !important;
        }

        /* Smooth transitions for all scalable elements */
        .widget-draggable * {
            transition: font-size 0.2s ease, padding 0.2s ease, margin 0.2s ease, 
                       width 0.2s ease, height 0.2s ease, line-height 0.2s ease !important;
        }

        /* Prevent transition during dragging for performance */
        .widget-dragging * {
            transition: none !important;
        }

        /* Enhanced scaling for all widget content */
        .widget-draggable .time-value,
        .widget-draggable .search-content,
        .widget-draggable .news-content,
        .widget-draggable .weather-temp,
        .widget-draggable .weather-condition,
        .widget-draggable .weather-feels-like {
            transform-origin: top left;
            will-change: font-size, padding, margin;
        }

        /* Ensure labels scale proportionally */
        .widget-draggable .time-label,
        .widget-draggable .weather-label,
        .widget-draggable .search-label,
        .widget-draggable .news-label {
            transform-origin: top left;
            will-change: font-size, padding, margin;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        @keyframes searchGlow {
            0%, 100% { 
                box-shadow: 
                    0 0 15px rgba(0, 255, 255, 0.3),
                    inset 0 0 10px rgba(0, 255, 255, 0.1);
            }
            50% { 
                box-shadow: 
                    0 0 25px rgba(0, 255, 255, 0.5),
                    inset 0 0 15px rgba(0, 255, 255, 0.2);
            }
        }

        .search-loading {
            display: none;
            font-size: 10px;
            color: rgba(255, 255, 255, 0.7);
            text-align: center;
            font-style: italic;
        }

        /* ================= NEWS WIDGET STYLES ================= */
        
        /* News Widget Styles */
        .news-widget {
            /* === POSITION CONTROLS === */
            position: absolute;
            top: 12vh;                   /* CHANGE THIS: 12vh = higher, 20vh = lower */
            right: 6vh;                  /* CHANGE THIS: 6vh = right side, 50% = center */
            
            /* === SIZE CONTROLS === */
            /* REMOVED: min-width restriction - users can now resize to any size */
            /* min-width: 300px; */
            max-width: none;             /* REMOVED: max-width restriction - users can resize as large as they want */
            padding: 16px 24px;          /* CHANGE THIS: 12px 20px = smaller, 20px 30px = larger */
            
            /* === STYLING === */
            z-index: 25;
            background: linear-gradient(135deg, rgba(255, 150, 0, 0.2) 0%, rgba(255, 100, 0, 0.15) 100%);
            border: 1px solid rgba(255, 165, 0, 0.4);
            border-radius: 8px;          /* CHANGE THIS: 8px = rounded corners, 0px = sharp corners, 15px = very rounded */
            font-family: 'Orbitron', 'Segoe UI', monospace;
            color: white;
            box-shadow: 
                0 0 15px rgba(255, 165, 0, 0.3),
                inset 0 0 10px rgba(255, 165, 0, 0.1);
            backdrop-filter: blur(5px);
            animation: newsGlow 3s ease-in-out infinite;
            display: none;               /* Initially hidden */
        }

        /* Corner brackets for the news widget */
        .news-widget::before,
        .news-widget::after {
            content: '';
            position: absolute;
            width: 12px;                 /* CHANGE THIS: 8px = smaller brackets, 16px = larger brackets */
            height: 12px;                /* CHANGE THIS: 8px = smaller brackets, 16px = larger brackets */
            border: 2px solid rgba(255, 255, 255, 1);
            z-index: 1;
        }

        /* Top-left corner */
        .news-widget::before {
            top: -4px;
            left: -4px;
            border-right: none;
            border-bottom: none;
        }

        /* Bottom-right corner */
        .news-widget::after {
            bottom: -4px;
            right: -4px;
            border-left: none;
            border-top: none;
        }

        /* Top-right corner */
        .news-widget .corner-top-right {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 12px;                 /* CHANGE THIS: match the size above */
            height: 12px;                /* CHANGE THIS: match the size above */
            border: 2px solid rgba(255, 255, 255, 1);
            border-left: none;
            border-bottom: none;
            z-index: 1;
        }

        /* Bottom-left corner */
        .news-widget .corner-bottom-left {
            position: absolute;
            bottom: -4px;
            left: -4px;
            width: 12px;                 /* CHANGE THIS: match the size above */
            height: 12px;                /* CHANGE THIS: match the size above */
            border: 2px solid rgba(255, 255, 255, 1);
            border-right: none;
            border-top: none;
            z-index: 1;
        }

        .news-label {
            font-size: 12px;             /* CHANGE THIS: 10px = smaller text, 14px = larger text */
            font-weight: 600;
            letter-spacing: 2px;
            color: white;
            margin-bottom: 8px;          /* CHANGE THIS: 6px = closer to content, 12px = further apart */
            text-transform: uppercase;
            position: relative;
            background: rgba(255, 140, 0, 0.8);
            padding: 3px 10px;           /* CHANGE THIS: 2px 8px = smaller label, 4px 12px = larger label */
            margin-left: -10px;          /* CHANGE THIS: adjust to match padding above */
            margin-right: -10px;         /* CHANGE THIS: adjust to match padding above */
            text-align: left;
        }

        .news-content {
            font-size: 13px;             /* CHANGE THIS: 11px = smaller text, 15px = larger text */
            font-weight: 400;
            letter-spacing: 0.5px;
            color: rgba(255, 255, 255, 0.95);
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
            margin-top: 6px;             /* CHANGE THIS: 4px = closer to label, 10px = further from label */
            position: relative;
            line-height: 1.4;            /* CHANGE THIS: 1.2 = tighter lines, 1.6 = looser lines */
            max-height: 120px;           /* CHANGE THIS: 80px = shorter content area, 160px = taller content area */
            overflow-y: auto;
            min-height: 40px;            /* CHANGE THIS: minimum height for content area */
            padding-right: 8px;          /* Space for scrollbar */
            padding-left: 4px;           /* Left padding for better text positioning */
            scroll-behavior: smooth;     /* Smooth scrolling */
            width: 100%;                 /* Full width */
            box-sizing: border-box;      /* Include padding in width calculation */
            word-wrap: break-word;       /* Break long words */
            overflow-wrap: break-word;   /* Modern word breaking */
            text-align: left;            /* Ensure left alignment */
            display: block;              /* Block display for full width */
        }

        /* Enhanced scrollbar for news content */
        .news-content::-webkit-scrollbar {
            width: 8px;
        }

        .news-content::-webkit-scrollbar-track {
            background: rgba(255, 140, 0, 0.1);
            border-radius: 4px;
            margin: 4px 0;
        }

        .news-content::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, rgba(255, 165, 0, 0.8) 0%, rgba(255, 140, 0, 0.6) 100%);
            border-radius: 4px;
            border: 1px solid rgba(255, 165, 0, 0.3);
            box-shadow: 0 0 4px rgba(255, 165, 0, 0.3);
        }

        .news-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, rgba(255, 165, 0, 1) 0%, rgba(255, 140, 0, 0.8) 100%);
            box-shadow: 0 0 8px rgba(255, 165, 0, 0.5);
        }

        .news-content::-webkit-scrollbar-thumb:active {
            background: linear-gradient(180deg, rgba(255, 165, 0, 1) 0%, rgba(255, 150, 0, 0.9) 100%);
        }

        /* Firefox scrollbar */
        .news-content {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 165, 0, 0.8) rgba(255, 140, 0, 0.1);
        }

        /* White underline under the news content */
        .news-content::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.8);
        }

        @keyframes newsGlow {
            0%, 100% {
                box-shadow:
                    0 0 15px rgba(255, 165, 0, 0.3),
                    inset 0 0 10px rgba(255, 165, 0, 0.1);
            }
            50% {
                box-shadow:
                    0 0 25px rgba(255, 165, 0, 0.5),
                    inset 0 0 15px rgba(255, 165, 0, 0.2);
            }
        }

        /* ================= NOTES WIDGET STYLES ================= */

        .notes-widget {
            position: absolute;
            top: 15vh;
            left: 50%;
            transform: translateX(-50%);
            width: 400px;
            max-width: 90vw;
            padding: 16px 20px 20px 20px;
            z-index: 25;
            background: linear-gradient(135deg, rgba(138, 43, 226, 0.2) 0%, rgba(75, 0, 130, 0.15) 100%);
            border: 1px solid rgba(138, 43, 226, 0.4);
            border-radius: 8px;
            font-family: 'Orbitron', 'Segoe UI', monospace;
            color: white;
            box-shadow:
                0 0 15px rgba(138, 43, 226, 0.3),
                inset 0 0 10px rgba(138, 43, 226, 0.1);
            backdrop-filter: blur(5px);
            animation: notesGlow 3s ease-in-out infinite;
            display: none;
        }

        /* Corner brackets for the notes widget */
        .notes-widget::before,
        .notes-widget::after {
            content: '';
            position: absolute;
            width: 12px;
            height: 12px;
            border: 2px solid rgba(255, 255, 255, 1);
            z-index: 1;
        }

        .notes-widget::before {
            top: -4px;
            left: -4px;
            border-right: none;
            border-bottom: none;
        }

        .notes-widget::after {
            bottom: -4px;
            right: -4px;
            border-left: none;
            border-top: none;
        }

        .notes-widget .corner-top-right {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 12px;
            height: 12px;
            border: 2px solid rgba(255, 255, 255, 1);
            border-left: none;
            border-bottom: none;
            z-index: 1;
        }

        .notes-widget .corner-bottom-left {
            position: absolute;
            bottom: -4px;
            left: -4px;
            width: 12px;
            height: 12px;
            border: 2px solid rgba(255, 255, 255, 1);
            border-right: none;
            border-top: none;
            z-index: 1;
        }

        .notes-label {
            font-size: 12px;
            font-weight: 600;
            letter-spacing: 2px;
            color: white;
            margin-bottom: 8px;
            text-transform: uppercase;
            position: relative;
            background: rgba(138, 43, 226, 0.8);
            padding: 3px 10px;
            margin-left: -10px;
            margin-right: -10px;
            text-align: left;
        }

        .notes-content {
            position: relative;
            width: 100%;
        }

        .notes-editor {
            width: 100%;
        }

        .note-textarea {
            width: 100%;
            height: 200px;
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(138, 43, 226, 0.3);
            border-radius: 6px;
            padding: 12px;
            color: rgba(255, 255, 255, 0.95);
            font-size: 13px;
            font-family: 'Segoe UI', system-ui, sans-serif;
            line-height: 1.4;
            resize: vertical;
            outline: none;
            box-sizing: border-box;
            pointer-events: auto;
            user-select: text;
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
            z-index: 100;
            position: relative;
        }

        .note-textarea::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .note-textarea:focus {
            border-color: rgba(138, 43, 226, 0.6);
            box-shadow: 0 0 10px rgba(138, 43, 226, 0.2);
        }

        .notes-controls {
            display: flex;
            gap: 8px;
            margin-top: 12px;
            justify-content: flex-start;
        }

        .notes-btn {
            background: linear-gradient(135deg, rgba(138, 43, 226, 0.6) 0%, rgba(75, 0, 130, 0.8) 100%);
            border: 1px solid rgba(138, 43, 226, 0.4);
            border-radius: 4px;
            color: white;
            padding: 6px 12px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .notes-btn:hover {
            background: linear-gradient(135deg, rgba(138, 43, 226, 0.8) 0%, rgba(75, 0, 130, 1) 100%);
            box-shadow: 0 0 8px rgba(138, 43, 226, 0.4);
            transform: translateY(-1px);
        }

        .notes-btn:active {
            transform: translateY(0);
        }

        .saved-notes-list {
            width: 100%;
            max-height: 300px;
            overflow-y: auto;
        }

        .notes-list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid rgba(138, 43, 226, 0.3);
        }

        .notes-list-header h3 {
            margin: 0;
            font-size: 14px;
            color: white;
            font-weight: 600;
        }

        .notes-list-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .note-item {
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(138, 43, 226, 0.3);
            border-radius: 4px;
            padding: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .note-item:hover {
            background: rgba(138, 43, 226, 0.1);
            border-color: rgba(138, 43, 226, 0.5);
            transform: translateY(-1px);
        }

        .note-preview {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.3;
            margin-bottom: 4px;
            max-height: 40px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .note-meta {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.6);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .note-delete {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid rgba(255, 0, 0, 0.4);
            border-radius: 3px;
            color: rgba(255, 255, 255, 0.8);
            padding: 2px 6px;
            font-size: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .note-delete:hover {
            background: rgba(255, 0, 0, 0.4);
            color: white;
        }

        .no-notes-message {
            text-align: center;
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            padding: 20px;
            font-style: italic;
        }

        @keyframes notesGlow {
            0%, 100% {
                box-shadow:
                    0 0 15px rgba(138, 43, 226, 0.3),
                    inset 0 0 10px rgba(138, 43, 226, 0.1);
            }
            50% {
                box-shadow:
                    0 0 25px rgba(138, 43, 226, 0.5),
                    inset 0 0 15px rgba(138, 43, 226, 0.2);
            }
        }

        /* ================= ENHANCED NEWS WIDGET STYLES ================= */
        
        /* Header Section */
        .news-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px 10px 20px;
            border-bottom: 1px solid rgba(255, 165, 0, 0.3);
            position: relative;
        }

        .news-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .news-logo {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #FF9500 0%, #FF6500 100%);
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 900;
            font-size: 12px;
            color: white;
            text-shadow: 0 0 6px rgba(255, 255, 255, 0.8);
        }

        .news-brand {
            font-size: 14px;
            font-weight: 700;
            letter-spacing: 1.5px;
            color: white;
            text-transform: uppercase;
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
        }

        .news-status {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 9px;
            font-weight: 500;
            letter-spacing: 1px;
            text-transform: uppercase;
        }

        .news-time-display {
            font-size: 11px;
            font-weight: 600;
            color: #00FF88;
            text-shadow: 0 0 8px #00FF88;
            font-family: 'Orbitron', monospace;
            margin-right: 8px;
            letter-spacing: 0.5px;
        }

        .status-indicator {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #00FF88;
            animation: pulse 2s infinite;
            box-shadow: 0 0 6px #00FF88;
        }

        /* Navigation Tabs */
        .news-nav {
            display: flex;
            padding: 0 20px;
            border-bottom: 1px solid rgba(255, 165, 0, 0.2);
            background: rgba(255, 165, 0, 0.05);
        }

        .nav-tab {
            padding: 10px 14px;
            font-size: 10px;
            font-weight: 500;
            letter-spacing: 1px;
            text-transform: uppercase;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
            color: rgba(255, 255, 255, 0.7);
        }

        .nav-tab:hover {
            color: rgba(255, 165, 0, 0.9);
            background: rgba(255, 165, 0, 0.1);
        }

        .nav-tab.active {
            color: #FF9500;
            border-bottom-color: #FF9500;
            background: rgba(255, 165, 0, 0.1);
            text-shadow: 0 0 6px rgba(255, 149, 0, 0.6);
        }

        /* Content Area */
        .news-content-area {
            padding: 16px 20px;
            min-height: 160px;
            max-height: 240px;
            overflow-y: auto;
        }

        .news-item {
            margin-bottom: 14px;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(255, 165, 0, 0.2);
            animation: slideIn 0.5s ease-out;
        }

        .news-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .news-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 9px;
            color: rgba(255, 255, 255, 0.7);
            text-transform: uppercase;
            letter-spacing: 1.2px;
        }

        .news-category {
            background: linear-gradient(135deg, rgba(255, 165, 0, 0.4) 0%, rgba(255, 100, 0, 0.3) 100%);
            padding: 3px 10px;
            border-radius: 12px;
            font-weight: 700;
            font-size: 8px;
            border: 1px solid rgba(255, 165, 0, 0.5);
            text-shadow: 0 0 4px rgba(255, 165, 0, 0.8);
            color: #FFF;
        }

        .news-time {
            font-weight: 500;
            font-size: 9px;
            color: rgba(0, 255, 136, 0.8);
            text-shadow: 0 0 3px rgba(0, 255, 136, 0.4);
        }

        .news-headline {
            font-size: 13px;
            font-weight: 600;
            line-height: 1.3;
            color: rgba(255, 255, 255, 1);
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.4);
            margin-bottom: 6px;
            letter-spacing: 0.4px;
            font-family: 'Orbitron', monospace;
        }

        .news-summary {
            font-size: 11px;
            line-height: 1.5;
            color: rgba(255, 255, 255, 0.85);
            font-weight: 400;
            letter-spacing: 0.2px;
            text-shadow: 0 0 3px rgba(255, 255, 255, 0.2);
        }

        /* Loading State */
        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 165, 0, 0.3);
            border-top: 2px solid #FF9500;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px auto;
        }

        /* Footer Controls */
        .news-footer {
            padding: 10px 20px;
            border-top: 1px solid rgba(255, 165, 0, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255, 165, 0, 0.05);
        }

        .news-controls {
            display: flex;
            gap: 6px;
        }

        .control-btn {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, rgba(255, 150, 0, 0.3) 0%, rgba(255, 100, 0, 0.2) 100%);
            border: 1px solid rgba(255, 165, 0, 0.4);
            border-radius: 3px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            transition: all 0.3s ease;
            font-family: 'Orbitron', monospace;
        }

        .control-btn:hover {
            background: linear-gradient(135deg, rgba(255, 150, 0, 0.5) 0%, rgba(255, 100, 0, 0.3) 100%);
            box-shadow: 0 0 8px rgba(255, 165, 0, 0.4);
            transform: translateY(-1px);
        }

        .news-count {
            font-size: 9px;
            color: rgba(255, 255, 255, 0.6);
            font-weight: 500;
        }

        /* Enhanced scrollbar for news content area */
        .news-content-area::-webkit-scrollbar {
            width: 5px;
        }

        .news-content-area::-webkit-scrollbar-track {
            background: rgba(255, 165, 0, 0.1);
            border-radius: 2px;
        }

        .news-content-area::-webkit-scrollbar-thumb {
            background: rgba(255, 165, 0, 0.5);
            border-radius: 2px;
        }

        .news-content-area::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 165, 0, 0.7);
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(8px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .news-loading {
            display: none;
            font-size: 10px;
            color: rgba(255, 255, 255, 0.7);
            text-align: center;
            font-style: italic;
        }

        /* ================= CALCULATOR WIDGET STYLES ================= */
        
        /* Calculator Widget Styles */
        .calculator-widget {
            /* === POSITION CONTROLS === */
            position: absolute;
            top: 12vh;                   /* CHANGE THIS: 12vh = higher, 20vh = lower */
            left: 50%;                   /* CHANGE THIS: Center horizontally */
            transform: translateX(-50%); /* Center the widget */
            
            /* === SIZE CONTROLS === */
            padding: 16px 20px 20px 20px;
            
            /* === STYLING === */
            z-index: 25;
            background: linear-gradient(135deg, rgba(0, 150, 255, 0.3) 0%, rgba(0, 120, 255, 0.2) 100%);
            border: 2px solid rgba(0, 255, 255, 0.5);
            border-radius: 12px;
            color: white;
            box-shadow: 
                0 0 20px rgba(0, 255, 255, 0.4),
                inset 0 0 15px rgba(0, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            animation: calculatorGlow 3s ease-in-out infinite;
            display: none; /* Initially hidden */
            width: 550px;
            max-width: 90vw;
        }

        /* Corner brackets for the calculator widget */
        .calculator-widget::before,
        .calculator-widget::after {
            content: '';
            position: absolute;
            width: 12px;
            height: 12px;
            border: 2px solid rgba(255, 255, 255, 1);
            z-index: 1;
        }

        /* Top-left corner */
        .calculator-widget::before {
            top: -4px;
            left: -4px;
            border-right: none;
            border-bottom: none;
        }

        /* Bottom-right corner */
        .calculator-widget::after {
            bottom: -4px;
            right: -4px;
            border-left: none;
            border-top: none;
        }

        /* Top-right corner */
        .calculator-widget .corner-top-right {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 12px;
            height: 12px;
            border: 2px solid rgba(255, 255, 255, 1);
            border-left: none;
            border-bottom: none;
            z-index: 1;
        }

        /* Bottom-left corner */
        .calculator-widget .corner-bottom-left {
            position: absolute;
            bottom: -4px;
            left: -4px;
            width: 12px;
            height: 12px;
            border: 2px solid rgba(255, 255, 255, 1);
            border-right: none;
            border-top: none;
            z-index: 1;
        }

        .calculator-label {
            background: linear-gradient(135deg, rgba(0, 150, 255, 0.8) 0%, rgba(0, 120, 255, 0.9) 100%);
            color: white;
            padding: 8px 16px;
            margin: -16px -20px 15px -20px;
            border-radius: 8px 8px 0 0;
            font-weight: bold;
            font-size: 14px;
            letter-spacing: 2px;
            text-align: center;
            position: relative;
        }

        .calculator-screen-area {
            background: rgba(240, 240, 240, 0.95);
            border: 2px solid rgba(0, 150, 255, 0.4);
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 15px;
            min-height: 80px;
            color: #333;
        }

        .calculator-input {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
            min-height: 20px;
        }

        .calculator-result {
            font-family: 'Courier New', monospace;
            font-size: 20px;
            font-weight: bold;
            color: #000;
            text-align: right;
            min-height: 30px;
            border-top: 1px solid rgba(0, 150, 255, 0.3);
            padding-top: 8px;
        }

        .controls-section {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .calculator-tabs {
            display: flex;
            margin-bottom: 10px;
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid rgba(0, 150, 255, 0.3);
        }

        .tab-button {
            flex: 1;
            background: rgba(100, 100, 100, 0.3);
            border: none;
            color: rgba(255, 255, 255, 0.7);
            padding: 10px 12px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .tab-button.active {
            background: linear-gradient(135deg, rgba(0, 150, 255, 0.6) 0%, rgba(0, 120, 255, 0.7) 100%);
            color: white;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .angle-mode {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-top: 10px;
        }

        .angle-button {
            background: rgba(100, 100, 100, 0.3);
            border: 1px solid rgba(0, 150, 255, 0.4);
            color: rgba(255, 255, 255, 0.7);
            padding: 8px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .angle-button.active {
            background: linear-gradient(135deg, rgba(0, 150, 255, 0.6) 0%, rgba(0, 120, 255, 0.7) 100%);
            color: white;
            border-color: rgba(0, 200, 255, 0.6);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .button-section {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 8px;
            padding: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .calculator-buttons {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 8px;
        }

        .calc-btn {
            background: linear-gradient(135deg, rgba(200, 200, 200, 0.9) 0%, rgba(180, 180, 180, 0.9) 100%);
            border: 1px solid rgba(150, 150, 150, 0.5);
            border-radius: 6px;
            color: #333;
            font-family: 'Arial', sans-serif;
            font-size: 13px;
            font-weight: 600;
            padding: 12px 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .calc-btn:hover {
            background: linear-gradient(135deg, rgba(220, 220, 220, 0.9) 0%, rgba(200, 200, 200, 0.9) 100%);
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
        }

        .calc-btn:active {
            transform: translateY(0);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .calc-btn.operator {
            background: linear-gradient(135deg, rgba(255, 150, 50, 0.9) 0%, rgba(255, 120, 20, 0.9) 100%);
            color: white;
            border-color: rgba(255, 100, 0, 0.7);
        }

        .calc-btn.scientific {
            background: linear-gradient(135deg, rgba(100, 150, 255, 0.9) 0%, rgba(80, 120, 255, 0.9) 100%);
            color: white;
            font-size: 11px;
            border-color: rgba(50, 100, 255, 0.7);
        }

        .calc-btn.equals {
            background: linear-gradient(135deg, rgba(0, 200, 100, 0.9) 0%, rgba(0, 170, 80, 0.9) 100%);
            color: white;
            border-color: rgba(0, 150, 70, 0.7);
            font-size: 16px;
            font-weight: bold;
        }

        .calc-btn.zero {
            grid-column: span 2;
        }

        .calc-btn.clear {
            background: linear-gradient(135deg, rgba(255, 80, 80, 0.9) 0%, rgba(255, 50, 50, 0.9) 100%);
            color: white;
            border-color: rgba(255, 30, 30, 0.7);
        }

        .calc-btn.ans {
            background: linear-gradient(135deg, rgba(150, 100, 255, 0.9) 0%, rgba(120, 80, 255, 0.9) 100%);
            color: white;
            font-size: 11px;
            border-color: rgba(100, 50, 255, 0.7);
            text-transform: uppercase;
        }

        .calc-btn.backspace {
            background: linear-gradient(135deg, rgba(255, 180, 50, 0.9) 0%, rgba(255, 150, 20, 0.9) 100%);
            color: white;
            border-color: rgba(255, 130, 0, 0.7);
            font-size: 16px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .error {
            color: #ff4444 !important;
        }

        /* Special layout for main tab */
        .main-buttons {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 8px;
        }

        .main-buttons .calc-btn.equals {
            grid-column: span 4;
        }

        .main-buttons .calc-btn.zero {
            grid-column: span 2;
        }

        /* ABC tab layout */
        .abc-buttons {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 8px;
        }

        /* Function tab layout */
        .func-buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
        }

        @keyframes calculatorGlow {
            0%, 100% { 
                box-shadow: 
                    0 0 15px rgba(0, 150, 255, 0.3),
                    inset 0 0 10px rgba(0, 150, 255, 0.1);
            }
            50% { 
                box-shadow: 
                    0 0 25px rgba(0, 150, 255, 0.5),
                    inset 0 0 15px rgba(0, 150, 255, 0.2);
            }
        }

        .calculator-loading {
            display: none;
            font-size: 10px;
            color: rgba(255, 255, 255, 0.7);
            text-align: center;
            font-style: italic;
        }

        /* ================= NOTEPAD WIDGET STYLES ================= */
        .notepad-widget {
            /* === POSITION CONTROLS === */
            position: absolute;
            top: 12vh;
            left: 70%;
            transform: translateX(-50%);
            
            /* === SIZE CONTROLS === */
            width: 400px;
            height: 500px;
            
            /* === STYLING === */
            z-index: 25;
            background: rgba(0, 255, 255, 0.05);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 15px;
            font-family: 'Orbitron', 'Segoe UI', monospace;
            color: white;
            box-shadow: 
                0 0 20px rgba(0, 255, 255, 0.2),
                inset 0 0 15px rgba(0, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            animation: notepadGlow 4s ease-in-out infinite alternate;
            display: flex;
            flex-direction: column;
            user-select: none;
        }

        /* Corner brackets for the notepad widget */
        .notepad-widget::before,
        .notepad-widget::after {
            content: '';
            position: absolute;
            width: 15px;
            height: 15px;
            border: 2px solid rgba(0, 255, 255, 0.8);
            z-index: 1;
        }

        /* Top-left corner */
        .notepad-widget::before {
            top: -4px;
            left: -4px;
            border-right: none;
            border-bottom: none;
        }

        /* Bottom-right corner */
        .notepad-widget::after {
            bottom: -4px;
            right: -4px;
            border-left: none;
            border-top: none;
        }

        /* Top-right corner */
        .notepad-widget .corner-top-right {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 15px;
            height: 15px;
            border: 2px solid rgba(0, 255, 255, 0.8);
            border-left: none;
            border-bottom: none;
            z-index: 1;
        }

        /* Bottom-left corner */
        .notepad-widget .corner-bottom-left {
            position: absolute;
            bottom: -4px;
            left: -4px;
            width: 15px;
            height: 15px;
            border: 2px solid rgba(0, 255, 255, 0.8);
            border-right: none;
            border-top: none;
            z-index: 1;
        }

        .notepad-label {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.3) 0%, rgba(0, 200, 255, 0.4) 100%);
            color: white;
            padding: 8px 16px;
            margin: -1px -1px 0 -1px;
            border-radius: 15px 15px 0 0;
            font-weight: bold;
            font-size: 12px;
            letter-spacing: 2px;
            text-align: center;
            position: relative;
            cursor: move;
            border-bottom: 1px solid rgba(0, 255, 255, 0.2);
        }

        .notepad-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: rgba(0, 255, 255, 0.05);
            border-bottom: 1px solid rgba(0, 255, 255, 0.2);
        }

        .notepad-title {
            font-size: 12px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.8);
            letter-spacing: 1px;
        }

        .notepad-controls {
            display: flex;
            gap: 6px;
        }

        .notepad-btn {
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 6px;
            color: rgba(255, 255, 255, 0.8);
            width: 26px;
            height: 26px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 12px;
        }

        .notepad-btn:hover {
            background: rgba(0, 255, 255, 0.2);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 255, 255, 0.2);
        }

        .notepad-btn:active {
            transform: translateY(1px);
        }

        .notepad-content-area {
            flex: 1;
            padding: 12px;
            overflow: hidden;
            background: rgba(0, 255, 255, 0.02);
            border-bottom: 1px solid rgba(0, 255, 255, 0.1);
        }

        #notepadTextarea {
            width: 100%;
            height: 100%;
            background: rgba(0, 255, 255, 0.03);
            border: 1px solid rgba(0, 255, 255, 0.2);
            border-radius: 8px;
            color: rgba(255, 255, 255, 0.9);
            font-family: 'Segoe UI', system-ui, sans-serif;
            font-size: 13px;
            line-height: 1.4;
            padding: 12px;
            resize: none;
            outline: none;
            box-sizing: border-box;
            transition: all 0.3s ease;
        }

        #notepadTextarea:focus {
            border-color: rgba(0, 255, 255, 0.5);
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.2);
            background: rgba(0, 255, 255, 0.05);
        }

        #notepadTextarea::placeholder {
            color: rgba(255, 255, 255, 0.4);
        }

        .notepad-status-bar {
            display: flex;
            justify-content: space-between;
            padding: 6px 12px;
            background: rgba(0, 255, 255, 0.05);
            font-size: 10px;
            color: rgba(255, 255, 255, 0.6);
            border-radius: 0 0 15px 15px;
        }

        @keyframes notepadGlow {
            0% { 
                box-shadow: 
                    0 0 15px rgba(0, 255, 255, 0.15),
                    inset 0 0 10px rgba(0, 255, 255, 0.03);
            }
            100% { 
                box-shadow: 
                    0 0 25px rgba(0, 255, 255, 0.25),
                    inset 0 0 15px rgba(0, 255, 255, 0.08);
            }
        }

        /* Widget size variants for notepad */
        .notepad-widget.widget-size-small {
            width: 320px;
            height: 400px;
        }

        .notepad-widget.widget-size-normal {
            width: 400px;
            height: 500px;
        }

        .notepad-widget.widget-size-large {
            width: 500px;
            height: 600px;
        }

        .notepad-widget.widget-size-xlarge {
            width: 600px;
            height: 700px;
        }

        /* ================= DRAGGABLE & RESIZABLE WIDGET STYLES ================= */
        
        .widget-draggable {
            cursor: grab;
            user-select: none;
            transition: transform 0.2s ease, box-shadow 0.2s ease, opacity 0.2s ease;
        }

        .widget-draggable:active {
            cursor: grabbing;
        }

        .widget-draggable:hover:not(.widget-dragging) {
            /* REMOVED: No automatic size scaling on hover - only manual resize allowed */
            /* transform: scale(1.01); */
            filter: brightness(1.05);
            transition: filter 0.2s ease;
        }

        /* ================= RESIZE HANDLE STYLES ================= */
        
        .resize-handle {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 24px;
            height: 24px;
            background: 
                linear-gradient(135deg, transparent 50%, rgba(0, 255, 255, 0.4) 50%),
                linear-gradient(45deg, transparent 50%, rgba(0, 255, 255, 0.4) 50%);
            background-size: 8px 8px;
            background-position: 0 0, 4px 4px;
            cursor: se-resize;
            z-index: 100;
            border-bottom-right-radius: 15px;
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }

        .resize-handle:hover {
            opacity: 1;
            background: 
                linear-gradient(135deg, transparent 50%, rgba(0, 255, 255, 0.7) 50%),
                linear-gradient(45deg, transparent 50%, rgba(0, 255, 255, 0.7) 50%);
            background-size: 8px 8px;
            background-position: 0 0, 4px 4px;
            transform: scale(1.1);
        }

        /* AI Movement Animation Class */
        .widget-ai-moving {
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1) !important;
            transform: scale(1.05) !important;
            filter: brightness(1.2) drop-shadow(0 0 20px rgba(0, 255, 255, 0.5)) !important;
            z-index: 999 !important;
        }

        .widget-dragging {
            opacity: 0.95;
            z-index: 1000 !important;
            transition: opacity 0.1s ease !important;
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4) !important;
            filter: brightness(1.05);
            cursor: grabbing !important;
            /* No transform scaling to prevent size changes during drag */
        }

        /* Enhanced dragging for specific widgets */
        .time-display.widget-dragging {
            box-shadow: 0 8px 30px rgba(0, 255, 255, 0.4) !important;
            border-color: rgba(0, 255, 255, 0.6) !important;
        }

        .weather-display.widget-dragging {
            box-shadow: 0 8px 30px rgba(0, 255, 255, 0.3) !important;
            border-color: rgba(0, 255, 255, 0.5) !important;
        }

        .search-widget.widget-dragging {
            box-shadow: 0 8px 30px rgba(0, 200, 255, 0.4) !important;
            border-color: rgba(0, 255, 255, 0.6) !important;
        }

        .news-widget.widget-dragging {
            box-shadow: 0 8px 30px rgba(255, 165, 0, 0.4) !important;
            border-color: rgba(255, 165, 0, 0.6) !important;
        }

        .widget-controls {
            position: absolute;
            top: -8px;
            right: -8px;
            display: none;
            gap: 6px;
            z-index: 10;
            padding: 4px;
            background: rgba(12, 41, 79, 0.3);
            border-radius: 8px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 255, 255, 0.2);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
        }

        .widget-draggable:hover .widget-controls {
            display: flex;
            animation: controlsSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes controlsSlideIn {
            from {
                opacity: 0;
                transform: translateY(-10px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .widget-control-btn {
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, rgba(12, 41, 79, 0.9) 0%, rgba(6, 29, 59, 0.95) 100%);
            border: 1px solid rgba(0, 255, 255, 0.4);
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: rgba(0, 255, 255, 0.9);
            font-weight: bold;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            user-select: none;
            position: relative;
            backdrop-filter: blur(8px);
            box-shadow: 
                0 2px 8px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .widget-control-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, 
                rgba(0, 255, 255, 0.1) 0%, 
                transparent 50%, 
                rgba(0, 255, 255, 0.1) 100%);
            border-radius: 5px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .widget-control-btn:hover {
            transform: scale(1.1) translateY(-1px);
            border-color: rgba(0, 255, 255, 0.8);
            box-shadow: 
                0 4px 15px rgba(0, 255, 255, 0.3),
                0 0 20px rgba(0, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .widget-control-btn:hover::before {
            opacity: 1;
        }

        .widget-control-btn:active {
            transform: scale(0.95) translateY(0px);
        }

        /* FUTURISTIC EXPAND BUTTON */
        .widget-control-btn[title="Make Bigger"] {
            background: linear-gradient(135deg, rgba(0, 100, 50, 0.9) 0%, rgba(0, 150, 75, 0.95) 100%);
            border-color: rgba(0, 255, 150, 0.6);
            color: rgba(0, 255, 150, 1);
        }

        .widget-control-btn[title="Make Bigger"]:hover {
            border-color: rgba(0, 255, 150, 1);
            box-shadow: 
                0 4px 15px rgba(0, 255, 150, 0.4),
                0 0 25px rgba(0, 255, 150, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .widget-control-btn[title="Make Bigger"]::before {
            background: linear-gradient(45deg, 
                rgba(0, 255, 150, 0.2) 0%, 
                transparent 50%, 
                rgba(0, 255, 150, 0.2) 100%);
        }

        /* FUTURISTIC COMPRESS BUTTON */
        .widget-control-btn[title="Make Smaller"] {
            background: linear-gradient(135deg, rgba(100, 50, 0, 0.9) 0%, rgba(150, 75, 0, 0.95) 100%);
            border-color: rgba(255, 150, 0, 0.6);
            color: rgba(255, 180, 0, 1);
        }

        .widget-control-btn[title="Make Smaller"]:hover {
            border-color: rgba(255, 150, 0, 1);
            box-shadow: 
                0 4px 15px rgba(255, 150, 0, 0.4),
                0 0 25px rgba(255, 150, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .widget-control-btn[title="Make Smaller"]::before {
            background: linear-gradient(45deg, 
                rgba(255, 150, 0, 0.2) 0%, 
                transparent 50%, 
                rgba(255, 150, 0, 0.2) 100%);
        }

        /* FUTURISTIC TERMINATE BUTTON */
        .widget-control-btn[title="Close"] {
            background: linear-gradient(135deg, rgba(100, 20, 20, 0.9) 0%, rgba(150, 30, 30, 0.95) 100%);
            border-color: rgba(255, 80, 80, 0.6);
            color: rgba(255, 100, 100, 1);
        }

        .widget-control-btn[title="Close"]:hover {
            border-color: rgba(255, 80, 80, 1);
            box-shadow: 
                0 4px 15px rgba(255, 80, 80, 0.4),
                0 0 25px rgba(255, 80, 80, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .widget-control-btn[title="Close"]::before {
            background: linear-gradient(45deg, 
                rgba(255, 80, 80, 0.2) 0%, 
                transparent 50%, 
                rgba(255, 80, 80, 0.2) 100%);
        }

        .resize-handle {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.9) 0%, rgba(0, 200, 255, 1) 100%);
            border: 2px solid rgba(0, 255, 255, 0.8);
            border-radius: 4px;
            cursor: se-resize;
            display: none;
            z-index: 1001;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.6);
            transition: all 0.2s ease;
        }

        .widget-draggable:hover .resize-handle {
            display: block;
        }

        .resize-handle:hover {
            transform: scale(1.2);
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.8);
            background: linear-gradient(135deg, rgba(0, 255, 255, 1) 0%, rgba(0, 220, 255, 1) 100%);
            cursor: se-resize;
        }

        .resize-handle::before {
            content: '⟲';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 10px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: bold;
        }

        .widget-resizing {
            transition: none !important;
        }

        /* Widget size classes - scales EVERYTHING including text */
        .widget-size-small {
            transform: scale(0.8);
            transform-origin: top left;
        }

        .widget-size-normal {
            transform: scale(1);
            transform-origin: top left;
        }

        .widget-size-large {
            transform: scale(1.2);
            transform-origin: top left;
        }

        .widget-size-xlarge {
            transform: scale(1.4);
            transform-origin: top left;
        }

        /* Additional text scaling for manual resize */
        .widget-text-scale-small {
            font-size: 0.8em;
        }

        .widget-text-scale-normal {
            font-size: 1em;
        }

        .widget-text-scale-large {
            font-size: 1.2em;
        }

        .widget-text-scale-xlarge {
            font-size: 1.4em;
        }

        /* Responsive text scaling for manual resize */
        .widget-draggable[style*="width"] .time-label,
        .widget-draggable[style*="width"] .weather-label,
        .widget-draggable[style*="width"] .search-label {
            font-size: calc(12px + 0.5vw);
        }

        .widget-draggable[style*="width"] .time-value {
            font-size: calc(18px + 1vw);
        }

        .widget-draggable[style*="width"] .search-content {
            font-size: calc(13px + 0.3vw);
            line-height: 1.4;
            /* ADDED: Responsive height scaling for manual resize */
            max-height: calc(200px + 8vw) !important;
            min-height: calc(60px + 2vw) !important;
            /* ADDED: Responsive padding scaling */
            padding-right: calc(12px + 0.5vw) !important;
            padding-bottom: calc(8px + 0.3vw) !important;
        }

        .widget-draggable[style*="width"] .weather-temp {
            font-size: calc(24px + 1vw);
        }

        .widget-draggable[style*="width"] .weather-condition,
        .widget-draggable[style*="width"] .weather-feels-like {
            font-size: calc(11px + 0.3vw);
        }

        .widget-draggable[style*="width"] .weather-details {
            font-size: calc(10px + 0.2vw);
        }

        /* ================= ENHANCED WIDGET STYLING WITH IMAGES ================= */
        
        /* Enhanced styling for news articles with images */
        .news-item {
            margin-bottom: 16px;
            padding: 12px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .news-item:hover {
            background: rgba(255, 255, 255, 0.04);
            border-color: rgba(255, 165, 0, 0.3);
            transform: translateY(-1px);
        }

        .news-item img {
            width: 100% !important;
            max-width: 100% !important;
            height: auto !important;
            max-height: 180px !important;
            object-fit: cover !important;
            border-radius: 6px !important;
            margin: 10px 0 !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
            transition: transform 0.3s ease !important;
        }

        .news-item img:hover {
            transform: scale(1.02) !important;
        }

        .news-headline {
            font-size: 14px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.95);
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .news-meta {
            font-size: 11px;
            color: rgba(255, 165, 0, 0.8);
            margin-bottom: 6px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .news-summary {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.85);
            line-height: 1.4;
            margin-top: 8px;
        }

        .news-summary p {
            margin-bottom: 8px;
        }

        .news-summary p:last-child {
            margin-bottom: 0;
        }

        /* Enhanced styling for search widget content with images */
        .search-content img {
            width: 100% !important;
            max-width: 100% !important;
            height: auto !important;
            max-height: 200px !important;
            object-fit: cover !important;
            border-radius: 6px !important;
            margin: 12px 0 !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
            transition: transform 0.3s ease !important;
        }

        .search-content img:hover {
            transform: scale(1.02) !important;
        }

        .search-content > div {
            padding: 8px;
        }

        .search-content > div > div:last-child {
            margin-top: 8px;
            font-size: 13px;
            line-height: 1.4;
            color: rgba(255, 255, 255, 0.9);
        }

        /* Time Display Styles */
        .time-display {
            position: absolute;
            top: 12vh;
            right: 6vh;
            z-index: 15;
            background: linear-gradient(135deg, rgba(0, 200, 255, 0.2) 0%, rgba(0, 150, 255, 0.15) 100%);
            border: 1px solid rgba(0, 255, 255, 0.4);
            border-radius: 12px;
            padding: 18px 28px;
            font-family: 'Orbitron', 'Segoe UI', monospace;
            color: white;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.4), inset 0 0 15px rgba(0, 255, 255, 0.15);
            backdrop-filter: blur(8px);
            animation: timeGlow 3s ease-in-out infinite;
            opacity: 1;
            transform: scale(1) translateY(0);
            min-width: 160px;
        }

        /* Corner brackets - straight angular corners */
        .time-display::before,
        .time-display::after {
            content: '';
            position: absolute;
            width: 10px;
            height: 10px;
            border: 2px solid rgba(255, 255, 255, 1);
            z-index: 1;
        }

        /* Top-left corner */
        .time-display::before {
            top: -4px;
            left: -4px;
            border-right: none;
            border-bottom: none;
        }

        /* Bottom-right corner */
        .time-display::after {
            bottom: -4px;
            right: -4px;
            border-left: none;
            border-top: none;
        }

        /* Top-right corner */
        .time-display .corner-top-right {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 10px;
            height: 10px;
            border: 2px solid rgba(255, 255, 255, 1);
            border-left: none;
            border-bottom: none;
            z-index: 1;
        }

        /* Bottom-left corner */
        .time-display .corner-bottom-left {
            position: absolute;
            bottom: -4px;
            left: -4px;
            width: 10px;
            height: 10px;
            border: 2px solid rgba(255, 255, 255, 1);
            border-right: none;
            border-top: none;
            z-index: 1;
        }

        .time-label {
            font-size: 14px;
            font-weight: 600;
            letter-spacing: 3px;
            color: white;
            margin-bottom: 8px;
            text-transform: uppercase;
            position: relative;
            background: rgba(0, 150, 255, 0.8);
            padding: 4px 12px;
            margin-left: -12px;
            margin-right: -12px;
            text-align: center;
        }

        .time-value {
            font-size: 24px;
            font-weight: 700;
            letter-spacing: 2px;
            color: rgba(255, 255, 255, 0.95);
            text-shadow: 0 0 12px rgba(255, 255, 255, 0.6);
            margin-top: 6px;
            position: relative;
            text-align: center;
        }

        /* White underline under the time value */
        .time-value::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.8);
        }

        .time-location {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            text-align: center;
            margin-top: 8px;
            font-weight: 500;
        }

        .time-contacts {
            margin-top: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding-top: 12px;
        }

        .contacts-label {
            font-size: 11px;
            color: rgba(0, 255, 255, 0.8);
            font-weight: 600;
            letter-spacing: 1px;
            margin-bottom: 8px;
            text-align: center;
        }

        .contacts-list {
            max-height: 120px;
            overflow-y: auto;
        }

        .contact-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .contact-item:last-child {
            border-bottom: none;
        }

        .contact-name {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
            flex: 1;
        }

        .contact-status {
            font-size: 9px;
            color: rgba(0, 255, 150, 0.7);
            font-weight: 400;
            text-align: right;
        }

        .contact-item:nth-child(2) .contact-status {
            color: rgba(255, 200, 0, 0.7);
        }

        .contact-item:nth-child(3) .contact-status {
            color: rgba(0, 255, 255, 0.7);
        }

        @keyframes timeGlow {
            0%, 100% {
                box-shadow: 0 0 15px rgba(0, 255, 255, 0.3), inset 0 0 10px rgba(0, 255, 255, 0.1);
            }
            50% {
                box-shadow: 0 0 25px rgba(0, 255, 255, 0.5), inset 0 0 15px rgba(0, 255, 255, 0.2);
            }
        }

        /* Weather Display Styles */
        .weather-display {
            position: absolute;
            top: 12vh;
            left: 6vh;
            z-index: 15;
            background: linear-gradient(135deg, rgba(0, 200, 255, 0.08) 0%, rgba(0, 150, 255, 0.05) 100%);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 8px;
            padding: 8px 12px;
            font-family: 'Orbitron', 'Segoe UI', monospace;
            color: white;
            box-shadow: 
                0 0 15px rgba(0, 255, 255, 0.2),
                inset 0 0 10px rgba(0, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            min-width: 220px;
            animation: weatherGlow 3s ease-in-out infinite;
            display: none;
            opacity: 0;
            transform: scale(0.9) translateY(0);
            transition: all 0.3s ease;
        }

        /* Corner brackets - straight angular corners */
        .weather-display::before,
        .weather-display::after {
            content: '';
            position: absolute;
            width: 12px;
            height: 12px;
            border: 2px solid rgba(255, 255, 255, 1);
            z-index: 1;
        }

        .weather-display::before {
            top: -4px;
            left: -4px;
            border-right: none;
            border-bottom: none;
        }

        .weather-display::after {
            bottom: -4px;
            right: -4px;
            border-left: none;
            border-top: none;
        }

        .weather-display .corner-top-right {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 12px;
            height: 12px;
            border: 2px solid rgba(255, 255, 255, 1);
            border-left: none;
            border-bottom: none;
            z-index: 1;
        }

        .weather-display .corner-bottom-left {
            position: absolute;
            bottom: -4px;
            left: -4px;
            width: 12px;
            height: 12px;
            border: 2px solid rgba(255, 255, 255, 1);
            border-right: none;
            border-top: none;
            z-index: 1;
        }

        .weather-label {
            font-size: 11px;
            font-weight: 600;
            letter-spacing: 2px;
            color: white;
            margin-bottom: 12px;
            text-transform: uppercase;
            position: relative;
            background: rgba(0, 150, 255, 0.4);
            padding: 2px 8px;
            margin-left: -8px;
            margin-right: -8px;
            text-align: center;
        }

        /* Main weather section */
        .weather-main {
                       display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .weather-temp-section {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .weather-temp {
            font-size: 24px;
            font-weight: 700;
            letter-spacing: 1px;
            color: rgba(255, 255, 255, 0.95);
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
            position: relative;
        }

        .weather-temp::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.8);
        }

        .weather-feels-like {
            font-size: 9px;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 4px;
            letter-spacing: 0.5px;
        }

        .weather-icon-main {
            font-size: 24px;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
        }

        .weather-condition {
            font-size: 10px;
            font-weight: 500;
            letter-spacing: 1px;
            color: rgba(255, 255, 255, 0.8);
            text-transform: uppercase;
            text-align: center;
            margin-bottom: 12px;
            padding: 3px 6px;
            background: rgba(0, 200, 255, 0.08);
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .weather-condition-emoji {
            font-size: 12px;
        }

        /* Weather details grid */
        .weather-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 8px;
        }

        .weather-detail-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 6px;
            background: rgba(0, 255, 255, 0.04);
            border-radius: 4px;
            border: 1px solid rgba(0, 255, 255, 0.1);
        }

        .weather-detail-label {
            font-size: 8px;
            color: rgba(255, 255, 255, 0.6);
            letter-spacing: 1px;
            text-transform: uppercase;
            margin-bottom: 2px;
        }

        .weather-detail-value {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .weather-detail-emoji {
            font-size: 10px;
        }

        /* Wind and pressure section */
        .weather-extended {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 6px;
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .weather-extended-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 4px;
            background: rgba(0, 200, 255, 0.03);
            border-radius: 3px;
        }

        .weather-extended-label {
            font-size: 7px;
            color: rgba(255, 255, 255, 0.5);
            letter-spacing: 1px;
            text-transform: uppercase;
            margin-bottom: 2px;
        }

        .weather-extended-value {
            font-size: 9px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 3px;
        }

        .weather-extended-emoji {
            font-size: 8px;
        }

        /* Sun times section */
        .weather-sun-times {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .weather-sun-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 6px;
            background: rgba(255, 200, 0, 0.04);
            border-radius: 4px;
            border: 1px solid rgba(255, 200, 0, 0.1);
        }

        .weather-sun-label {
            font-size: 8px;
            color: rgba(255, 255, 255, 0.6);
            letter-spacing: 1px;
            text-transform: uppercase;
            margin-bottom: 2px;
        }

        .weather-sun-value {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .weather-sun-emoji {
            font-size: 12px;
        }

        /* Location info */
        .weather-location {
            text-align: center;
            margin-top: 8px;
            padding-top: 6px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 8px;
            color: rgba(255, 255, 255, 0.6);
            letter-spacing: 1px;
        }

        @keyframes weatherGlow {
            0%, 100% { 
                box-shadow: 
                    0 0 15px rgba(0, 255, 255, 0.2),
                    inset 0 0 10px rgba(0, 255, 255, 0.05);
            }
            50% { 
                box-shadow: 
                    0 0 25px rgba(0, 255, 255, 0.3),
                    inset 0 0 15px rgba(0, 255, 255, 0.1);
            }
        }

        /* ================= COPY BUTTON STYLES ================= */
        .copy-button {
            position: absolute;
            bottom: 8px;
            right: 8px;
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.8) 0%, rgba(0, 200, 255, 0.9) 100%);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 255, 255, 0.2);
            z-index: 15;
        }
        
        .copy-button:hover {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.9) 0%, rgba(0, 200, 255, 1) 100%);
            transform: scale(1.05);
            box-shadow: 0 6px 12px rgba(0, 255, 255, 0.3);
        }
        
        .copy-button:active {
            transform: scale(0.95);
        }
        
        .copy-button svg {
            width: 14px;
            height: 14px;
            fill: rgba(4, 21, 41, 0.9);
        }
        
        .copy-button.copied {
            background: linear-gradient(135deg, rgba(0, 255, 128, 0.8) 0%, rgba(0, 200, 100, 0.9) 100%);
            box-shadow: 0 4px 8px rgba(0, 255, 128, 0.3);
        }
        
        .copy-button.copied svg {
            fill: rgba(4, 21, 41, 0.9);
        }
        
        /* Copy button specific positioning for different widgets */
        .search-widget .copy-button {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.7) 0%, rgba(0, 200, 255, 0.8) 100%);
        }

        .search-widget .copy-button:hover {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.9) 0%, rgba(0, 200, 255, 1) 100%);
        }

        .news-widget .copy-button {
            background: linear-gradient(135deg, rgba(255, 165, 0, 0.7) 0%, rgba(255, 140, 0, 0.8) 100%);
            box-shadow: 0 4px 8px rgba(255, 165, 0, 0.2);
        }

        .news-widget .copy-button:hover {
            background: linear-gradient(135deg, rgba(255, 165, 0, 0.9) 0%, rgba(255, 140, 0, 1) 100%);
            box-shadow: 0 6px 12px rgba(255, 165, 0, 0.3);
        }
        
        /* Tooltip for copy button */
        .copy-button:hover::after {
            content: 'Copy text';
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            white-space: nowrap;
            opacity: 0;
            animation: tooltipFadeIn 0.3s ease forwards;
        }
        
        @keyframes tooltipFadeIn {
            from { opacity: 0; transform: translateX(-50%) translateY(5px); }
            to { opacity: 1; transform: translateX(-50%) translateY(0); }
        }
    </style>
</head>
<body>
    <div class="grid-container">
        <div class="dot-grid"></div>
        <div class="glow-overlay"></div>
        
        <div class="jarvis-interface">
            <div class="concentric-circles">
                <div class="outer-ring"></div>
                <div class="inner-ring"></div>
                <div class="jarvis-text">Nova</div>
            </div>
        </div>

        <!-- Time Display Widget -->
        <div class="time-display widget-draggable widget-size-normal" id="timeDisplay" style="display: none;">
            <div class="corner-top-right"></div>
            <div class="corner-bottom-left"></div>
            <div class="widget-controls">
                <div class="widget-control-btn" onclick="increaseWidgetSize('timeDisplay')" title="Make Bigger">⧨</div>
                <div class="widget-control-btn" onclick="decreaseWidgetSize('timeDisplay')" title="Make Smaller">⧩</div>
                <div class="widget-control-btn" onclick="hideTimeDisplay()" title="Close">⧬</div>
            </div>
            <div class="resize-handle"></div>
            <div class="time-label">TIME</div>
            <div class="time-value" id="timeValue">--:-- --</div>
            <div class="time-location" id="timeLocation">Local Time</div>
            <div class="time-contacts" id="timeContacts">
                <div class="contacts-label">📞 CONTACTS</div>
                <div class="contacts-list" id="contactsList">
                    <div class="contact-item">
                        <span class="contact-name">📱 John Doe</span>
                        <span class="contact-status">Available</span>
                    </div>
                    <div class="contact-item">
                        <span class="contact-name">💼 Sarah Wilson</span>
                        <span class="contact-status">In Meeting</span>
                    </div>
                    <div class="contact-item">
                        <span class="contact-name">🏠 Family Group</span>
                        <span class="contact-status">Online</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search Widget -->
        <div class="search-widget widget-draggable widget-size-normal" id="searchWidget">
            <div class="corner-top-right"></div>
            <div class="corner-bottom-left"></div>
            <div class="widget-controls">
                <div class="widget-control-btn" onclick="increaseWidgetSize('searchWidget')" title="Make Bigger">⧨</div>
                <div class="widget-control-btn" onclick="decreaseWidgetSize('searchWidget')" title="Make Smaller">⧩</div>
                <div class="widget-control-btn" onclick="hideSearchWidget()" title="Close">⧬</div>
            </div>
            <div class="resize-handle"></div>
            <div class="search-label">SEARCH</div>
            <div class="search-content" id="searchContent">Ready to search...</div>
            <div class="search-loading" id="searchLoading">Searching...</div>
        </div>

        <!-- Enhanced News Widget -->
        <div class="news-widget widget-draggable widget-size-normal" id="newsWidget">
            <div class="corner-top-right"></div>
            <div class="corner-bottom-left"></div>
            
            <!-- Widget Controls -->
            <div class="widget-controls">
                <div class="widget-control-btn" onclick="increaseWidgetSize('newsWidget')" title="Make Bigger">⧨</div>
                <div class="widget-control-btn" onclick="decreaseWidgetSize('newsWidget')" title="Make Smaller">⧩</div>
                <div class="widget-control-btn" onclick="hideNewsWidget()" title="Close">⧬</div>
            </div>
            <div class="resize-handle"></div>
            
            <!-- Header -->
            <div class="news-header">
                <div class="news-title">
                    <div class="news-logo">N</div>
                    <div class="news-brand">News Feed</div>
                </div>
                <div class="news-status">
                    <div class="news-time-display" id="newsTimeDisplay">--:--:--</div>
                    <div class="status-indicator"></div>
                    <span>Live</span>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <div class="news-nav">
                <div class="nav-tab active" onclick="switchTab('breaking')">Breaking</div>
                <div class="nav-tab" onclick="switchTab('sports')">Sports</div>
            </div>

            <!-- Content Area -->
            <div class="news-content-area" id="newsContentArea">
                <!-- News items will be populated here -->
            </div>

            <!-- Loading State -->
            <div class="news-loading" id="newsLoading">
                <div class="loading-spinner"></div>
                <div>Fetching latest headlines...</div>
            </div>

            <!-- Footer Controls -->
            <div class="news-footer">
                <div class="news-controls"></div>
                <div class="news-count" id="newsCount">0 stories</div>
            </div>
            
            <!-- Backward compatibility -->
            <div class="news-content" id="newsContent" style="display: none;"></div>
        </div>

        <!-- Test Button for Notes Widget -->
        <button onclick="showNotesWidget()" style="position: fixed; top: 10px; left: 10px; z-index: 9999; background: purple; color: white; padding: 10px; border: none; border-radius: 5px; cursor: pointer;">
            Show Notes Widget
        </button>

        <!-- Notes Widget -->
        <div id="notesWidget" class="notes-widget widget-draggable widget-size-normal widget-text-scale-normal" style="display: none;">
            <div class="corner-top-right"></div>
            <div class="corner-bottom-left"></div>

            <div class="notes-label">NOTES</div>
            <div class="notes-content">
                <!-- Notes Editor -->
                <div id="notesEditor" class="notes-editor">
                    <textarea id="noteTextarea" class="note-textarea" placeholder="Write your note here..."></textarea>
                    <div class="notes-controls">
                        <button id="saveNoteBtn" class="notes-btn save-btn" title="Save Note">
                            <i class="fa-solid fa-save"></i> Save
                        </button>
                        <button id="copyNoteBtn" class="notes-btn copy-btn" title="Copy Note">
                            <i class="fa-solid fa-copy"></i> Copy
                        </button>
                        <button id="viewNotesBtn" class="notes-btn view-btn" title="View Saved Notes">
                            <i class="fa-regular fa-folder-open"></i> View Notes
                        </button>
                        <button id="newNoteBtn" class="notes-btn new-btn" title="New Note" style="display: none;">
                            <i class="fa-solid fa-plus"></i> New
                        </button>
                        <button id="clearNoteBtn" class="notes-btn clear-btn" title="Clear Note">
                            <i class="fa-solid fa-eraser"></i> Clear
                        </button>
                    </div>
                </div>

                <!-- Saved Notes List -->
                <div id="savedNotesList" class="saved-notes-list" style="display: none;">
                    <div class="notes-list-header">
                        <h3>Saved Notes</h3>
                        <button id="backToEditorBtn" class="notes-btn back-btn">
                            <i class="fa-solid fa-arrow-left"></i> Back
                        </button>
                    </div>
                    <div id="notesListContainer" class="notes-list-container">
                        <div class="no-notes-message">No saved notes yet.</div>
                    </div>
                </div>
            </div>

            <!-- Widget Controls -->
            <div class="widget-controls">
                <div class="widget-control-btn" onclick="increaseWidgetSize('notesWidget')" title="Make Bigger">⧨</div>
                <div class="widget-control-btn" onclick="decreaseWidgetSize('notesWidget')" title="Make Smaller">⧩</div>
                <div class="widget-control-btn" onclick="hideNotesWidget()" title="Close">⧬</div>
            </div>

            <!-- Resize Handle -->
            <div class="resize-handle" title="Drag to resize"></div>
        </div>

        <!-- Calculator Widget -->
        <div class="calculator-widget widget-draggable widget-size-normal" id="calculatorWidget">
            <div class="corner-top-right"></div>
            <div class="corner-bottom-left"></div>
            <div class="widget-controls">
                <div class="widget-control-btn" onclick="increaseWidgetSize('calculatorWidget')" title="Make Bigger">⧨</div>
                <div class="widget-control-btn" onclick="decreaseWidgetSize('calculatorWidget')" title="Make Smaller">⧩</div>
                <div class="widget-control-btn" onclick="hideCalculatorWidget()" title="Close">⧬</div>
            </div>
            <div class="resize-handle"></div>
            
            <div class="calculator-label">CALCULATOR</div>
            
            <div class="calculator-screen-area">
                <div class="calculator-input" id="calculatorInput"></div>
                <div class="calculator-result" id="calculatorResult">0</div>
            </div>
            
            <div class="controls-section">
                <div class="calculator-tabs">
                    <button class="tab-button active" onclick="switchTab('main')">Main</button>
                    <button class="tab-button" onclick="switchTab('abc')">ABC</button>
                    <button class="tab-button" onclick="switchTab('func')">Func</button>
                </div>
                
                <div class="angle-mode">
                    <button class="angle-button active" onclick="setAngleMode('deg')">Deg</button>
                    <button class="angle-button" onclick="setAngleMode('rad')">Rad</button>
                </div>
            </div>
            
            <div class="button-section">
                <!-- Main Tab -->
                <div id="main-tab" class="tab-content active">
                    <div class="main-buttons">
                        <button class="calc-btn clear" onclick="clearAll()">C</button>
                        <button class="calc-btn backspace" onclick="backspace()">⌫</button>
                        <button class="calc-btn operator" onclick="appendToInput('/')">/</button>
                        <button class="calc-btn operator" onclick="appendToInput('*')">×</button>
                        <button class="calc-btn operator" onclick="appendToInput('-')">-</button>
                        <button class="calc-btn operator" onclick="appendToInput('+')">+</button>
                        
                        <button class="calc-btn" onclick="appendToInput('7')">7</button>
                        <button class="calc-btn" onclick="appendToInput('8')">8</button>
                        <button class="calc-btn" onclick="appendToInput('9')">9</button>
                        <button class="calc-btn" onclick="appendToInput('(')">(</button>
                        <button class="calc-btn" onclick="appendToInput(')')">)</button>
                        <button class="calc-btn ans" onclick="useLastAnswer()">ANS</button>
                        
                        <button class="calc-btn" onclick="appendToInput('4')">4</button>
                        <button class="calc-btn" onclick="appendToInput('5')">5</button>
                        <button class="calc-btn" onclick="appendToInput('6')">6</button>
                        <button class="calc-btn scientific" onclick="appendToInput('Math.pow(')">x^y</button>
                        <button class="calc-btn scientific" onclick="appendToInput('Math.sqrt(')">√</button>
                        <button class="calc-btn scientific" onclick="appendToInput('%')">%</button>
                        
                        <button class="calc-btn" onclick="appendToInput('1')">1</button>
                        <button class="calc-btn" onclick="appendToInput('2')">2</button>
                        <button class="calc-btn" onclick="appendToInput('3')">3</button>
                        <button class="calc-btn scientific" onclick="appendToInput('Math.sin(')">sin</button>
                        <button class="calc-btn scientific" onclick="appendToInput('Math.cos(')">cos</button>
                        <button class="calc-btn scientific" onclick="appendToInput('Math.tan(')">tan</button>
                        
                        <button class="calc-btn zero" onclick="appendToInput('0')">0</button>
                        <button class="calc-btn" onclick="appendToInput('.')">.</button>
                        <button class="calc-btn equals" onclick="calculate()">=</button>
                    </div>
                </div>
                
                <!-- ABC Tab -->
                <div id="abc-tab" class="tab-content">
                    <div class="abc-buttons">
                        <button class="calc-btn" onclick="appendToInput('A')">A</button>
                        <button class="calc-btn" onclick="appendToInput('B')">B</button>
                        <button class="calc-btn" onclick="appendToInput('C')">C</button>
                        <button class="calc-btn" onclick="appendToInput('D')">D</button>
                        <button class="calc-btn" onclick="appendToInput('E')">E</button>
                        
                        <button class="calc-btn" onclick="appendToInput('F')">F</button>
                        <button class="calc-btn" onclick="appendToInput('G')">G</button>
                        <button class="calc-btn" onclick="appendToInput('H')">H</button>
                        <button class="calc-btn" onclick="appendToInput('I')">I</button>
                        <button class="calc-btn" onclick="appendToInput('J')">J</button>
                        
                        <button class="calc-btn" onclick="appendToInput('K')">K</button>
                        <button class="calc-btn" onclick="appendToInput('L')">L</button>
                        <button class="calc-btn" onclick="appendToInput('M')">M</button>
                        <button class="calc-btn" onclick="appendToInput('N')">N</button>
                        <button class="calc-btn" onclick="appendToInput('O')">O</button>
                        
                        <button class="calc-btn" onclick="appendToInput('P')">P</button>
                        <button class="calc-btn" onclick="appendToInput('Q')">Q</button>
                        <button class="calc-btn" onclick="appendToInput('R')">R</button>
                        <button class="calc-btn" onclick="appendToInput('S')">S</button>
                        <button class="calc-btn" onclick="appendToInput('T')">T</button>
                        
                        <button class="calc-btn" onclick="appendToInput('U')">U</button>
                        <button class="calc-btn" onclick="appendToInput('V')">V</button>
                        <button class="calc-btn" onclick="appendToInput('W')">W</button>
                        <button class="calc-btn" onclick="appendToInput('X')">X</button>
                        <button class="calc-btn" onclick="appendToInput('Y')">Y</button>
                        
                        <button class="calc-btn" onclick="appendToInput('Z')">Z</button>
                        <button class="calc-btn clear" onclick="clearAll()">C</button>
                        <button class="calc-btn backspace" onclick="backspace()">⌫</button>
                        <button class="calc-btn equals" onclick="calculate()">=</button>
                        <button class="calc-btn ans" onclick="useLastAnswer()">ANS</button>
                    </div>
                </div>
                
                <!-- Function Tab -->
                <div id="func-tab" class="tab-content">
                    <div class="func-buttons">
                        <button class="calc-btn scientific" onclick="appendToInput('Math.log(')">log</button>
                        <button class="calc-btn scientific" onclick="appendToInput('Math.log10(')">log10</button>
                        <button class="calc-btn scientific" onclick="appendToInput('Math.exp(')">e^x</button>
                        <button class="calc-btn scientific" onclick="appendToInput('Math.PI')">π</button>
                        
                        <button class="calc-btn scientific" onclick="appendToInput('Math.asin(')">asin</button>
                        <button class="calc-btn scientific" onclick="appendToInput('Math.acos(')">acos</button>
                        <button class="calc-btn scientific" onclick="appendToInput('Math.atan(')">atan</button>
                        <button class="calc-btn scientific" onclick="appendToInput('Math.E')">e</button>
                        
                        <button class="calc-btn scientific" onclick="appendToInput('Math.abs(')">abs</button>
                        <button class="calc-btn scientific" onclick="appendToInput('Math.floor(')">floor</button>
                        <button class="calc-btn scientific" onclick="appendToInput('Math.ceil(')">ceil</button>
                        <button class="calc-btn scientific" onclick="appendToInput('Math.round(')">round</button>
                        
                        <button class="calc-btn scientific" onclick="appendToInput('Math.random()')">rand</button>
                        <button class="calc-btn scientific" onclick="appendToInput('!')">!</button>
                        <button class="calc-btn clear" onclick="clearAll()">C</button>
                        <button class="calc-btn equals" onclick="calculate()">=</button>
                    </div>
                </div>
            </div>
            
            <div class="calculator-loading" id="calculatorLoading">Calculating...</div>
        </div>

        <!-- Weather Display Widget -->
        <div class="weather-display widget-draggable widget-size-normal" id="weatherDisplay" style="display: none;">
            <div class="corner-top-right"></div>
            <div class="corner-bottom-left"></div>
            <div class="widget-controls">
                <div class="widget-control-btn" onclick="increaseWidgetSize('weatherDisplay')" title="Make Bigger">⧨</div>
                <div class="widget-control-btn" onclick="decreaseWidgetSize('weatherDisplay')" title="Make Smaller">⧩</div>
                <div class="widget-control-btn" onclick="hideWeatherDisplay()" title="Close">⧬</div>
            </div>
            <div class="resize-handle"></div>
            <div class="weather-label">WEATHER</div>
            
            <!-- Main temperature and condition -->
            <div class="weather-main">
                <div class="weather-temp-section">
                    <div class="weather-temp" id="weatherTemp">--°F</div>
                    <div class="weather-feels-like" id="weatherFeelsLike">Feels like --°F</div>
                </div>
                <div class="weather-icon-main" id="weatherIcon">--</div>
            </div>
            
            <div class="weather-condition" id="weatherCondition">
                <span class="weather-condition-emoji">--</span>
                <span>--</span>
            </div>
            
            <!-- Weather details -->
            <div class="weather-details">
                <div class="weather-detail-item">
                    <div class="weather-detail-label">High/Low</div>
                    <div class="weather-detail-value" id="weatherHighLow">--° / --°</div>
                </div>
                <div class="weather-detail-item">
                    <div class="weather-detail-label">Humidity</div>
                    <div class="weather-detail-value" id="weatherHumidity">
                        <span class="weather-detail-emoji">💧</span>
                        <span>--%</span>
                    </div>
                </div>
                <div class="weather-detail-item">
                    <div class="weather-detail-label">UV Index</div>
                    <div class="weather-detail-value" id="weatherUV">-- --</div>
                </div>
                <div class="weather-detail-item">
                    <div class="weather-detail-label">Visibility</div>
                    <div class="weather-detail-value" id="weatherVisibility">-- mi</div>
                </div>
            </div>
            
            <!-- Extended details -->
            <div class="weather-extended">
                <div class="weather-extended-item">
                    <div class="weather-extended-label">Wind</div>
                    <div class="weather-extended-value" id="weatherWind">
                        <span class="weather-extended-emoji">💨</span>
                        <span>-- mph --</span>
                    </div>
                </div>
                <div class="weather-extended-item">
                    <div class="weather-extended-label">Pressure</div>
                    <div class="weather-extended-value" id="weatherPressure">-- in</div>
                </div>
                <div class="weather-extended-item">
                    <div class="weather-extended-label">Dew Point</div>
                    <div class="weather-extended-value" id="weatherDewPoint">--°F</div>
                </div>
            </div>
            
            <!-- Sun times section -->
            <div class="weather-sun-times">
                <div class="weather-sun-item">
                    <div class="weather-sun-label">Sunrise</div>
                    <div class="weather-sun-value" id="weatherSunrise">
                        <span class="weather-sun-emoji">🌅</span>
                        <span>--:--</span>
                    </div>
                </div>
                <div class="weather-sun-item">
                    <div class="weather-sun-label">Sunset</div>
                    <div class="weather-sun-value" id="weatherSunset">
                        <span class="weather-sun-emoji">🌇</span>
                        <span>--:--</span>
                    </div>
                </div>
            </div>
            
            <div class="weather-location" id="weatherLocation">Current Location</div>
        </div>

        <!-- Notepad Widget -->
        <div class="notepad-widget widget-draggable widget-size-normal" id="notepadWidget" style="display: none;">
            <div class="corner-top-right"></div>
            <div class="corner-bottom-left"></div>
            <div class="widget-controls">
                <div class="widget-control-btn" onclick="increaseWidgetSize('notepadWidget')" title="Make Bigger">⧨</div>
                <div class="widget-control-btn" onclick="decreaseWidgetSize('notepadWidget')" title="Make Smaller">⧩</div>
                <div class="widget-control-btn" onclick="hideNotepadWidget()" title="Close">⧬</div>
            </div>
            <div class="resize-handle"></div>
            
            <div class="notepad-label">NOTEPAD</div>
            
            <div class="notepad-toolbar">
                <div class="notepad-title">Notes</div>
                <div class="notepad-controls">
                    <button class="notepad-btn" id="notepadDeleteBtn" title="Delete Note">
                        <i class="fa-solid fa-trash"></i>
                    </button>
                    <button class="notepad-btn" id="notepadShareBtn" title="View Saved Notes">
                        <i class="fa-solid fa-list"></i>
                    </button>
                    <button class="notepad-btn" id="notepadCopyBtn" title="Copy Note">
                        <i class="fa-solid fa-copy"></i>
                    </button>
                    <button class="notepad-btn" id="notepadSaveBtn" title="Save Note">
                        <i class="fa-solid fa-floppy-disk"></i>
                    </button>
                </div>
            </div>
            
            <div class="notepad-content-area">
                <textarea id="notepadTextarea" placeholder="Start typing your notes here..."></textarea>
            </div>
            
            <div class="notepad-status-bar">
                <span id="notepadWordCount">0 words</span>
                <span id="notepadSaveStatus">Ready</span>
            </div>
        </div>

        <!-- Chat Messages Area -->
        <div class="chat-messages" id="chatMessages">
            <div class="message nova-message">
                <div class="message-text">Hello! I'm NOVA, your AI assistant. How can I help you today?</div>
            </div>
            <div class="typing-indicator" id="typingIndicator">
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        </div>

        <!-- Original Chat Input (Hidden by default, will be replaced by floating chat) -->
        <div class="chat-container" id="originalChatContainer" style="display: none;">
            <div class="chat-input-wrapper">
                <input type="text" class="chat-input" id="chatInput" placeholder="What can I help you with?" maxlength="500">
                <button class="send-button" id="sendButton">
                    <svg class="send-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Floating Chat Button -->
        <div class="floating-chat-button" id="floatingChatButton" title="Open Chat Interface">
            <div class="chat-button-icon">
                <!-- Better chat icon with speech bubble design -->
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
                    <circle cx="8" cy="10" r="1.5"/>
                    <circle cx="12" cy="10" r="1.5"/>
                    <circle cx="16" cy="10" r="1.5"/>
                </svg>
            </div>
            <div class="chat-button-pulse"></div>
        </div>

        <!-- Modern Chat Interface -->
        <div class="modern-chat-interface" id="modernChatInterface">
            <!-- Chat Header -->
            <div class="chat-header">
                <div class="chat-header-left">
                    <!-- Avatar removed for cleaner interface -->
                    <div class="chat-header-info">
                        <div class="chat-title">Chat Interface</div>
                        <div class="chat-status">
                            <div class="status-dot"></div>
                            <span>Ready</span>
                        </div>
                    </div>
                </div>
                <div class="chat-header-controls">
                    <button class="chat-minimize-btn" id="chatMinimizeBtn" title="Minimize">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 13H5v-2h14v2z"/>
                        </svg>
                    </button>
                    <button class="chat-close-btn" id="chatCloseBtn" title="Close">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Chat Messages Area -->
            <div class="modern-chat-messages" id="modernChatMessages">
                <div class="welcome-message">
                    <div class="welcome-avatar">
                        <span>N</span>
                    </div>
                    <div class="welcome-content">
                        <div class="welcome-title">Welcome to NOVA AI!</div>
                        <div class="welcome-text">I'm here to help you with anything you need. What would you like to know?</div>
                    </div>
                </div>
            </div>

            <!-- Chat Input Area -->
            <div class="modern-chat-input-area">
                <div class="chat-input-container">
                    <div class="input-wrapper">
                        <input type="text" class="modern-chat-input" id="modernChatInput" placeholder="Type your message..." maxlength="500">
                        <button class="modern-send-button" id="modernSendButton">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const chatInput = document.getElementById('chatInput');
        const sendButton = document.getElementById('sendButton');
        const chatMessages = document.getElementById('chatMessages');

        // Modern Chat Elements
        const floatingChatButton = document.getElementById('floatingChatButton');
        const modernChatInterface = document.getElementById('modernChatInterface');
        const modernChatInput = document.getElementById('modernChatInput');
        const modernSendButton = document.getElementById('modernSendButton');
        const modernChatMessages = document.getElementById('modernChatMessages');
        const chatMinimizeBtn = document.getElementById('chatMinimizeBtn');
        const chatCloseBtn = document.getElementById('chatCloseBtn');

        // Modern Chat State
        let isChatOpen = false;
        let isChatMinimized = false;
        
        // Search Widget Elements
        const searchWidget = document.getElementById('searchWidget');
        const searchContent = document.getElementById('searchContent');
        const searchLoading = document.getElementById('searchLoading');
        const typingIndicator = document.getElementById('typingIndicator');

        const urlParams = new URLSearchParams(window.location.search);
        const apiPort = urlParams.get('api_port') || '5000';
        const apiUrl = `http://127.0.0.1:${apiPort}/api/chat`;

        // Generate a unique session ID for this conversation
        let sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        
        // Store user's location (default to Italy for testing)
        let userLocation = localStorage.getItem('userLocation') || 'Italy';
        if (userLocation === 'Italy') {
            localStorage.setItem('userLocation', 'Italy');
            console.log('🌍 Set default location to Italy');
        }
        let locationDetectionShown = localStorage.getItem('locationDetectionShown') === 'true';
        
        // Cache for faster time requests
        let timeCache = {};
        const CACHE_DURATION = 30000; // 30 seconds
        
        // Auto-update timer
        let timeUpdateInterval = null;
        let secondsUpdateInterval = null;
        let currentDisplayLocation = null;
        let lastKnownTime = null;

        // Weather display variables
        let weatherDisplayInterval = null;
        let currentWeatherLocation = null;
        let weatherCache = {};
        const WEATHER_CACHE_DURATION = 300000; // 5 minutes

        // ================= ENHANCED TIME DISPLAY FUNCTIONS =================
        // Global variables for time management
        let realTimeInterval = null;
        let lastServerTime = null;
        let lastServerTimestamp = null;
        let timeOffset = 0; // Offset between server time and local time

        function updateTimeDisplay(timeString, location = null) {
            const timeValue = document.getElementById('timeValue');
            const oldTime = timeValue.textContent;
            
            console.log(`⚡ UPDATING TIME DISPLAY: ${timeString} for ${location}`);
            
            // Store server time information for real-time calculation
            lastServerTime = parseTimeString(timeString);
            lastServerTimestamp = Date.now();
            
            // Calculate offset if we have location context
            if (location && location !== 'local') {
                calculateTimeOffset(timeString);
            }
            
            // Visual feedback for time changes
            if (oldTime !== timeString && oldTime !== "Loading..." && oldTime !== "--:-- --") {
                console.log(`✅ TIME CHANGED: ${oldTime} → ${timeString}`);
                
                // Bright flash for actual time changes
                timeValue.style.color = '#00ff00';
                timeValue.style.textShadow = '0 0 30px #00ff00';
                timeValue.style.transform = 'scale(1.1)';
                
                setTimeout(() => {
                    timeValue.style.color = 'rgba(255, 255, 255, 0.95)';
                    timeValue.style.textShadow = '0 0 12px rgba(255, 255, 255, 0.6)';
                    timeValue.style.transform = 'scale(1)';
                }, 800);
            } else if (oldTime === timeString && oldTime !== "Loading..." && oldTime !== "--:-- --") {
                // Subtle pulse for same time (showing it's updating)
                timeValue.style.textShadow = '0 0 20px rgba(0, 255, 255, 0.8)';
                setTimeout(() => {
                    timeValue.style.textShadow = '0 0 12px rgba(255, 255, 255, 0.6)';
                }, 300);
            }
            
            // Update the display
            timeValue.textContent = timeString;
            
            // Start real-time updates
            startRealTimeUpdates(location);
        }

        function parseTimeString(timeString) {
            // Parse various time formats: HH:MM:SS, HH:MM AM/PM, etc.
            const patterns = [
                /(\d{1,2}):(\d{2}):(\d{2})(\s?(AM|PM))?/i, // HH:MM:SS with optional AM/PM
                /(\d{1,2}):(\d{2})(\s?(AM|PM))?/i          // HH:MM with optional AM/PM
            ];
            
            for (const pattern of patterns) {
                const match = timeString.match(pattern);
                if (match) {
                    let hours = parseInt(match[1]);
                    const minutes = parseInt(match[2]);
                    const seconds = match[3] ? parseInt(match[3]) : 0;
                    const period = match[4] || match[5]; // AM/PM if present
                    
                    // Convert to 24-hour format if needed
                    if (period) {
                        if (period.toUpperCase() === 'PM' && hours !== 12) {
                            hours += 12;
                        } else if (period.toUpperCase() === 'AM' && hours === 12) {
                            hours = 0;
                        }
                    }
                    
                    return { hours, minutes, seconds };
                }
            }
            
            return null;
        }

        function calculateTimeOffset(serverTimeString) {
            // This would calculate the offset between server time and local time
            // For now, we'll use the server time as authoritative
            timeOffset = 0;
        }

        function startRealTimeUpdates(location = null) {
            // Clear any existing real-time interval
            if (realTimeInterval) {
                clearInterval(realTimeInterval);
            }
            
            if (!lastServerTime) {
                console.warn('No server time available for real-time updates');
                return;
            }
            
            console.log(`🔄 Starting real-time updates for: ${location || 'unknown location'}`);
            
            // Update every second for smooth real-time display
            realTimeInterval = setInterval(() => {
                const timeValue = document.getElementById('timeValue');
                if (!timeValue || !lastServerTime || !lastServerTimestamp) return;
                
                // Calculate elapsed time since last server update
                const elapsedMs = Date.now() - lastServerTimestamp;
                const elapsedSeconds = Math.floor(elapsedMs / 1000);
                
                // Calculate current time based on server time + elapsed time
                let currentSeconds = lastServerTime.seconds + elapsedSeconds;
                let currentMinutes = lastServerTime.minutes;
                let currentHours = lastServerTime.hours;
                
                // Handle overflow
                if (currentSeconds >= 60) {
                    const additionalMinutes = Math.floor(currentSeconds / 60);
                    currentSeconds = currentSeconds % 60;
                    currentMinutes += additionalMinutes;
                }
                
                if (currentMinutes >= 60) {
                    const additionalHours = Math.floor(currentMinutes / 60);
                    currentMinutes = currentMinutes % 60;
                    currentHours += additionalHours;
                }
                
                if (currentHours >= 24) {
                    currentHours = currentHours % 24;
                }
                
                // Format the time
                const formattedTime = formatTime(currentHours, currentMinutes, currentSeconds);
                
                // Update display only if it has changed
                if (timeValue.textContent !== formattedTime) {
                    timeValue.textContent = formattedTime;
                    
                    // Subtle animation for second updates
                    timeValue.style.textShadow = '0 0 15px rgba(0, 255, 255, 0.6)';
                    setTimeout(() => {
                        timeValue.style.textShadow = '0 0 12px rgba(255, 255, 255, 0.6)';
                    }, 200);
                }
                
                // Refresh from server every 30 seconds to stay accurate
                if (elapsedSeconds > 0 && elapsedSeconds % 30 === 0 && currentDisplayLocation) {
                    console.log(`🔄 Refreshing time from server after ${elapsedSeconds} seconds`);
                    refreshTimeFromServer(currentDisplayLocation);
                }
            }, 1000);
        }

        function formatTime(hours, minutes, seconds) {
            // Format time as HH:MM:SS
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        async function refreshTimeFromServer(location) {
            try {
                // Clear cache to force fresh request
                const cacheKey = location.toLowerCase();
                if (timeCache[cacheKey]) {
                    delete timeCache[cacheKey];
                }
                
                const freshTime = await getTimeForLocation(location, true);
                if (freshTime) {
                    console.log(`🔄 Server time refreshed: ${freshTime}`);
                    // Update our reference time
                    lastServerTime = parseTimeString(freshTime);
                    lastServerTimestamp = Date.now();
                }
            } catch (error) {
                console.error('❌ Failed to refresh time from server:', error);
            }
        }

        function stopRealTimeUpdates() {
            if (realTimeInterval) {
                clearInterval(realTimeInterval);
                realTimeInterval = null;
                console.log('⏹️ Real-time updates stopped');
            }
            // Reset time tracking variables
            lastServerTime = null;
            lastServerTimestamp = null;
            timeOffset = 0;
        }

        // Enhanced showTimeDisplay function
        function showTimeDisplay(timeString, location = null) {
            const timeDisplay = document.getElementById('timeDisplay');
            const timeValue = document.getElementById('timeValue');
            const timeLocation = document.getElementById('timeLocation');
            
            console.log(`⚡ SHOWING TIME: ${timeString} for ${location}`);
            
            // Set initial time
            timeValue.textContent = timeString;
            
            // Update location display
            if (location) {
                timeLocation.textContent = location.charAt(0).toUpperCase() + location.slice(1);
                currentDisplayLocation = location;
            } else {
                timeLocation.textContent = 'Local Time';
            }
            
            // Show the display with animation
            timeDisplay.style.cssText = `
                display: block !important;
                opacity: 1 !important;
                transform: scale(1) translateY(0) !important;
                transition: all 0.3s ease !important;
                animation: timeGlow 3s ease-in-out infinite !important;
                visibility: visible !important;
            `;
            
            // Start real-time updates immediately
            updateTimeDisplay(timeString, location);
        }

        // Enhanced time update management
        function startTimeUpdates(location) {
            // Stop any existing updates
            stopTimeUpdates();
            stopRealTimeUpdates();
            
            if (!location || location === 'local') {
                showLocalTime();
                return;
            }
            
            currentDisplayLocation = location;
            console.log(`🔄 Starting comprehensive time updates for: ${location}`);
            
            // Server sync interval - get fresh time from server every 30 seconds
            timeUpdateInterval = setInterval(async () => {
                if (currentDisplayLocation) {
                    console.log(`🕐 Syncing with server for ${currentDisplayLocation}...`);
                    
                    try {
                        // Force fresh request
                        const time = await getTimeForLocation(currentDisplayLocation, true);
                        if (time) {
                            // Update our reference time for real-time calculation
                            lastServerTime = parseTimeString(time);
                            lastServerTimestamp = Date.now();
                            console.log(`✅ Server sync complete: ${time}`);
                        } else {
                            console.warn('❌ Server sync failed - retrying...');
                            // Retry in 5 seconds
                            setTimeout(async () => {
                                const retryTime = await getTimeForLocation(currentDisplayLocation, true);
                                if (retryTime) {
                                    lastServerTime = parseTimeString(retryTime);
                                    lastServerTimestamp = Date.now();
                                }
                            }, 5000);
                        }
                    } catch (error) {
                        console.error('❌ Server sync error:', error);
                    }
                }
            }, 30000); // Sync with server every 30 seconds
        }

        function stopTimeUpdates() {
            if (timeUpdateInterval) {
                clearInterval(timeUpdateInterval);
                timeUpdateInterval = null;
            }
            stopRealTimeUpdates();
            currentDisplayLocation = null;
            console.log('⏹️ All time updates stopped');
        }

        function addMessage(text, isUser = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'nova-message'}`;
            
            const messageText = document.createElement('div');
            messageText.className = 'message-text';
            messageText.textContent = text;
            
            messageDiv.appendChild(messageText);
            chatMessages.insertBefore(messageDiv, typingIndicator);
            
            // Auto-scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;

            if (!isUser) {
                updateTimeLabelFromAI(text);
            }
        }

        function showTypingIndicator() {
            typingIndicator.style.display = 'block';
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function hideTypingIndicator() {
            typingIndicator.style.display = 'none';
        }

        function isTimeRequest(message) {
            return /^(show|what('| i)?s|tell me|current)? ?(the )?time( in [\w\s]+)?\??$/i.test(message.trim());
        }

        function isWeatherRequest(message) {
            // Weather requests are now handled by Nova AI, not locally
            return false;
        }

        function detectLocationFromMessage(message) {
            const patterns = [
                /(?:show|time|how about|what about)\s+(?:me\s+the\s+time\s+)?(?:in|at|for)\s+([\w\s]+)/i,
                /(?:how about|what about)\s+([\w\s]+)/i,
                /time\s+(?:in|at|for)\s+([\w\s]+)/i,
                /([\w\s]+)\s+time/i
            ];
            
            for (let pattern of patterns) {
                const match = message.match(pattern);
                if (match) {
                    return match[1].trim().toLowerCase();
                }
            }
            return null;
        }

        function detectWeatherLocationFromMessage(message) {
            const patterns = [
                /(?:show|what('| i)?s|tell me|current)? ?(?:the )?weather(?: in| at| for)\s+([\w\s]+)/i,
                /weather.*(?:in|at|for)\s+([\w\s]+)/i,
                /(?:temperature|forecast|conditions).*(?:in|at|for)\s+([\w\s]+)/i,
                /(?:how about|what about)\s+([\w\s]+)/i
            ];
            
            for (let pattern of patterns) {
                const match = message.match(pattern);
                if (match) {
                    return match[1].trim().toLowerCase();
                }
            }
            return null;
        }

        function saveUserLocation(location, showMessage = true) {
            userLocation = location;
            localStorage.setItem('userLocation', location);
            if (showMessage && !locationDetectionShown) {
                localStorage.setItem('locationDetectionShown', 'true');
                locationDetectionShown = true;
            }
            console.log(`User location saved: ${location}`);
        }

        async function detectUserLocationFromIP() {
            try {
                const response = await fetch('https://ipapi.co/json/');
                const data = await response.json();
                if (data.country_name) {
                    saveUserLocation(data.country_name.toLowerCase());
                    if (!locationDetectionShown) {
                        addMessage(`I've detected you're in ${data.country_name}. I'll remember this for future time requests.`, false);
                    }
                }
            } catch (error) {
                console.log('Could not detect location from IP');
            }
        }

        async function getTimeForLocation(location, forceRefresh = false) {
            const cacheKey = location.toLowerCase();
            const now = Date.now();
            
            console.log(`🕐 getTimeForLocation called with: "${location}", forceRefresh: ${forceRefresh}`);
            
            // Always skip cache when forceRefresh is true for real-time updates
            if (!forceRefresh && timeCache[cacheKey] && (now - timeCache[cacheKey].timestamp < 3000)) {
                console.log(`📋 Using cached time for ${location}: ${timeCache[cacheKey].time}`);
                return timeCache[cacheKey].time;
            }
            
            try {
                console.log(`🌐 Making API request for time in: ${location}`);
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache'
                    },
                    body: JSON.stringify({ 
                        message: `What is the current time in ${location} right now? Please provide the time in HH:MM:SS format with seconds included. Always show seconds. Timestamp: ${Date.now()}`,
                        session_id: sessionId 
                    })
                });
                
                const data = await response.json();
                console.log(`📡 API response for ${location}:`, data.response?.substring(0, 100) + '...');
                
                if (data.response) {
                    // Check if we have separate widget time format
                    if (data.response.includes('WIDGET_TIME:')) {
                        const parts = data.response.split('WIDGET_TIME:');
                        const timeString = parts[1].trim();
                        console.log(`⏰ Found widget time in getTimeForLocation: ${timeString}`);
                        
                        // Only cache for 3 seconds for frequent updates
                        timeCache[cacheKey] = {
                            time: timeString,
                            timestamp: now
                        };
                        return timeString;
                    }
                    
                    // Fallback: Try multiple time patterns to be more robust (PRIORITIZING SECONDS)
                    const timePatterns = [
                        /(\d{1,2}:\d{2}:\d{2}(?:\s?(?:AM|PM|am|pm))?)/,  // HH:MM:SS with optional AM/PM
                        /time.*?(\d{1,2}:\d{2}:\d{2})/i,  // "time is HH:MM:SS"
                        /(\d{1,2}:\d{2}:\d{2})/,  // Just HH:MM:SS
                        // If we only get HH:MM, we'll add :00 for seconds
                        /(\d{1,2}:\d{2})(?:\s?(?:AM|PM|am|pm))?/,  // HH:MM - we'll add seconds
                        /time.*?(\d{1,2}:\d{2})/i,  // "time is HH:MM" - we'll add seconds
                    ];
                    
                    for (const pattern of timePatterns) {
                        const timeMatch = data.response.match(pattern);
                        if (timeMatch) {
                            let timeString = timeMatch[1];
                            console.log(`⏰ Successfully extracted time: ${timeString}`);
                            
                            // ENSURE SECONDS ARE ALWAYS INCLUDED
                            if (timeString && !timeString.match(/:\d{2}:\d{2}/)) {
                                // If time is HH:MM format, add :00 for seconds
                                const timeParts = timeString.split(' ');
                                const timeOnly = timeParts[0];
                                const ampm = timeParts[1] || '';
                                
                                if (timeOnly.split(':').length === 2) {
                                    timeString = timeOnly + ':00' + (ampm ? ' ' + ampm : '');
                                    console.log(`🔧 Added seconds to time: "${timeString}"`);
                                }
                            }
                            
                            // Only cache for 3 seconds for frequent updates
                            timeCache[cacheKey] = {
                                time: timeString,
                                timestamp: now
                            };
                            return timeString;
                        }
                    }
                    
                    console.warn(`⚠️ No time pattern found in response for ${location}`);
                    console.warn(`Full response: ${data.response}`);
                }
            } catch (error) {
                console.error(`❌ Error getting time for ${location}:`, error);
            }
            
            console.error(`❌ Failed to get time for: ${location}`);
            return null;
        }

        // 🌍 SMART LOCATION DETECTION & MEMORY SYSTEM
        function detectAndSaveUserLocation(message) {
            const msg = message.toLowerCase();
            
            // 📍 DETECT: "I am in [location]" - ONLY if no location is saved yet
            const locationPatterns = [
                /(?:i am|i'm|im)\s+(?:in|at|from|located in)\s+([a-zA-Z\s]+)/i,
                /(?:i live|i'm living|im living)\s+(?:in|at)\s+([a-zA-Z\s]+)/i,
                /(?:my location is|i'm located in|im located in)\s+([a-zA-Z\s]+)/i,
                /(?:currently in|currently at)\s+([a-zA-Z\s]+)/i
            ];
            
            // Only save initial location if none exists
            if (!userLocation) {
                for (const pattern of locationPatterns) {
                    const match = message.match(pattern);
                    if (match) {
                        const newLocation = match[1].trim();
                        setUserLocation(newLocation, message);
                        return true;
                    }
                }
            } else {
                // If location already exists, check if user is trying to set it again
                for (const pattern of locationPatterns) {
                    const match = message.match(pattern);
                    if (match) {
                        console.log(`📍 Location already saved as: ${userLocation}. Ignoring duplicate.`);
                        addMessage(message, true);
                        addMessage(`I already know you're in ${userLocation}! If you've moved, say "I moved to [new location]" to update it.`, false);
                        return true;
                    }
                }
            }
            
            // 🔄 DETECT: "I moved to [location]" - ALWAYS check for moves
            const movePatterns = [
                /(?:i moved|i'm now|im now|now i'm|now im)\s+(?:in|at|to)\s+([a-zA-Z\s]+)/i,
                /(?:i relocated|i'm no longer|im no longer).*(?:in|at|to)\s+([a-zA-Z\s]+)/i,
                /(?:i changed|i switched).*(?:location|place).*(?:to|in)\s+([a-zA-Z\s]+)/i,
                /(?:not in .* anymore|no longer in).*(?:now in|now at)\s+([a-zA-Z\s]+)/i
            ];
            
            for (const pattern of movePatterns) {
                const match = message.match(pattern);
                if (match) {
                    const newLocation = match[1].trim();
                    updateUserLocation(newLocation, message);
                    return true;
                }
            }
            
            return false;
        }
        
        function setUserLocation(location, originalMessage) {
            const cleanLocation = location.replace(/[^\w\s]/g, '').trim();
            
            // Improve location format for better time requests
            const improvedLocation = improveLocationFormat(cleanLocation);
            
            userLocation = improvedLocation;
            localStorage.setItem('userLocation', improvedLocation);
            console.log(`📍 Location saved: "${cleanLocation}" → "${improvedLocation}"`);
            
            // Show user's message first
            addMessage(originalMessage, true);
            
            // Confirm to user - SAVE ONCE, NEVER REPEAT
            addMessage(`Perfect! I've saved your location as ${improvedLocation}. Now I'll remember this forever! 🌍`, false);
        }
        
        function improveLocationFormat(location) {
            const loc = location.toLowerCase().trim();
            
            // Map common country names to major cities for better time accuracy
            const locationMappings = {
                'italy': 'Rome, Italy',
                'france': 'Paris, France', 
                'germany': 'Berlin, Germany',
                'spain': 'Madrid, Spain',
                'uk': 'London, UK',
                'united kingdom': 'London, UK',
                'england': 'London, England',
                'japan': 'Tokyo, Japan',
                'china': 'Beijing, China',
                'india': 'New Delhi, India',
                'brazil': 'São Paulo, Brazil',
                'australia': 'Sydney, Australia',
                'canada': 'Toronto, Canada',
                'mexico': 'Mexico City, Mexico',
                'russia': 'Moscow, Russia',
                'south korea': 'Seoul, South Korea',
                'usa': 'New York, USA',
                'united states': 'New York, USA',
                'america': 'New York, USA'
            };
            
            // Check if it's a country that needs a city
            if (locationMappings[loc]) {
                return locationMappings[loc];
            }
            
            // If it's already a city, keep it as is
            return location;
        }
        
        function updateUserLocation(newLocation, originalMessage) {
            const cleanLocation = newLocation.replace(/[^\w\s]/g, '').trim();
            const improvedLocation = improveLocationFormat(cleanLocation);
            const oldLocation = userLocation;
            
            userLocation = improvedLocation;
            localStorage.setItem('userLocation', improvedLocation);
            console.log(`🔄 Location updated: "${oldLocation}" → "${improvedLocation}"`);
            
            // Show user's message first
            addMessage(originalMessage, true);
            
            // Confirm to user
            addMessage(`Location updated! I've changed your location from ${oldLocation || 'unknown'} to ${improvedLocation}. Got it! 📍`, false);
        }

        async function handleSendMessage() {
            const message = chatInput.value.trim();
            if (!message) return;

            // 🌍 SMART LOCATION DETECTION & MEMORY SYSTEM
            const locationDetected = detectAndSaveUserLocation(message);
            if (locationDetected) {
                // Location was detected and saved, don't send to AI
                chatInput.value = '';
                return;
            }

            // Check for "clear the time" command
            if (/^(clear|hide|remove)\s+(the\s+)?time$/i.test(message)) {
                hideTimeDisplay();
                addMessage(message, true);
                addMessage("Time display cleared.", false);
                chatInput.value = '';
                return;
            }

            // Check for "clear the weather" command
            if (/^(clear|hide|remove)\s+(the\s+)?weather$/i.test(message)) {
                hideWeatherDisplay();
                addMessage(message, true);
                addMessage("Weather display cleared.", false);
                chatInput.value = '';
                return;
            }

            // Check for "clear the search" command
            if (/^(clear|hide|remove)\s+(the\s+)?search$/i.test(message)) {
                hideSearchWidget();
                addMessage(message, true);
                addMessage("Search display cleared.", false);
                chatInput.value = '';
                return;
            }

            // Check for "clear the news" command
            if (/^(clear|hide|remove)\s+(the\s+)?news$/i.test(message)) {
                hideNewsWidget();
                addMessage(message, true);
                addMessage("News display cleared.", false);
                chatInput.value = '';
                return;
            }

            // Check for "reset widgets" command
            if (/^reset\s+(widgets?|positions?)$/i.test(message)) {
                resetAllWidgetPositions();
                addMessage(message, true);
                addMessage("All widget positions and sizes have been reset to default.", false);
                chatInput.value = '';
                return;
            }

            // Weather requests are now handled by Nova AI - removed local weather interception

            // Check for location detection from user messages
            const locationFromMessage = detectLocationFromMessage(message);
            if (locationFromMessage && !userLocation) {
                if (/^(i'm|i am|i live)\s+(?:in|at|from)\s+([\w\s]+)/i.test(message)) {
                    saveUserLocation(locationFromMessage, false);
                    addMessage(message, true);
                    addMessage(`Got it! I've saved ${locationFromMessage.charAt(0).toUpperCase() + locationFromMessage.slice(1)} as your location.`, false);
                    chatInput.value = '';
                    return;
                }
            }

            // Let the AI backend handle ALL time requests - this is simpler and more reliable

            // 🔍 DETECT SEARCH REQUESTS
            const isSearch = isSearchRequest(message);
            if (isSearch) {
                const searchQuery = extractSearchQuery(message);
                console.log(`🔍 Search detected: "${searchQuery}"`);
                
                // Show search loading widget instead of adding to chat
                showSearchLoading();
                
                // Add user message to chat but indicate it's a search
                addMessage(`🔍 Searching: ${searchQuery}`, true);
            } 
            // 📰 DETECT NEWS REQUESTS
            else if (isNewsRequest(message)) {
                const newsQuery = extractNewsQuery(message);
                console.log(`📰 Enhanced news detected:`, newsQuery);
                
                // Show news loading widget instead of adding to chat
                showNewsLoading();
                
                // Create enhanced message based on query type
                let displayMessage = '';
                if (newsQuery && newsQuery.type === 'topic_with_source') {
                    displayMessage = `📰 Getting news: ${newsQuery.topic} from ${newsQuery.source}`;
                } else if (newsQuery && newsQuery.type === 'source_only') {
                    displayMessage = `📰 Getting news from ${newsQuery.source}`;
                } else if (newsQuery && newsQuery.type === 'topic_only') {
                    const suggestedSource = suggestNewsSource(newsQuery.topic);
                    displayMessage = `📰 Getting news: ${newsQuery.topic} (from ${suggestedSource})`;
                } else if (typeof newsQuery === 'string') {
                    displayMessage = `📰 Getting news: ${newsQuery}`;
                } else {
                    displayMessage = `📰 Getting latest news`;
                }
                
                // Add user message to chat but indicate it's a news request
                addMessage(displayMessage, true);
            }
            // 🧮 DETECT CALCULATOR REQUESTS
            else if (isCalculatorRequest(message)) {
                const calculationQuery = extractCalculationQuery(message);
                console.log(`🧮 Calculator detected: "${calculationQuery}"`);
                
                // Show calculator loading widget instead of adding to chat
                showCalculatorLoading();
                
                // Add user message to chat but indicate it's a calculation
                addMessage(`🧮 Calculating: ${calculationQuery}`, true);
            } else {
                addMessage(message, true);
            }
            
            chatInput.value = '';
            showTypingIndicator();

            console.log(`🚀 Sending: "${message}"`);
            const startTime = Date.now();

            try {
                // Include user location in the request if available
                const requestBody = { 
                    message: message, 
                    session_id: sessionId 
                };
                
                // Add location context if available
                if (userLocation) {
                    requestBody.user_location = userLocation;
                    console.log(`📍 Sending user location: ${userLocation}`);
                } else {
                    console.log(`❌ No user location to send`);
                }
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache'
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();
                const responseTime = Date.now() - startTime;
                
                console.log(`⚡ Response in ${responseTime}ms`);
                hideTypingIndicator();

                if (data.session_id) sessionId = data.session_id;

                if (response.ok) {
                    console.log(`📝 AI: ${data.response.substring(0, 50)}...`);
                    
                    // 🔄 FORCE "MORE INFO" REQUESTS TO WIDGETS - NEVER TO CHAT
                    const isMoreInfoRequest = isMoreInfoRequest_Function(message);
                    if (isMoreInfoRequest) {
                        console.log('🔄 MORE INFO REQUEST DETECTED - FORCING TO WIDGETS');
                        
                        // Determine if this is search or news related
                        if (isSearchRelated(message, data.response)) {
                            console.log('🔍 More info is search-related, showing in search widget');
                            updateSearchWidget(data.response);
                            return;
                        } else if (isNewsRelated(message, data.response)) {
                            console.log('📰 More info is news-related, showing in news widget');
                            updateNewsWidget(data.response);
                            return;
                        } else {
                            // Default to search widget for more info requests
                            console.log('🔍 More info defaulting to search widget');
                            updateSearchWidget(data.response);
                            return;
                        }
                    }
                    
                    // 🔍 CHECK FOR SEARCH RESULTS - Always show in widget, including follow-ups
                    if (data.response && data.response.includes('SEARCH_RESULT:')) {
                        // Extract the search result content (remove the prefix)
                        const searchResult = data.response.replace('SEARCH_RESULT: ', '');
                        console.log('🔍 Search result detected, showing in widget (including follow-ups)');
                        
                        // Show result in search widget instead of chat
                        updateSearchWidget(searchResult);
                        
                        // Don't add search results to chat, only show in widget
                        return;
                    }
                    
                    // 📰 CHECK FOR NEWS RESULTS - Always show in widget, including follow-ups
                    if (data.response && data.response.includes('NEWS_RESULT:')) {
                        // Extract the news result content (remove the prefix)
                        const newsResult = data.response.replace('NEWS_RESULT: ', '');
                        console.log('📰 News result detected, showing in widget (including follow-ups)');
                        
                        // Show result in news widget instead of chat
                        updateNewsWidget(newsResult);
                        
                        // Don't add news results to chat, only show in widget
                        return;
                    }
                    
                    // Check for special time display commands from AI
                    if (data.response.includes('TIME_DISPLAY_REQUEST:')) {
                        // AI wants to show time display but needs location
                        const cleanResponse = data.response.replace('TIME_DISPLAY_REQUEST: ', '');
                        addMessage(cleanResponse, false);
                        
                        // Show empty time display to indicate time feature is available
                        showTimeDisplay("Please specify location", "");
                        
                    } else if (data.response.includes('TIME_DISPLAY_SHOW:')) {
                        // AI has time information to display
                        const parts = data.response.split('TIME_DISPLAY_SHOW: ')[1];
                        const [location, timeInfo] = parts.split('|');
                        
                        // Extract time for chat message and widget display separately
                        console.log(`[TIME] Extracting time from: "${timeInfo}"`);
                        
    let timeString = null;
                        let chatMessage = timeInfo;
                        
                        // Check if we have separate widget time
                        if (timeInfo.includes('WIDGET_TIME:')) {
                            const parts = timeInfo.split('WIDGET_TIME:');
                            chatMessage = parts[0].trim();
                            timeString = parts[1].trim();
                            console.log(`[TIME] Found widget time: "${timeString}"`);
                        } else {
                            // Fallback: extract time from message and add seconds
                                                const timePatterns = [
                                /(\d{1,2}:\d{2}:\d{2}(?:\s?(?:AM|PM|am|pm))?)/,  // HH:MM:SS with optional AM/PM
                                /time.*?(\d{1,2}:\d{2}:\d{2})/i,  // "time is HH:MM:SS"
                                /(\d{1,2}:\d{2}:\d{2})/,  // Just HH:MM:SS
                                /(\d{1,2}:\d{2})(?:\s?(?:AM|PM|am|pm))?/,  // HH:MM - we'll add seconds
                                /time.*?(\d{1,2}:\d{2})/i,  // "time is HH:MM" - we'll add seconds
                            ];
                            
                            for (const pattern of timePatterns) {
                                const timeMatch = timeInfo.match(pattern);
                                if (timeMatch) {
                                    timeString = timeMatch[1];
                                    console.log(`[TIME] Extracted time: "${timeString}" using pattern: ${pattern}`);
                                    
                                    // ENSURE SECONDS ARE ALWAYS INCLUDED
                                    if (timeString && !timeString.match(/:\d{2}:\d{2}/)) {
                                        // If time is HH:MM format, add :00 for seconds
                                        const timeParts = timeString.split(' ');
                                        const timeOnly = timeParts[0];
                                        const ampm = timeParts[1] || '';
                                        
                                        if (timeOnly.split(':').length === 2) {
                                            timeString = timeOnly + ':00' + (ampm ? ' ' + ampm : '');
                                            console.log(`🔧 Added seconds to time: "${timeString}"`);
                                        }
                                    }
                                    break;
                                }
                            }
                        }
                        
                        if (timeString && location) {
                            showTimeDisplay(timeString, location.trim());
                            startTimeUpdates(location.trim());
                            // Show the chat message without the widget time part
                            addMessage(chatMessage, false);
                        } else {
                            console.error(`❌ Failed to extract time from: "${timeInfo}"`);
                            addMessage(timeInfo, false);
                        }
                        
                    } else if (data.response.includes('LOCATION_UPDATE:')) {
                        // AI detected location update
                        const parts = data.response.split('LOCATION_UPDATE: ')[1];
                        const [location, message] = parts.split('|');
                        
                        // Update the stored user location
                        userLocation = location.trim();
                        localStorage.setItem('userLocation', userLocation);
                        console.log(`🌍 Location updated to: ${userLocation}`);
                        
                        // Show the confirmation message
                        addMessage(message, false);
                        
                        // Automatically show time for the new location
                        setTimeout(async () => {
                            console.log(`🕐 Auto-showing time for new location: ${userLocation}`);
                            const time = await getTimeForLocation(userLocation);
                            if (time) {
                                showTimeDisplay(time, userLocation);
                                startTimeUpdates(userLocation);
                                addMessage(`Here's the current time in ${userLocation}: ${time}`, false);
                            }
                        }, 1000);
                        
                    } else if (data.response.includes('WIDGET_MOVE:')) {
                        // AI wants to move a widget
                        const commandParts = data.response.split('WIDGET_MOVE: ')[1];
                        const [widgetName, direction, amount] = commandParts.split('|');
                        
                        console.log(`🎯 Processing widget movement: ${widgetName} ${direction} ${amount}`);
                        
                        // Execute the widget movement
                        const result = moveWidgetByCommand(widgetName, direction, amount);
                        
                        // Show the result in chat
                        addMessage(result, false);
                        
                    } else if (data.response.includes('WEATHER_DISPLAY_SHOW:')) {
                        // AI has weather information to display
                        const parts = data.response.split('WEATHER_DISPLAY_SHOW: ')[1];
                        const [location, weatherInfo] = parts.split('|');
                        
                        console.log(`[WEATHER] Extracting weather from: "${weatherInfo}"`);
                        
                        let weatherData = null;
                        let chatMessage = weatherInfo;
                        
                        // Check if we have separate weather data
                        if (weatherInfo.includes('WEATHER_DATA:')) {
                            const parts = weatherInfo.split('WEATHER_DATA:');
                            chatMessage = parts[0].trim();
                            const weatherJson = parts[1].trim();
                            console.log(`[WEATHER] Found weather data: "${weatherJson}"`);
                            
                            try {
                                weatherData = JSON.parse(weatherJson);
                            } catch (parseError) {
                                console.error('❌ Failed to parse weather JSON:', parseError);
                            }
                        } else {
                            // Fallback: extract weather from message
                            weatherData = extractWeatherFromText(weatherInfo, location.trim());
                        }
                        
                        if (weatherData && location) {
                            showWeatherDisplay(weatherData, location.trim());
                            startWeatherUpdates(location.trim());
                            // Don't add any chat message - show only the weather widget
                            console.log(`✅ Weather widget displayed for ${location.trim()}`);
                        } else {
                            console.error(`❌ Failed to extract weather from: "${weatherInfo}"`);
                            addMessage(`Sorry, I couldn't get weather information for that location.`, false);
                        }
                        
                    } else if (data.response.includes('NEWS_DISPLAY_SHOW:')) {
                        // AI has news information to display  
                        const newsContent = data.response.split('NEWS_DISPLAY_SHOW: ')[1];
                        console.log(`[NEWS] Processing news content: "${newsContent.substring(0, 100)}..."`);
                        
                        // Update the news widget with the content
                        updateNewsWidget(newsContent);
                        
                        // Also show a brief chat message
                        addMessage("Here are the latest news headlines:", false);
                        
                    } else if (data.response.includes('SEARCH_RESULT:')) {
                        // AI has search results to display
                        const searchContent = data.response.replace('SEARCH_RESULT: ', '');
                        console.log(`[SEARCH] Processing search result: "${searchContent.substring(0, 100)}..."`);
                        
                        // Check if this is news-related content
                        const isNewsContent = isNewsContentDetected(message, searchContent);
                        
                        if (isNewsContent) {
                            console.log(`[NEWS] Search result contains news content, displaying in news widget`);
                            // Display in news widget
                            updateNewsWidget(searchContent);
                            addMessage("Here are the latest news updates:", false);
                        } else {
                            // Update search widget for non-news content
                            updateSearchWidget(searchContent);
                        }
                        
                    } else if (isNewsRequest(message) && data.response) {
                        // Handle news requests - check if response contains news content
                        console.log(`[NEWS] Detected news request, checking response...`);
                        updateNewsWidget(data.response);
                        addMessage("Here's the news information:", false);
                        
                    } else {
                        // Normal AI response
                        addMessage(data.response, false);
                    }
                } else {
                    addMessage(`Error: ${data.error || 'Unknown error occurred'}`, false);
                }
            } catch (error) {
                const errorTime = Date.now() - startTime;
                console.error(`❌ Failed after ${errorTime}ms:`, error);
                hideTypingIndicator();
                addMessage(`Connection error: ${error.message}`, false);
            }
        }

        function updateTimeLabelFromAI(aiText) {
            const timeRegex = /The current time in ([\w\s]+) is (\d{1,2}:\d{2}(?:\s?(?:AM|PM|am|pm))?)/i;
            const match = aiText.match(timeRegex);
            if (match) {
                showTimeDisplay(match[2], match[1].toLowerCase());
                startTimeUpdates(match[1].toLowerCase());
                return;
            }
        }

        function showLocalTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                second: '2-digit',
                hour12: true
            });
            
            showTimeDisplay(timeString, 'local');
            
            // Update local time every second for real-time display
            stopTimeUpdates();
            timeUpdateInterval = setInterval(() => {
                const now = new Date();
                const timeString = now.toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: true
                });
                updateTimeDisplay(timeString);
                console.log(`🕐 Local time updated: ${timeString}`);
            }, 1000);
        }

        function hideTimeDisplay() {
            const timeDisplay = document.getElementById('timeDisplay');
            stopTimeUpdates();
            timeDisplay.style.display = 'none';
            timeDisplay.style.transition = 'none';
        }

        // ================= MODERN CHAT FUNCTIONALITY =================

        // Open/Close Modern Chat Interface
        function openModernChat() {
            isChatOpen = true;
            modernChatInterface.classList.add('active');
            floatingChatButton.style.display = 'none';
            
            // Focus on input
            setTimeout(() => {
                modernChatInput.focus();
            }, 400);
            
            console.log('Modern chat interface opened');
        }

        function closeModernChat() {
            isChatOpen = false;
            isChatMinimized = false;
            modernChatInterface.classList.remove('active', 'minimized');
            floatingChatButton.style.display = 'flex';
            
            console.log('Modern chat interface closed');
        }

        function minimizeModernChat() {
            if (isChatMinimized) {
                // Restore
                isChatMinimized = false;
                modernChatInterface.classList.remove('minimized');
                modernChatInput.focus();
            } else {
                // Minimize
                isChatMinimized = true;
                modernChatInterface.classList.add('minimized');
            }
            
            console.log('Modern chat interface', isChatMinimized ? 'minimized' : 'restored');
        }

        // Add message to modern chat
        function addModernChatMessage(text, isUser = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-message ${isUser ? 'user' : ''}`;
            
            const avatarDiv = document.createElement('div');
            avatarDiv.className = `message-avatar ${isUser ? 'user-avatar' : 'nova-avatar-small'}`;
            avatarDiv.textContent = isUser ? 'U' : ''; // Removed "N" for cleaner interface
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            
            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'message-bubble';
            bubbleDiv.textContent = text;
            
            contentDiv.appendChild(bubbleDiv);
            messageDiv.appendChild(avatarDiv);
            messageDiv.appendChild(contentDiv);
            
            modernChatMessages.appendChild(messageDiv);
            
            // Scroll to bottom
            modernChatMessages.scrollTop = modernChatMessages.scrollHeight;
            
            console.log('Added message to modern chat:', isUser ? 'User' : 'NOVA');
        }

        // Modern chat typing indicator
        let modernTypingElement = null;

        function showModernChatTyping() {
            if (modernTypingElement) return; // Already showing

            modernTypingElement = document.createElement('div');
            modernTypingElement.className = 'chat-message typing-message';
            
            const avatarDiv = document.createElement('div');
            avatarDiv.className = 'message-avatar nova-avatar-small';
            avatarDiv.textContent = ''; // Removed "N" for cleaner interface
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            
            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'message-bubble typing-bubble';
            
            const dotsDiv = document.createElement('div');
            dotsDiv.className = 'typing-dots';
            dotsDiv.innerHTML = '<div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div>';
            
            bubbleDiv.appendChild(dotsDiv);
            contentDiv.appendChild(bubbleDiv);
            modernTypingElement.appendChild(avatarDiv);
            modernTypingElement.appendChild(contentDiv);
            
            modernChatMessages.appendChild(modernTypingElement);
            modernChatMessages.scrollTop = modernChatMessages.scrollHeight;
            
            console.log('Showing typing indicator in modern chat');
        }

        function hideModernChatTyping() {
            if (modernTypingElement) {
                modernTypingElement.remove();
                modernTypingElement = null;
                console.log('Hiding typing indicator in modern chat');
            }
        }

        // Helper functions for modern chat integration
        function displayTimeForLocation(location) {
            // Show the time widget
            showTimeDisplay(null, location);
            console.log(`Displaying time for ${location}`);
        }

        function displayWeatherForLocation(location) {
            // Show the weather widget - integrate with existing weather functionality
            const weatherWidget = document.getElementById('weatherWidget');
            if (weatherWidget) {
                weatherWidget.style.display = 'block';
            }
            console.log(`Displaying weather for ${location}`);
        }

        function performCalculation(calculation) {
            // Show the calculator widget
            showCalculatorWidget();
            console.log(`Performing calculation: ${calculation}`);
        }

        // Functions to send widget requests to AI backend
        async function sendNewsRequestToAI(message) {
            try {
                const requestBody = { 
                    message: message, 
                    session_id: sessionId 
                };
                
                if (userLocation) {
                    requestBody.user_location = userLocation;
                }
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache'
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();
                
                if (response.ok && data.response) {
                    // Handle news response and update widget
                    updateNewsWidget(data.response);
                    
                    // Add response to chat
                    if (isChatOpen) {
                        addModernChatMessage("Here's the latest news for you!", false);
                    }
                }
            } catch (error) {
                console.error('Error fetching news:', error);
                if (isChatOpen) {
                    addModernChatMessage("Sorry, I couldn't fetch the news right now. Please try again.", false);
                }
            }
        }

        async function sendWeatherRequestToAI(message) {
            try {
                const requestBody = { 
                    message: message, 
                    session_id: sessionId 
                };
                
                if (userLocation) {
                    requestBody.user_location = userLocation;
                }
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache'
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();
                
                if (response.ok && data.response) {
                    // Handle weather response and update widget
                    updateWeatherWidget(data.response);
                                    // Add response to chat
                    if (isChatOpen) {
                        addModernChatMessage("Here's the current weather information!", false);
                    }
                }
            } catch (error) {
                console.error('Error fetching weather:', error);
                if (isChatOpen) {
                    addModernChatMessage("Sorry, I couldn't fetch the weather right now. Please try again.", false);
                }
    
            }
        }

        async function sendSearchRequestToAI(message) {
            try {
                const requestBody = { 
                    message: message, 
                    session_id: sessionId 
                };
                
                if (userLocation) {
                    requestBody.user_location = userLocation;
                }
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache'
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();
                
                if (response.ok && data.response) {
                    // Handle search response and update widget
                    updateSearchWidget(data.response);
                    
                    // Add response to chat
                    if (isChatOpen) {
                        addModernChatMessage("Here are the search results for you!", false);
                    }
                }
            } catch (error) {
                console.error('Error fetching search results:', error);
                if (isChatOpen) {
                    addModernChatMessage("Sorry, I couldn't search for that right now. Please try again.", false);
                }
            }
        }

        // Handle modern chat message sending
        function handleModernChatSend() {
            const message = modernChatInput.value.trim();
            if (!message) return;
            
            // Add user message
            addModernChatMessage(message, true);
            
            // Clear input
            modernChatInput.value = '';
            
            // Process the message (integrate with existing chat logic)
            handleSendMessage(message);
            
            console.log('Modern chat message sent:', message);
        }

        // Enhanced handleSendMessage to work with both interfaces
        function handleSendMessage(messageOverride = null) {
            const message = messageOverride || chatInput.value.trim() || modernChatInput.value.trim();
            if (!message) return;

            console.log('Processing message:', message);

            // Clear original input if it was used
            if (!messageOverride) {
                chatInput.value = '';
                modernChatInput.value = '';
            }

            // Add to original chat if visible
            if (chatMessages && document.getElementById('originalChatContainer').style.display !== 'none') {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message user-message';
                const messageText = document.createElement('div');
                messageText.className = 'message-text';
                messageText.textContent = message;
                messageDiv.appendChild(messageText);
                chatMessages.appendChild(messageDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }

            // Add to modern chat if open
            if (isChatOpen && !messageOverride) {
                // User message already added in handleModernChatSend
            }

            // Process the message with existing logic
            processUserMessage(message);
        }

        // Process user message (integrate with existing functionality)
        async function processUserMessage(message) {
            if (!isChatOpen) return;

            console.log(`Processing message in modern chat: "${message}"`);
            
            // Check for direct widget commands first
            const lowerMessage = message.toLowerCase();
            
            // Time requests
            if (lowerMessage.includes('time') || lowerMessage.includes('clock')) {
                // Show time widget and load current time
                showTimeDisplay();
                showLocalTime(); // Load actual current time
                addModernChatMessage("I've displayed the current time for you!", false);
                return;
            }
            
            // News requests  
            if (lowerMessage.includes('news') || lowerMessage.includes('headlines')) {
                // Show loading state and fetch actual news
                showNewsLoading();
                addModernChatMessage("Getting the latest news for you...", false);
                
                // Send news request to AI backend to get real content
                sendNewsRequestToAI(message);
                return;
            }
            
            // Weather requests
            if (lowerMessage.includes('weather') || lowerMessage.includes('forecast')) {
                addModernChatMessage("Getting weather information for you...", false);
                
                // Send weather request to AI backend to get real content
                sendWeatherRequestToAI(message);
                return;
            }
            
            // Calculator requests
            if (lowerMessage.includes('calculator') || lowerMessage.includes('calculate') || lowerMessage.includes('math')) {
                showCalculatorWidget();
                addModernChatMessage("I've opened the calculator for you!", false);
                return;
            }
            
            // Notepad requests
            if (lowerMessage.includes('notepad') || lowerMessage.includes('notes') || lowerMessage.includes('note pad') || 
                lowerMessage.includes('open notepad') || lowerMessage.includes('show notepad') || lowerMessage.includes('take notes')) {
                showNotepadWidget();
                addModernChatMessage("I've opened the notepad for you! You can start typing your notes.", false);
                return;
            }
            
            // Search requests
            if (lowerMessage.includes('search') || lowerMessage.includes('find') || lowerMessage.includes('look up')) {
                // Show loading state and fetch search results
                showSearchLoading();
                addModernChatMessage("Searching for information...", false);
                
                // Send search request to AI backend to get real content
                sendSearchRequestToAI(message);
                return;
            }

            const startTime = Date.now();

            try {
                // Show typing indicator
                showModernChatTyping();

                // Prepare request body (same as existing API call)
                const requestBody = { 
                    message: message, 
                    session_id: sessionId 
                };
                
                // Add location context if available
                if (userLocation) {
                    requestBody.user_location = userLocation;
                    console.log(`📍 Sending user location: ${userLocation}`);
                }
                
                // Make API call to actual backend
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache'
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();
                const responseTime = Date.now() - startTime;
                
                console.log(`Modern chat response in ${responseTime}ms`);
                hideModernChatTyping();

                if (data.session_id) sessionId = data.session_id;

                if (response.ok && data.response) {
                    console.log(`AI Response: ${data.response.substring(0, 50)}...`);
                    
                    // Handle special response types for widget display
                    if (data.response.includes('SEARCH_RESULT:')) {
                        const searchResult = data.response.replace('SEARCH_RESULT: ', '');
                        updateSearchWidget(searchResult);
                        addModernChatMessage("I've found detailed search results for you! Check the search widget for complete information with all the details.", false);
                        return;
                    }
                    
                    if (data.response.includes('NEWS_RESULT:')) {
                        const newsResult = data.response.replace('NEWS_RESULT: ', '');
                        updateNewsWidget(newsResult);
                        addModernChatMessage("I've fetched the latest news with full details! Check the news widget for complete headlines, sources, and content.", false);
                        return;
                    }
                    
                    if (data.response.includes('NEWS_DISPLAY_SHOW:')) {
                        const newsContent = data.response.split('NEWS_DISPLAY_SHOW: ')[1];
                        updateNewsWidget(newsContent);
                        addModernChatMessage("Here are the latest news headlines with complete information! Check the news widget for all details and sources.", false);
                        return;
                    }
                    
                    if (data.response.includes('TIME_DISPLAY_REQUEST:')) {
                        const locationMatch = data.response.match(/TIME_DISPLAY_REQUEST:\s*(.+)/);
                        if (locationMatch) {
                            const timeLocation = locationMatch[1].trim();
                            displayTimeForLocation(timeLocation);
                            addModernChatMessage(`I've displayed the current time for ${timeLocation}!`, false);
                            return;
                        }
                    }
                    
                    if (data.response.includes('WEATHER_DISPLAY_REQUEST:')) {
                        const locationMatch = data.response.match(/WEATHER_DISPLAY_REQUEST:\s*(.+)/);
                        if (locationMatch) {
                            const weatherLocation = locationMatch[1].trim();
                            displayWeatherForLocation(weatherLocation);
                            addModernChatMessage(`I've displayed the weather information for ${weatherLocation}!`, false);
                            return;
                        }
                    }
                    
                    if (data.response.includes('CALCULATOR_DISPLAY_REQUEST:')) {
                        const calculationMatch = data.response.match(/CALCULATOR_DISPLAY_REQUEST:\s*(.+)/);
                        if (calculationMatch) {
                            const calculation = calculationMatch[1].trim();
                            showCalculatorWidget();
                            performCalculation(calculation);
                            addModernChatMessage(`I've opened the calculator and performed your calculation!`, false);
                            return;
                        }
                    // Check if user is asking for summary and there's content to summarize
                    const lowerMessage = message.toLowerCase();
                    if (lowerMessage.includes('summarize') || lowerMessage.includes('summary')) {
                        // AI should provide the summary in the chat interface
                        addModernChatMessage(data.response, false);
                        return;
                    }
                    
                    }
                    
                    // Normal response - add to chat
                    addModernChatMessage(data.response, false);
                    
                } else {
                    console.error('API Error:', data);
                    addModernChatMessage("I'm sorry, I encountered an issue processing your request. Please try again.", false);
                }
                
            } catch (error) {
                console.error('Modern chat error:', error);
                hideModernChatTyping();
                addModernChatMessage("I'm having trouble connecting right now. Please check your connection and try again.", false);
            }
        }

        // Generate NOVA response (integrate with existing logic)
        function generateNovaResponse(message) {
            const lowerMessage = message.toLowerCase();
            
            // Check for news requests
            if (lowerMessage.includes('news') || lowerMessage.includes('headlines')) {
                showNewsWidget();
                return "I'm fetching the latest news for you! Check out the news widget that just appeared.";
            }

            // Check for notes requests
            if (lowerMessage.includes('note') || lowerMessage.includes('write') || lowerMessage.includes('notepad') || lowerMessage.includes('memo')) {
                showNotesWidget();
                return "I've opened the notes widget for you! You can write notes, save them, and view your saved notes.";
            }

            // Check for weather requests
            if (lowerMessage.includes('weather') || lowerMessage.includes('temperature')) {
                return "I can help you with weather information! Let me get that for you.";
            }
            
            // Check for calculator requests
            if (lowerMessage.includes('calculate') || lowerMessage.includes('math')) {
                return "I can help you with calculations! The calculator widget is available for complex calculations.";
            }
            
            // Check for time requests
            if (lowerMessage.includes('time') || lowerMessage.includes('clock')) {
                return "I can show you the current time! Let me display that for you.";
            }
            
            // Default responses
            const responses = [
                "I understand your request. Let me help you with that.",
                "That's an interesting question! Let me think about it.",
                "I'm here to assist you. How can I help further?",
                "Great question! I'm processing that information now.",
                "I'll do my best to help you with that request."
            ];
            return responses[Math.floor(Math.random() * responses.length)];
        }

        // Event listeners for modern chat
        floatingChatButton.addEventListener('click', openModernChat);
        chatCloseBtn.addEventListener('click', closeModernChat);
        chatMinimizeBtn.addEventListener('click', minimizeModernChat);
        modernSendButton.addEventListener('click', handleModernChatSend);

        modernChatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleModernChatSend();
            }
        });

        // Event listeners for original chat
        sendButton.addEventListener('click', handleSendMessage);

        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleSendMessage();
            }
        });

        // Auto-focus on input (only if original chat is visible)
        if (document.getElementById('originalChatContainer').style.display !== 'none') {
            chatInput.focus();
        }

        // ================= MODERN CHAT INITIALIZATION =================
        
        // Initialize modern chat system
        function initializeModernChat() {
            console.log('Modern chat system initialized');
            
            // Add keyboard shortcuts
            document.addEventListener('keydown', (e) => {
                // Escape to close chat
                if (e.key === 'Escape' && isChatOpen) {
                    closeModernChat();
                }
                
                // Ctrl/Cmd + M to toggle chat
                if ((e.ctrlKey || e.metaKey) && e.key === 'm') {
                    e.preventDefault();
                    if (isChatOpen) {
                        if (isChatMinimized) {
                            minimizeModernChat(); // Restore
                        } else {
                            minimizeModernChat(); // Minimize
                        }
                    } else {
                        openModernChat();
                    }
                }
            });
            
            // Start floating animation
            setTimeout(() => {
                floatingChatButton.style.animation = 'chatButtonFloat 3s ease-in-out infinite alternate';
            }, 1000);
        }

        // Test functions for modern chat
        window.testModernChat = function() {
            console.log('Testing modern chat system...');
            openModernChat();
            
            setTimeout(() => {
                addModernChatMessage("Hello! This is a test message from NOVA AI.", false);
            }, 1000);
            
            setTimeout(() => {
                addModernChatMessage("The modern chat interface is working perfectly! Try typing a message to test the full AI integration.", false);
            }, 2500);

            setTimeout(() => {
                addModernChatMessage("You can ask me about:\n• Latest news\n• Weather information\n• Time in different locations\n• Calculations\n• Or anything else!", false);
            }, 4000);
        };

        window.showModernChat = function() {
            openModernChat();
        };

        window.hideModernChat = function() {
            closeModernChat();
        };

        // Initialize modern chat when page loads
        initializeModernChat();

        // Initialize location detection on page load
        if (!userLocation) {
            setTimeout(() => {
                detectUserLocationFromIP();
            }, 2000);
        } else {
            console.log(`User location loaded: ${userLocation}`);
        }

        // ================= WEATHER DISPLAY FUNCTIONS =================
        
        function showWeatherDisplay(weatherData, location = null) {
            const weatherDisplay = document.getElementById('weatherDisplay');
            
            console.log(`🌤️ SHOWING WEATHER: ${location} - ${weatherData.temperature}°C`);
            
            // Update weather display with data
            updateWeatherDisplay(weatherData);
            
            // Show the display with animation
            weatherDisplay.style.cssText = `
                display: block !important;
                opacity: 1 !important;
                transform: scale(1) translateY(0) !important;
                transition: all 0.3s ease !important;
                animation: weatherGlow 3s ease-in-out infinite !important;
                visibility: visible !important;
            `;
            
            // Set current location and start updates
            if (location) {
                currentWeatherLocation = location;
            }
            
            // Start weather updates
            startWeatherUpdates(location);
        }

        function updateWeatherDisplay(weather) {
            document.getElementById('weatherTemp').textContent = `${weather.temperature}°C`;
            document.getElementById('weatherFeelsLike').textContent = `Feels like ${weather.feelsLike}°C`;
            
            // Update condition with emoji
            const conditionElement = document.getElementById('weatherCondition');
            conditionElement.innerHTML = `
                <span class="weather-condition-emoji">${weather.conditionEmoji}</span>
                <span>${weather.condition}</span>
            `;
            
            document.getElementById('weatherIcon').textContent = weather.icon;
            document.getElementById('weatherHighLow').textContent = `${weather.high}° / ${weather.low}°`;
            
            // Update humidity with emoji
            const humidityElement = document.getElementById('weatherHumidity');
            humidityElement.innerHTML = `
                <span class="weather-detail-emoji">💧</span>
                <span>${weather.humidity}%</span>
            `;
            
            document.getElementById('weatherUV').textContent = `${weather.uvIndex} ${getUVLevel(weather.uvIndex)}`;
            document.getElementById('weatherVisibility').textContent = `${weather.visibility} mi`;
            
            // Update wind with emoji
            const windElement = document.getElementById('weatherWind');
            windElement.innerHTML = `
                <span class="weather-extended-emoji">💨</span>
                <span>${weather.windSpeed} mph ${weather.windDirection}</span>
            `;
            
            document.getElementById('weatherPressure').textContent = `${weather.pressure} in`;
            document.getElementById('weatherDewPoint').textContent = `${weather.dewPoint}°C`;
            
            // Update sunrise/sunset
            const sunriseElement = document.getElementById('weatherSunrise');
            sunriseElement.innerHTML = `
                <span class="weather-sun-emoji">🌅</span>
                <span>${weather.sunrise}</span>
            `;
            
            const sunsetElement = document.getElementById('weatherSunset');
            sunsetElement.innerHTML = `
                <span class="weather-sun-emoji">🌇</span>
                <span>${weather.sunset}</span>
            `;
            
            document.getElementById('weatherLocation').textContent = weather.location;
        }

        function getUVLevel(uvIndex) {
            if (uvIndex <= 2) return 'Low';
            if (uvIndex <= 5) return 'Moderate';
            if (uvIndex <= 7) return 'High';
            if (uvIndex <= 10) return 'Very High';
            return 'Extreme';
        }

        function startWeatherUpdates(location) {
            // Stop any existing weather updates
            stopWeatherUpdates();
            
            if (!location) {
                console.warn('No location provided for weather updates');
                return;
            }
            
            currentWeatherLocation = location;
            console.log(`🌤️ Starting weather updates for: ${location}`);
            
            // Update weather every 5 minutes
            weatherDisplayInterval = setInterval(async () => {
                if (currentWeatherLocation) {
                    console.log(`🌤️ Refreshing weather for ${currentWeatherLocation}...`);
                    
                    try {
                        const weatherData = await getWeatherForLocation(currentWeatherLocation, true);
                        if (weatherData) {
                            updateWeatherDisplay(weatherData);
                            console.log(`✅ Weather refreshed for ${currentWeatherLocation}`);
                        }
                    } catch (error) {
                        console.error('❌ Weather refresh error:', error);
                    }
                }
            }, 300000); // 5 minutes
        }

        function stopWeatherUpdates() {
            if (weatherDisplayInterval) {
                clearInterval(weatherDisplayInterval);
                weatherDisplayInterval = null;
            }
            currentWeatherLocation = null;
            console.log('⏹️ Weather updates stopped');
        }

        function hideWeatherDisplay() {
            const weatherDisplay = document.getElementById('weatherDisplay');
            stopWeatherUpdates();
            weatherDisplay.style.display = 'none';
            weatherDisplay.style.transition = 'none';
        }

        async function getWeatherForLocation(location, forceRefresh = false) {
            const cacheKey = location.toLowerCase();
            const now = Date.now();
            
            console.log(`🌤️ getWeatherForLocation called with: "${location}", forceRefresh: ${forceRefresh}`);
            
            // Check cache unless force refresh
            if (!forceRefresh && weatherCache[cacheKey] && (now - weatherCache[cacheKey].timestamp < WEATHER_CACHE_DURATION)) {
                console.log(`📋 Using cached weather for ${location}`);
                return weatherCache[cacheKey].data;
            }
            
            try {
                console.log(`🌐 Making API request for weather in: ${location}`);
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache'
                    },
                    body: JSON.stringify({ 
                        message: `What is the current weather in ${location}? Please provide comprehensive weather data including temperature, condition, humidity, wind, pressure, UV index, visibility, sunrise/sunset times, and dew point. Format as WEATHER_DATA: [JSON data]. Timestamp: ${Date.now()}`,
                        session_id: sessionId 
                    })
                });
                
                const data = await response.json();
                console.log(`📡 Weather API response for ${location}:`, data.response?.substring(0, 100) + '...');
                
                if (data.response) {
                    // Check if we have weather data format
                    if (data.response.includes('WEATHER_DATA:')) {
                        const parts = data.response.split('WEATHER_DATA:');
                        const weatherJson = parts[1].trim();
                        console.log(`🌤️ Found weather data: ${weatherJson}`);
                        
                        try {
                            const weatherData = JSON.parse(weatherJson);
                            
                            // Cache the weather data
                            weatherCache[cacheKey] = {
                                data: weatherData,
                                timestamp: now
                            };
                            
                            return weatherData;
                        } catch (parseError) {
                            console.error('❌ Failed to parse weather JSON:', parseError);
                        }
                    }
                    
                    // Fallback: Try to extract weather from text response
                    const weatherData = extractWeatherFromText(data.response, location);
                    if (weatherData) {
                        weatherCache[cacheKey] = {
                            data: weatherData,
                            timestamp: now
                        };
                        return weatherData;
                    }
                }
            } catch (error) {
                console.error(`❌ Error getting weather for ${location}:`, error);
            }
            
            console.error(`❌ Failed to get weather for: ${location}`);
            return null;
        }

        function extractWeatherFromText(text, location) {
            // Extract weather information from AI text response
            const tempMatch = text.match(/(\d+)°?F?/i);
            const conditionMatch = text.match(/(sunny|cloudy|rainy|clear|overcast|partly cloudy|scattered clouds|thunderstorm|light rain)/i);
            const humidityMatch = text.match(/(\d+)%/);
            const windMatch = text.match(/(\d+)\s*(?:mph|km\/h)/i);
            
            if (tempMatch) {
                const temp = parseInt(tempMatch[1]);
                const condition = conditionMatch ? conditionMatch[1] : 'Unknown';
                const humidity = humidityMatch ? parseInt(humidityMatch[1]) : 50;
                const windSpeed = windMatch ? parseInt(windMatch[1]) : 10;
                
                // Generate mock comprehensive data based on extracted info
                return generateMockWeatherData(temp, condition, humidity, windSpeed, location);
            }
            
            return null;
        }

        function generateMockWeatherData(temp, condition, humidity, windSpeed, location) {
            const conditions = {
                'sunny': { icon: '☀️', emoji: '☀️' },
                'cloudy': { icon: '☁️', emoji: '☁️' },
                'rainy': { icon: '🌧️', emoji: '🌧️' },
                'clear': { icon: '🌤️', emoji: '🌤️' },
                'overcast': { icon: '☁️', emoji: '☁️' },
                'partly cloudy': { icon: '⛅', emoji: '⛅' },
                'scattered clouds': { icon: '⛅', emoji: '🌪️' },
                'thunderstorm': { icon: '⛈️', emoji: '⛈️' },
                'light rain': { icon: '🌦️', emoji: '🌦️' }
            };
            
            const conditionInfo = conditions[condition.toLowerCase()] || { icon: '🌤️', emoji: '🌤️' };
            const windDirections = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'];
            const windDirection = windDirections[Math.floor(Math.random() * windDirections.length)];
            
            // Generate realistic sunrise/sunset times
            const sunriseHour = Math.floor(Math.random() * 3) + 6; // 6-8 AM
            const sunriseMinute = Math.floor(Math.random() * 60);
            const sunsetHour = Math.floor(Math.random() * 3) + 18; // 6-8 PM
            const sunsetMinute = Math.floor(Math.random() * 60);
            
            return {
                temperature: temp,
                feelsLike: temp + Math.round(Math.random() * 8 - 4),
                condition: condition,
                icon: conditionInfo.icon,
                conditionEmoji: conditionInfo.emoji,
                high: temp + Math.round(Math.random() * 10 + 5),
                low: temp - Math.round(Math.random() * 15 + 5),
                humidity: humidity,
                uvIndex: Math.round(Math.random() * 10),
                visibility: Math.round(Math.random() * 10 + 5), // 5-15 miles
                windSpeed: windSpeed,
                windDirection: windDirection,
                pressure: (29.5 + Math.random() * 1.5).toFixed(2), // 29.5-31.0 inches
                dewPoint: temp - Math.round(Math.random() * 20 + 10),
                sunrise: `${sunriseHour.toString().padStart(2, '0')}:${sunriseMinute.toString().padStart(2, '0')}`,
                sunset: `${sunsetHour.toString().padStart(2, '0')}:${sunsetMinute.toString().padStart(2, '0')}`,
                location: location
            };
        }

        // ================= SEARCH WIDGET FUNCTIONS =================
        
        function showSearchWidget() {
            searchWidget.style.display = 'block';
            console.log('🔍 Search widget shown');
        }

        function hideSearchWidget() {
            searchWidget.style.display = 'none';
            console.log('🔍 Search widget hidden');
        }

        function showSearchLoading() {
            searchContent.style.display = 'none';
            searchLoading.style.display = 'block';
            showSearchWidget();
            console.log('🔍 Search loading shown');
        }

        function hideSearchLoading() {
            searchContent.style.display = 'block';
            searchLoading.style.display = 'none';
            console.log('🔍 Search loading hidden');
        }

        async function updateSearchWidget(text) {
            hideSearchLoading();
            
            try {
                // Clear the search content and rebuild with text only
                searchContent.innerHTML = '';
                
                // Create a container for the content
                const contentContainer = document.createElement('div');
                contentContainer.style.cssText = `
                    display: flex;
                    flex-direction: column;
                    gap: 12px;
                    padding: 8px;
                    position: relative;
                `;
                
                // Add the text content only (no images)
                const textElement = document.createElement('div');
                textElement.style.cssText = `
                    font-size: 13px;
                    line-height: 1.4;
                    color: rgba(255, 255, 255, 0.9);
                    text-align: left;
                    white-space: pre-wrap;
                    word-wrap: break-word;
                `;
                textElement.textContent = text;
                contentContainer.appendChild(textElement);
                
                // Add the content to the widget
                searchContent.appendChild(contentContainer);

                // Remove any existing copy button first
                const existingCopyButton = searchWidget.querySelector('.copy-button');
                if (existingCopyButton) {
                    existingCopyButton.remove();
                }

                // Add copy button to the widget (not inside content)
                const copyButton = document.createElement('button');
                copyButton.className = 'copy-button';
                copyButton.title = 'Copy search results';
                copyButton.innerHTML = `
                    <svg viewBox="0 0 24 24">
                        <path d="M16 1H4C2.9 1 2 1.9 2 3V15H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z"/>
                    </svg>
                `;
                copyButton.addEventListener('click', () => copySearchContent(text, copyButton));
                searchWidget.appendChild(copyButton);
                
                showSearchWidget();
                
                // Add a subtle animation to indicate new search result
                searchWidget.style.animation = 'none';
                setTimeout(() => {
                    searchWidget.style.animation = 'searchGlow 3s ease-in-out infinite';
                }, 10);
                
                console.log('🔍 Search widget updated with full content (no images):', text.substring(0, 50) + '...');
                
            } catch (error) {
                console.error('❌ Error updating search widget:', error);
                // Fallback to simple text display
                searchContent.textContent = text;
                showSearchWidget();
                
                searchWidget.style.animation = 'none';
                setTimeout(() => {
                    searchWidget.style.animation = 'searchGlow 3s ease-in-out infinite';
                }, 10);
                
                console.log('🔍 Fallback search widget updated:', text.substring(0, 50) + '...');
            }
        }

        // Helper function to extract main topic from text for image search
        function extractMainTopicFromText(text) {
            const lines = text.split('\n');
            const firstLine = lines[0] || text;
            
            // Remove common prefixes and clean up
            let topic = firstLine
                .replace(/^(Search results?|Results?|Information|About|Here's what I found|Found:|Results for):\s*/i, '')
                .replace(/^(.*?about|.*?for|.*?on)\s+/i, '')
                .substring(0, 100)
                .trim();
            
            // If topic is still too generic, try to extract key words from full text
            if (topic.length < 5 || topic.toLowerCase().includes('search') || topic.toLowerCase().includes('result')) {
                // Look for important words in the full text
                const importantWords = text.match(/\b(?:AI|artificial intelligence|machine learning|technology|science|climate|energy|space|health|business|politics|education|sports|environment|cryptocurrency|quantum|biotechnology|research|innovation|development|system|company|study|project|program|market|economy|government|university|hospital|laboratory|discovery|breakthrough|solution|analysis|report|data|information|news|update|announcement|launch|release|investment|funding|growth|expansion|partnership|collaboration|agreement|deal|contract|acquisition|merger|IPO|stock|shares|revenue|profit|loss|earnings|financial|economic|global|international|national|local|regional|world|country|state|city|community|society|culture|social|environmental|sustainable|renewable|clean|green|electric|solar|wind|nuclear|fossil|oil|gas|coal|carbon|emissions|pollution|waste|recycling|conservation|protection|wildlife|nature|ocean|forest|agriculture|farming|food|nutrition|medicine|pharmaceutical|vaccine|treatment|therapy|diagnosis|disease|virus|bacteria|infection|pandemic|epidemic|surgery|hospital|clinic|doctor|nurse|patient|healthcare|fitness|exercise|diet|mental|psychological|brain|neuroscience|genetics|DNA|gene|protein|cell|molecular|biology|chemistry|physics|mathematics|engineering|computer|software|hardware|internet|web|mobile|app|platform|service|cloud|database|algorithm|network|security|privacy|cyber|digital|virtual|augmented|reality|blockchain|cryptocurrency|bitcoin|ethereum|NFT|metaverse|gaming|entertainment|media|television|film|music|art|design|architecture|construction|infrastructure|transportation|automotive|aviation|shipping|logistics|supply|chain|manufacturing|production|factory|automation|robotics|drone|satellite|communication|telecommunication|broadcasting|journalism|publishing|education|school|college|university|student|teacher|professor|research|academic|scientific|experiment|test|trial|study|survey|poll|election|vote|democracy|politics|government|policy|law|legal|court|justice|crime|police|military|defense|security|war|peace|conflict|diplomacy|trade|commerce|business|industry|sector|market|economy|finance|banking|insurance|investment|startup|entrepreneur|innovation|patent|intellectual|property|copyright|trademark|licensing|regulation|compliance|standard|certification|quality|safety|risk|management|leadership|strategy|planning|execution|implementation|performance|efficiency|productivity|optimization|improvement|transformation|change|evolution|revolution|disruption|trend|future|prediction|forecast|projection|estimate|analysis|evaluation|assessment|measurement|metric|indicator|benchmark|comparison|competition|advantage|opportunity|challenge|problem|solution|answer|question|issue|topic|subject|matter|concern|interest|focus|priority|goal|objective|target|milestone|achievement|success|failure|mistake|error|bug|fix|update|upgrade|version|release|launch|debut|introduction|announcement|declaration|statement|comment|opinion|view|perspective|approach|method|technique|strategy|tactic|plan|process|procedure|protocol|guideline|instruction|manual|guide|tutorial|course|training|workshop|seminar|conference|meeting|event|ceremony|celebration|festival|competition|championship|tournament|match|game|sport|exercise|activity|hobby|interest|passion|talent|skill|ability|expertise|knowledge|wisdom|intelligence|creativity|imagination|innovation|invention|discovery|exploration|investigation|research|development|progress|advancement|improvement|enhancement|optimization|refinement|perfection|excellence|quality|standard|benchmark|criteria|requirement|specification|feature|function|capability|capacity|potential|possibility|opportunity|chance|likelihood|probability|risk|uncertainty|doubt|question|mystery|secret|revelation|disclosure|transparency|openness|honesty|integrity|trust|reliability|credibility|authenticity|genuineness|originality|uniqueness|distinctiveness|specialty|expertise|mastery|proficiency|competence|qualification|certification|accreditation|recognition|acknowledgment|appreciation|gratitude|thanks|praise|compliment|congratulations|celebration|success|achievement|accomplishment|victory|triumph|win|gain|benefit|advantage|profit|reward|prize|award|honor|recognition|fame|reputation|status|position|rank|level|grade|class|category|type|kind|sort|variety|diversity|difference|distinction|comparison|contrast|similarity|resemblance|connection|relation|relationship|association|partnership|collaboration|cooperation|teamwork|unity|harmony|balance|equilibrium|stability|consistency|reliability|dependability|trustworthiness|credibility|authenticity|genuineness|sincerity|honesty|integrity|morality|ethics|values|principles|beliefs|convictions|faith|hope|optimism|positivity|confidence|assurance|certainty|determination|perseverance|persistence|dedication|commitment|loyalty|devotion|passion|enthusiasm|excitement|energy|vitality|strength|power|force|influence|impact|effect|consequence|result|outcome|conclusion|end|finish|completion|achievement|success|victory|triumph|win|gain|benefit|advantage|progress|development|growth|expansion|increase|rise|improvement|enhancement|optimization|refinement|perfection|excellence|quality|standard|benchmark|criteria|requirement|specification|feature|function|capability|capacity|potential|possibility|opportunity|chance|likelihood|probability|risk|uncertainty|doubt|question|mystery|secret|revelation|disclosure|transparency|openness|honesty|integrity|trust|reliability|credibility|authenticity|genuineness|originality|uniqueness|distinctiveness|specialty|expertise|mastery|proficiency|competence|qualification|certification|accreditation|recognition|acknowledgment|appreciation|gratitude|thanks|praise|compliment|congratulations|celebration)\b/gi);
                
                if (importantWords && importantWords.length > 0) {
                    // Take the first few important words
                    topic = importantWords.slice(0, 3).join(' ');
                } else {
                    // Fall back to the first significant words
                    const words = text.split(/\s+/).filter(word => word.length > 3);
                    topic = words.slice(0, 3).join(' ');
                }
            }
            
            return topic || 'information';
        }

        function isSearchRequest(message) {
            const searchKeywords = [
                'search about', 'search for', 'look up', 'find information about',
                'search', 'google', 'look for', 'find about', 'research',
                'tell me about', 'what is', 'who is', 'where is', 'when is',
                'how to', 'why is', 'explain', 'information on', 'details about'
            ];
            
            const lowerMessage = message.toLowerCase();
            return searchKeywords.some(keyword => lowerMessage.includes(keyword));
        }

        function extractSearchQuery(message) {
            const lowerMessage = message.toLowerCase();
            
            // Try to extract the search query after common search phrases
            const patterns = [
                /search about (.+)/i,
                /search for (.+)/i,
                /look up (.+)/i,
                /find information about (.+)/i,
                /search (.+)/i,
                /google (.+)/i,
                /look for (.+)/i,
                /find about (.+)/i,
                /research (.+)/i,
                /tell me about (.+)/i,
                /what is (.+)/i,
                /who is (.+)/i,
                /where is (.+)/i,
                /when is (.+)/i,
                /how to (.+)/i,
                /why is (.+)/i,
                /explain (.+)/i,
                /information on (.+)/i,
                /details about (.+)/i
            ];
            
            for (const pattern of patterns) {
                const match = message.match(pattern);
                if (match && match[1]) {
                    return match[1].trim();
                }
            }
            
            return message; // Return original message if no pattern matches
        }

        // ================= MORE INFO DETECTION FUNCTIONS =================
        function isMoreInfoRequest_Function(message) {
            const moreInfoKeywords = [
                'more info', 'more information', 'tell me more', 'more details',
                'can you tell me more', 'give me more', 'more about', 'elaborate',
                'explain more', 'expand on', 'go deeper', 'more specifics',
                'additional info', 'additional information', 'further details',
                'anything else', 'what else', 'continue', 'keep going',
                'tell me about', 'explain that', 'clarify', 'expand',
                'more on that', 'dig deeper', 'more context', 'give me details',
                'show me more', 'i want more', 'need more', 'want to know more'
            ];
            
            const lowerMessage = message.toLowerCase();
            return moreInfoKeywords.some(keyword => lowerMessage.includes(keyword));
        }

        function isSearchRelated(message, response) {
            const searchKeywords = [
                'search', 'look up', 'find', 'information', 'research', 'study',
                'learn', 'discover', 'explore', 'investigate', 'examine'
            ];
            
            const lowerMessage = message.toLowerCase();
            const lowerResponse = response.toLowerCase();
            
            return searchKeywords.some(keyword => 
                lowerMessage.includes(keyword) || lowerResponse.includes(keyword)
            );
        }

        function isNewsRelated(message, response) {
            const newsKeywords = [
                'news', 'headlines', 'breaking', 'current events', 'latest',
                'happening', 'update', 'report', 'story', 'article', 'press'
            ];
            
            const lowerMessage = message.toLowerCase();
            const lowerResponse = response.toLowerCase();
            
            return newsKeywords.some(keyword => 
                lowerMessage.includes(keyword) || lowerResponse.includes(keyword)
            );
        }

        // ================= IMAGE FETCHING FUNCTIONS =================
        async function fetchImageForContent(query, type = 'general') {
            try {
                console.log(`🖼️ Fetching image for: "${query}" (type: ${type})`);
                
                // Extract meaningful keywords from the query
                const keywords = extractKeywordsFromQuery(query, type);
                console.log(`🔍 Extracted keywords: ${keywords}`);
                
                // Use different image sources based on content type
                let imageUrl;
                
                if (type === 'news') {
                    // For news, use more specific categories
                    imageUrl = await getNewsRelatedImage(keywords);
                } else if (type === 'search') {
                    // For search, use topic-based images
                    imageUrl = await getSearchRelatedImage(keywords);
                } else {
                    // General content
                    imageUrl = await getGeneralImage(keywords);
                }
                
                // Test if image loads
                return new Promise((resolve) => {
                    const img = new Image();
                    img.onload = () => {
                        console.log(`✅ Image loaded successfully: ${imageUrl}`);
                        resolve(imageUrl);
                    };
                    img.onerror = () => {
                        console.log(`❌ Image failed to load, using fallback`);
                        const fallbackUrl = getFallbackImage(type);
                        resolve(fallbackUrl);
                    };
                    img.src = imageUrl;
                });
                
            } catch (error) {
                console.error('Error fetching image:', error);
                return getFallbackImage(type);
            }
        }

        // Extract meaningful keywords from query
        function extractKeywordsFromQuery(query, type) {
            // Clean and normalize the query
            let cleanQuery = query.toLowerCase()
                .replace(/[^\w\s]/g, ' ')
                .replace(/\s+/g, ' ')
                .trim();
            
            // Remove common stopwords
            const stopwords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'up', 'down', 'out', 'off', 'over', 'under', 'again', 'further', 'then', 'once', 'here', 'there', 'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very', 'can', 'will', 'just', 'should', 'now'];
            
            const words = cleanQuery.split(' ').filter(word => 
                word.length > 2 && !stopwords.includes(word)
            );
            
            // Return the most relevant keywords (first 2-3 words)
            return words.slice(0, 3).join(' ') || cleanQuery.split(' ')[0] || 'information';
        }

        // Get news-related images
        async function getNewsRelatedImage(keywords) {
            // Map keywords to relevant image categories
            const newsCategories = {
                'technology': ['technology', 'computer', 'innovation', 'digital', 'tech', 'ai', 'software', 'hardware'],
                'politics': ['government', 'politics', 'election', 'policy', 'political', 'vote', 'democracy'],
                'business': ['business', 'finance', 'economy', 'market', 'economic', 'financial', 'company', 'corporate'],
                'science': ['science', 'research', 'laboratory', 'discovery', 'scientific', 'study', 'experiment'],
                'health': ['health', 'medical', 'hospital', 'medicine', 'healthcare', 'doctor', 'patient'],
                'sports': ['sports', 'stadium', 'athlete', 'competition', 'game', 'team', 'player'],
                'environment': ['nature', 'environment', 'climate', 'earth', 'environmental', 'green', 'eco'],
                'space': ['space', 'nasa', 'rocket', 'universe', 'satellite', 'astronomy', 'mars'],
                'energy': ['energy', 'solar', 'renewable', 'power', 'electric', 'battery', 'fuel'],
                'education': ['education', 'school', 'university', 'learning', 'student', 'academic', 'college']
            };
            
            // Find matching category
            let category = 'news';
            for (const [cat, terms] of Object.entries(newsCategories)) {
                if (terms.some(term => keywords.toLowerCase().includes(term.toLowerCase()))) {
                    category = cat;
                    break;
                }
            }
            
            console.log(`🖼️ News image category: ${category} for keywords: ${keywords}`);
            
            // Use Lorem Picsum with consistent seeding for better reliability
            const seed = `${category}-${keywords.replace(/\s+/g, '-').toLowerCase()}`;
            return `https://picsum.photos/seed/${seed}/400/200`;
        }

        // Get search-related images
        async function getSearchRelatedImage(keywords) {
            // For search results, use category-based seeding
            const searchCategories = {
                'artificial intelligence': 'ai-tech',
                'machine learning': 'ml-tech',
                'space exploration': 'space-cosmos',
                'climate change': 'climate-environment',
                'renewable energy': 'green-energy',
                'covid pandemic': 'health-medical',
                'cryptocurrency': 'crypto-finance',
                'electric vehicles': 'electric-transport',
                'quantum computing': 'quantum-tech',
                'technology': 'tech-innovation',
                'biotechnology': 'biotech-science',
                'science': 'science-research',
                'research': 'research-data',
                'information': 'info-knowledge',
                'study': 'study-education',
                'analysis': 'analysis-data'
            };
            
            // Find exact match or closest match
            let seed = 'search-information';
            for (const [term, category] of Object.entries(searchCategories)) {
                if (keywords.toLowerCase().includes(term.toLowerCase())) {
                    seed = category;
                    break;
                }
            }
            
            console.log(`🖼️ Search image seed: ${seed} for keywords: ${keywords}`);
            
            // Use Lorem Picsum with consistent seeding
            return `https://picsum.photos/seed/${seed}/400/200`;
        }

        // Get general images
        async function getGeneralImage(keywords) {
            // Use a more specific seed based on content
            const seed = keywords.replace(/\s+/g, '-').toLowerCase();
            return `https://picsum.photos/seed/${seed}/400/200`;
        }

        // Get fallback image based on type
        function getFallbackImage(type) {
            const fallbacks = {
                'news': 'https://picsum.photos/seed/news-default/400/200',
                'search': 'https://picsum.photos/seed/search-default/400/200',
                'general': 'https://picsum.photos/seed/general-default/400/200'
            };
            
            return fallbacks[type] || fallbacks['general'];
        }

        function createImageElement(imageUrl, alt = 'Related image') {
            const img = document.createElement('img');
            img.src = imageUrl;
            img.alt = alt;
            img.style.cssText = `
                width: 100%;
                max-width: 400px;
                height: 200px;
                object-fit: cover;
                border-radius: 8px;
                margin: 12px 0;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                transition: transform 0.3s ease;
                display: block;
            `;
            
            // Set initial opacity for smooth loading
            img.style.opacity = '0.8';
            
            // Add loading and error handling
            img.addEventListener('load', () => {
                console.log(`✅ Image loaded successfully: ${imageUrl}`);
                img.style.opacity = '1';
            });
            
            img.addEventListener('error', () => {
                console.log(`❌ Image failed to load: ${imageUrl}`);
                // Try fallback image
                const fallbackUrl = `https://picsum.photos/400/200?random=${Math.floor(Math.random() * 1000)}`;
                img.src = fallbackUrl;
            });
            
            // Add hover effects
            img.addEventListener('mouseenter', () => {
                img.style.transform = 'scale(1.02)';
            });
            
            img.addEventListener('mouseleave', () => {
                img.style.transform = 'scale(1)';
            });
            
            return img;
        }

        // ================= NEWS WIDGET FUNCTIONS =================
        
        const newsWidget = document.getElementById('newsWidget');
        const newsContent = document.getElementById('newsContent'); // Old compatibility
        const newsContentArea = document.getElementById('newsContentArea'); // New enhanced area
        const newsLoading = document.getElementById('newsLoading');
        const newsCount = document.getElementById('newsCount');

        function showNewsWidget() {
            newsWidget.style.display = 'block';
            startNewsTimeDisplay(); // Start Italy time when widget is shown
            console.log('📰 News widget shown with Italy time');
        }

        function hideNewsWidget() {
            newsWidget.style.display = 'none';
            stopNewsTimeDisplay(); // Stop Italy time when widget is hidden
            console.log('📰 News widget hidden');
        }

        function showNewsLoading() {
            if (newsContentArea) {
                newsContentArea.style.display = 'none';
            } else if (newsContent) {
                newsContent.style.display = 'none';
            }
            if (newsLoading) {
                newsLoading.style.display = 'block';
            }
            showNewsWidget();
        }

        function hideNewsLoading() {
            if (newsContentArea) {
                newsContentArea.style.display = 'block';
            } else if (newsContent) {
                newsContent.style.display = 'block';
            }
            if (newsLoading) {
                newsLoading.style.display = 'none';
            }
        }

        function parseStructuredNews(newsText) {
            const articles = [];
            const sections = newsText.split(/(?=HEADLINE:|SOURCE:)/);
            
            let currentArticle = {};
            
            for (const section of sections) {
                if (section.includes('HEADLINE:')) {
                    const lines = section.split('\n');
                    for (const line of lines) {
                        if (line.startsWith('HEADLINE:')) {
                            currentArticle.headline = line.replace('HEADLINE:', '').trim();
                        } else if (line.startsWith('SOURCE:')) {
                            currentArticle.source = line.replace('SOURCE:', '').trim();
                        } else if (line.startsWith('TIME:')) {
                            currentArticle.time = line.replace('TIME:', '').trim();
                        } else if (line.trim() && !line.startsWith('HEADLINE:') && !line.startsWith('SOURCE:') && !line.startsWith('TIME:')) {
                            if (!currentArticle.summary) currentArticle.summary = '';
                            currentArticle.summary += line.trim() + ' ';
                        }
                    }
                    
                    if (currentArticle.headline) {
                        articles.push({
                            headline: currentArticle.headline,
                            summary: currentArticle.summary?.trim() || '',
                            source: currentArticle.source || 'Unknown Source',
                            time: currentArticle.time || 'Recently'
                        });
                        currentArticle = {};
                    }
                }
            }
            
            return articles.length > 0 ? articles : [{
                headline: "News Update",
                summary: newsText,
                source: "General News",
                time: "Just now"
            }];
        }

        async function renderNewsArticles(articles) {
            if (newsContentArea) {
                // Clear existing content
                newsContentArea.innerHTML = '';
                
                // Render each article with image asynchronously
                for (let i = 0; i < articles.length; i++) {
                    const article = articles[i];
                    await renderSingleNewsArticle(article, i);
                }
                
                updateNewsCount(articles.length);
            }
        }

        async function renderSingleNewsArticle(article, index) {
            try {
                // Create article container
                const articleContainer = document.createElement('div');
                articleContainer.className = 'news-item';
                articleContainer.style.animationDelay = `${index * 0.1}s`;
                articleContainer.style.position = 'relative';
                
                // Create meta section
                const metaDiv = document.createElement('div');
                metaDiv.className = 'news-meta';
                metaDiv.innerHTML = `<span class="news-category">${article.source}</span>`;
                
                // Create headline
                const headlineDiv = document.createElement('div');
                headlineDiv.className = 'news-headline';
                headlineDiv.textContent = article.headline;
                

                

                
                // Create summary section
                const summaryDiv = document.createElement('div');
                summaryDiv.className = 'news-summary';
                summaryDiv.innerHTML = formatNewsContent(article.summary);
                
                // Assemble the article in the correct order:
                // 1. Meta (source)
                // 2. Headline
                // 3. Summary/Content (no images)
                articleContainer.appendChild(metaDiv);
                articleContainer.appendChild(headlineDiv);
                articleContainer.appendChild(summaryDiv);

                // Remove any existing copy button first
                const existingCopyButton = newsWidget.querySelector('.copy-button');
                if (existingCopyButton) {
                    existingCopyButton.remove();
                }

                // Add copy button to the widget (not inside content)
                const copyButton = document.createElement('button');
                copyButton.className = 'copy-button';
                copyButton.title = 'Copy news article';
                copyButton.innerHTML = `
                    <svg viewBox="0 0 24 24">
                        <path d="M16 1H4C2.9 1 2 1.9 2 3V15H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z"/>
                    </svg>
                `;
                const articleText = `${article.headline}\n\nSource: ${article.source}\n\n${article.summary}`;
                copyButton.addEventListener('click', () => copyNewsContent(articleText, copyButton));
                newsWidget.appendChild(copyButton);

                
                // Add to the news content area
                newsContentArea.appendChild(articleContainer);
                
                console.log(`📰 Rendered article ${index + 1} with image: ${article.headline}`);
                
            } catch (error) {
                console.error('❌ Error rendering news article with image:', error);
                
                // Fallback to simple rendering without image
                const fallbackHTML = `
                    <div class="news-item" style="animation-delay: ${index * 0.1}s">
                        <div class="news-meta">
                            <span class="news-category">${article.source}</span>
                        </div>
                        <div class="news-headline">${article.headline}</div>
                        <div class="news-summary">${formatNewsContent(article.summary)}</div>
                    </div>
                `;
                
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = fallbackHTML;
                newsContentArea.appendChild(tempDiv.firstElementChild);
            }
        }

        function formatNewsContent(content) {
            // Format the news content for display - DO NOT SUMMARIZE unless explicitly requested
            // Split into paragraphs and format properly
            return content.split('\n\n').map(paragraph => 
                paragraph.trim() ? `<p>${paragraph.trim()}</p>` : ''
            ).join('');
        }

        function extractHeadlineFromText(text) {
            // Extract a reasonable headline from the first sentence
            const firstSentence = text.split(/[.!?]/)[0].trim();
            
            // If first sentence is too long, truncate it
            if (firstSentence.length > 80) {
                const words = firstSentence.split(' ');
                if (words.length > 10) {
                    return words.slice(0, 10).join(' ') + '...';
                }
                return firstSentence.substring(0, 80) + '...';
            }
            
            // If first sentence is too short, try to get more context
            if (firstSentence.length < 20) {
                const sentences = text.split(/[.!?]/).filter(s => s.trim().length > 0);
                if (sentences.length > 1) {
                    const combined = sentences.slice(0, 2).join('. ').trim();
                    return combined.length > 80 ? combined.substring(0, 80) + '...' : combined;
                }
            }
            
            return firstSentence || 'News Update';
        }

        function extractSourceFromText(text) {
            // Try to extract source from common patterns
            const sourcePatterns = [
                /(?:according to|reported by|from|source:|via)\s+([A-Za-z\s]+)/i,
                /\b([A-Z][a-z]+\s+News|[A-Z][a-z]+\s+Times|BBC|CNN|Reuters|AP|Reuters)\b/i
            ];
            
            for (const pattern of sourcePatterns) {
                const match = text.match(pattern);
                if (match) {
                    return match[1].trim();
                }
            }
            
            return null; // Return null if no source found, will use default
        }

        function updateNewsCount(count) {
            if (newsCount) {
                newsCount.textContent = `${count} ${count === 1 ? 'story' : 'stories'}`;
            }
        }

        function switchTab(tabName) {
            // Update active tab
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            
            console.log(`📰 Switched to ${tabName} tab`);
            // Tab functionality can be enhanced later for filtering
        }

        // Italy time functionality for news widget
        let newsTimeInterval = null;

        function startNewsTimeDisplay() {
            const newsTimeDisplay = document.getElementById('newsTimeDisplay');
            if (!newsTimeDisplay) return;

            function updateItalyTime() {
                const now = new Date();
                const italyTime = new Date(now.toLocaleString("en-US", {timeZone: "Europe/Rome"}));
                const timeString = italyTime.toLocaleTimeString('it-IT', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });
                newsTimeDisplay.textContent = timeString;
            }

            // Update immediately
            updateItalyTime();
            
            // Update every second
            if (newsTimeInterval) {
                clearInterval(newsTimeInterval);
            }
            newsTimeInterval = setInterval(updateItalyTime, 1000);
            
            console.log('🇮🇹 Italy time display started for news widget');
        }

        function stopNewsTimeDisplay() {
            if (newsTimeInterval) {
                clearInterval(newsTimeInterval);
                newsTimeInterval = null;
                console.log('🇮🇹 Italy time display stopped');
            }
        }

        // Debug function to test news widget
        function testNewsWidget() {
            const testNews = `
            HEADLINE: European Space Agency Launches Revolutionary Mars Mission
            SOURCE: Space News International
            The European Space Agency (ESA) successfully launched its most ambitious Mars exploration mission to date from the Guiana Space Centre. The mission includes advanced drilling equipment designed to search for signs of ancient microbial life beneath the Martian surface.

            The spacecraft, named "Artemis Explorer," carries cutting-edge scientific instruments developed through international collaboration with NASA and the Japan Aerospace Exploration Agency. Scientists expect the mission to provide unprecedented insights into Mars' geological history and potential for past life.

            HEADLINE: Italy Announces Major Investment in Renewable Energy Infrastructure
            SOURCE: Italian Energy Review
            The Italian government unveiled a €50 billion renewable energy initiative aimed at making Italy carbon-neutral by 2035. The comprehensive plan includes massive solar farm installations across southern regions and offshore wind projects along the Mediterranean coast.

            Prime Minister announced that this investment will create over 200,000 new jobs and position Italy as a leader in European clean energy production. The initiative also includes incentives for residential solar installations and electric vehicle infrastructure.
            `;
            
            console.log('🧪 Testing news widget with enhanced sample data (no dates, full content)');
            updateNewsWidget(testNews);
        }

        // Add test button functionality - call this from browser console if needed
        window.testNewsWidget = testNewsWidget;

        // ================= NOTES WIDGET FUNCTIONS =================

        const notesWidget = document.getElementById('notesWidget');
        const notesEditor = document.getElementById('notesEditor');
        const savedNotesList = document.getElementById('savedNotesList');
        const noteTextarea = document.getElementById('noteTextarea');
        const saveNoteBtn = document.getElementById('saveNoteBtn');
        const copyNoteBtn = document.getElementById('copyNoteBtn');
        const viewNotesBtn = document.getElementById('viewNotesBtn');
        const newNoteBtn = document.getElementById('newNoteBtn');
        const clearNoteBtn = document.getElementById('clearNoteBtn');
        const backToEditorBtn = document.getElementById('backToEditorBtn');
        const notesListContainer = document.getElementById('notesListContainer');

        // Notes storage key
        const NOTES_STORAGE_KEY = 'nova_saved_notes';

        function showNotesWidget() {
            if (notesWidget) {
                notesWidget.style.display = 'block';
                console.log('📝 Notes widget display set to block');

                // Ensure the editor is visible and notes list is hidden
                if (notesEditor) {
                    notesEditor.style.display = 'block';
                    console.log('📝 Notes editor shown');
                }
                if (savedNotesList) {
                    savedNotesList.style.display = 'none';
                    console.log('📝 Saved notes list hidden');
                }

                // Focus on the textarea when widget is shown
                setTimeout(() => {
                    if (noteTextarea) {
                        noteTextarea.focus();
                        noteTextarea.click(); // Also trigger click to ensure focus
                        console.log('📝 Textarea focused and clicked');

                        // Test if textarea is actually focusable
                        console.log('📝 Textarea properties:', {
                            disabled: noteTextarea.disabled,
                            readOnly: noteTextarea.readOnly,
                            tabIndex: noteTextarea.tabIndex,
                            style: noteTextarea.style.cssText
                        });
                    } else {
                        console.error('❌ Textarea not found when trying to focus');
                    }
                }, 200);

                console.log('📝 Notes widget shown and textarea focus attempted');
            } else {
                console.error('❌ Notes widget not found');
            }
        }

        // Test function to show notes widget - can be called from console
        window.showNotesWidget = showNotesWidget;

        function hideNotesWidget() {
            if (notesWidget) {
                notesWidget.style.display = 'none';
                console.log('📝 Notes widget hidden');
            }
        }

        function saveNote() {
            console.log('📝 Save button clicked!');

            if (!noteTextarea) {
                console.error('❌ Textarea not found!');
                alert('Error: Textarea not found!');
                return;
            }

            const noteContent = noteTextarea.value.trim();
            console.log('📝 Note content:', noteContent);

            if (!noteContent) {
                alert('Please write something before saving!');
                return;
            }

            try {
                // Get existing notes
                const existingNotes = getSavedNotes();
                console.log('📝 Existing notes count:', existingNotes.length);

                // Create new note object
                const newNote = {
                    id: Date.now().toString(),
                    content: noteContent,
                    timestamp: new Date().toLocaleString(),
                    preview: noteContent.substring(0, 100) + (noteContent.length > 100 ? '...' : '')
                };

                // Add to beginning of array (newest first)
                existingNotes.unshift(newNote);

                // Save to localStorage
                localStorage.setItem(NOTES_STORAGE_KEY, JSON.stringify(existingNotes));
                console.log('📝 Note saved to localStorage');

                // Clear textarea
                noteTextarea.value = '';

                // Show success message
                showNotificationMessage('Note saved successfully!', 'success');

                console.log('📝 Note saved successfully:', newNote.id);
            } catch (error) {
                console.error('❌ Error saving note:', error);
                alert('Error saving note: ' + error.message);
            }
        }

        function getSavedNotes() {
            try {
                const notes = localStorage.getItem(NOTES_STORAGE_KEY);
                return notes ? JSON.parse(notes) : [];
            } catch (error) {
                console.error('Error loading notes:', error);
                return [];
            }
        }

        function showSavedNotes() {
            const notes = getSavedNotes();

            // Hide editor, show notes list
            notesEditor.style.display = 'none';
            savedNotesList.style.display = 'block';
            newNoteBtn.style.display = 'inline-flex';

            // Clear and populate notes list
            notesListContainer.innerHTML = '';

            if (notes.length === 0) {
                notesListContainer.innerHTML = '<div class="no-notes-message">No saved notes yet.</div>';
            } else {
                notes.forEach(note => {
                    const noteElement = createNoteElement(note);
                    notesListContainer.appendChild(noteElement);
                });
            }

            console.log(`📝 Showing ${notes.length} saved notes`);
        }

        function createNoteElement(note) {
            const noteDiv = document.createElement('div');
            noteDiv.className = 'note-item';
            noteDiv.onclick = () => openNote(note);

            noteDiv.innerHTML = `
                <div class="note-preview">${note.preview}</div>
                <div class="note-meta">
                    <span>${note.timestamp}</span>
                    <button class="note-delete" onclick="event.stopPropagation(); deleteNote('${note.id}')" title="Delete Note">
                        <i class="fa-solid fa-trash"></i>
                    </button>
                </div>
            `;

            return noteDiv;
        }

        function openNote(note) {
            // Load note content into textarea
            noteTextarea.value = note.content;

            // Switch back to editor view
            backToEditor();

            console.log('📝 Opened note:', note.id);
        }

        function deleteNote(noteId) {
            if (!confirm('Are you sure you want to delete this note?')) {
                return;
            }

            const notes = getSavedNotes();
            const filteredNotes = notes.filter(note => note.id !== noteId);

            localStorage.setItem(NOTES_STORAGE_KEY, JSON.stringify(filteredNotes));

            // Refresh the notes list
            showSavedNotes();

            showNotificationMessage('Note deleted successfully!', 'success');
            console.log('📝 Note deleted:', noteId);
        }

        function backToEditor() {
            // Show editor, hide notes list
            notesEditor.style.display = 'block';
            savedNotesList.style.display = 'none';
            newNoteBtn.style.display = 'none';

            // Focus on textarea
            noteTextarea.focus();
        }

        function newNote() {
            // Clear textarea and go back to editor
            noteTextarea.value = '';
            backToEditor();
            console.log('📝 Starting new note');
        }

        function copyNote() {
            console.log('📝 Copy button clicked!');

            if (!noteTextarea) {
                console.error('❌ Textarea not found!');
                alert('Error: Textarea not found!');
                return;
            }

            const noteContent = noteTextarea.value;
            console.log('📝 Content to copy:', noteContent.substring(0, 50) + '...');

            if (!noteContent.trim()) {
                showNotificationMessage('Nothing to copy! Write something first.', 'info');
                return;
            }

            // Use the modern clipboard API if available
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(noteContent).then(() => {
                    showNotificationMessage('Note copied to clipboard!', 'success');
                    console.log('📝 Note copied to clipboard using modern API');
                }).catch(err => {
                    console.error('Failed to copy note with modern API:', err);
                    fallbackCopyNote(noteContent);
                });
            } else {
                console.log('📝 Using fallback copy method');
                // Fallback for older browsers or non-secure contexts
                fallbackCopyNote(noteContent);
            }
        }

        function fallbackCopyNote(text) {
            // Create a temporary textarea element
            const tempTextarea = document.createElement('textarea');
            tempTextarea.value = text;
            tempTextarea.style.position = 'fixed';
            tempTextarea.style.left = '-999999px';
            tempTextarea.style.top = '-999999px';
            document.body.appendChild(tempTextarea);

            // Select and copy the text
            tempTextarea.focus();
            tempTextarea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showNotificationMessage('Note copied to clipboard!', 'success');
                    console.log('📝 Note copied to clipboard (fallback method)');
                } else {
                    showNotificationMessage('Failed to copy note', 'error');
                }
            } catch (err) {
                console.error('Fallback copy failed:', err);
                showNotificationMessage('Copy not supported in this browser', 'error');
            }
            // Clean up
            document.body.removeChild(tempTextarea);
        }

        function clearNote() {
            if (noteTextarea.value.trim() && !confirm('Are you sure you want to clear this note?')) {
                return;
            }

            noteTextarea.value = '';
            noteTextarea.focus();
            showNotificationMessage('Note cleared!', 'info');
            console.log('📝 Note cleared');
        }

        function showNotificationMessage(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'rgba(0, 255, 0, 0.9)' : 'rgba(138, 43, 226, 0.9)'};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                animation: slideInRight 0.3s ease-out;
            `;
            notification.textContent = message;

            // Add animation keyframes if not already added
            if (!document.querySelector('#notification-styles')) {
                const style = document.createElement('style');
                style.id = 'notification-styles';
                style.textContent = `
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes slideOutRight {
                        from { transform: translateX(0); opacity: 1; }
                        to { transform: translateX(100%); opacity: 0; }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(notification);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Debug functions to test resize buttons
        window.testResizeButtons = function() {
            console.log('🧪 Testing resize buttons...');
            console.log('Current widgetSizeIndex:', widgetSizeIndex);
            
            // Test news widget resize
            console.log('Testing news widget increase size...');
            increaseWidgetSize('newsWidget');
            
            setTimeout(() => {
                console.log('Testing news widget decrease size...');
                decreaseWidgetSize('newsWidget');
            }, 2000);
        };
        
        // Debug function to show/hide all widgets for testing
        window.showAllWidgets = function() {
            console.log('🧪 Showing all widgets for testing...');
            showNewsWidget();
            document.getElementById('timeDisplay').style.display = 'block';
            document.getElementById('weatherDisplay').style.display = 'block';
            document.getElementById('searchWidget').style.display = 'block';
            document.getElementById('calculatorWidget').style.display = 'block';
        };

        function suggestNewsSource(topic) {
            const topicLower = topic.toLowerCase();
            
            // Source suggestions based on topic
            if (topicLower.includes('tech') || topicLower.includes('ai') || topicLower.includes('computer')) {
                return 'TechCrunch';
            } else if (topicLower.includes('business') || topicLower.includes('finance') || topicLower.includes('market')) {
                return 'Bloomberg';
            } else if (topicLower.includes('sport') || topicLower.includes('football') || topicLower.includes('basketball')) {
                return 'ESPN';
            } else if (topicLower.includes('science') || topicLower.includes('research') || topicLower.includes('study')) {
                return 'Nature';
            } else if (topicLower.includes('health') || topicLower.includes('medical') || topicLower.includes('covid')) {
                return 'Reuters';
            } else {
                return 'BBC';
            }
        }

        function isNewsRequest(message) {
            const messageLower = message.toLowerCase();
            const newsKeywords = [
                'news', 'headlines', 'latest news', 'current events', 'breaking news',
                'what happened', 'what\'s happening', 'today\'s news', 'recent news',
                'news about', 'news on', 'tell me about the news', 'any news'
            ];
            
            return newsKeywords.some(keyword => messageLower.includes(keyword));
        }

        function isNewsContentDetected(userMessage, searchContent) {
            const messageLower = userMessage.toLowerCase();
            const contentLower = searchContent.toLowerCase();
            
            // Check if user asked for news or current events
            const newsRequestKeywords = [
                'news', 'headlines', 'latest', 'current events', 'breaking',
                'what happened', 'what\'s happening', 'recent', 'updates',
                'more info', 'tell me more', 'give me more information'
            ];
            
            // Check if search content contains news indicators
            const newsContentIndicators = [
                'reuters', 'bbc', 'cnn', 'associated press', 'bloomberg', 
                'techcrunch', 'espn', 'news', 'breaking', 'headlines',
                'reported', 'according to', 'sources', 'journalist',
                'ago)', 'days ago', 'hours ago', 'minutes ago', 'recently'
            ];
            
            const hasNewsRequest = newsRequestKeywords.some(keyword => messageLower.includes(keyword));
            const hasNewsContent = newsContentIndicators.some(indicator => contentLower.includes(indicator));
            
            return hasNewsRequest || hasNewsContent;
        }

        async function updateNewsWidget(newsText) {
            console.log('📰 updateNewsWidget called with:', newsText.substring(0, 100) + '...');
            hideNewsLoading();
            
            try {
                // Try to parse as structured news data
                let newsArticles = [];
                
                // Check if the text contains structured news data
                if (newsText.includes('HEADLINE:') || newsText.includes('SOURCE:')) {
                    console.log('📰 Parsing structured news data');
                    newsArticles = parseStructuredNews(newsText);
                } else {
                    console.log('📰 Creating full article from plain text - NO SUMMARIZATION');
                    // Handle plain text news - DISPLAY FULL CONTENT, DO NOT SUMMARIZE
                    const headline = extractHeadlineFromText(newsText);
                    const source = extractSourceFromText(newsText) || "News Feed";
                    
                    newsArticles = [{
                        headline: headline,
                        summary: newsText, // FULL CONTENT - NO SUMMARIZATION
                        source: source
                        // NO TIME FIELD - dates removed from display
                    }];
                }
                
                console.log('📰 Rendering', newsArticles.length, 'articles with full content and images (no summarization)');
                await renderNewsArticles(newsArticles);
                showNewsWidget(); // This will also start the Italy time display
                
                // Add animation
                newsWidget.style.animation = 'none';
                setTimeout(() => {
                    newsWidget.style.animation = 'newsGlow 4s ease-in-out infinite';
                }, 10);
                
                console.log('📰 Enhanced news widget updated successfully with images and Italy time');
                
            } catch (error) {
                console.error('❌ Error updating news widget:', error);
                // Fallback to simple text display
                if (newsContentArea) {
                    newsContentArea.innerHTML = `
                        <div class="news-item">
                            <div class="news-meta">
                                <span class="news-category">News Feed</span>
                            </div>
                            <div class="news-headline">Latest News</div>
                            <div class="news-summary">${formatNewsContent(newsText)}</div>
                        </div>
                    `;
                }
                showNewsWidget(); // This will also start the Italy time display
                console.log('📰 Fallback news display used');
            }
        }

        function isNewsRequest(message) {
            const newsKeywords = [
                'news about', 'news on', 'latest news', 'breaking news',
                'news', 'headlines', 'current events', 'what\'s happening',
                'recent news', 'today\'s news', 'news update', 'news summary',
                'give me news', 'show me news', 'tell me the news'
            ];
            
            const lowerMessage = message.toLowerCase();
            return newsKeywords.some(keyword => lowerMessage.includes(keyword));
        }

        function extractNewsQuery(message) {
            const lowerMessage = message.toLowerCase();
            
            // Try to extract the news query after common news phrases
            const patterns = [
                /news about (.+)/,
                /news on (.+)/,
                /latest news (.+)/,
                /breaking news (.+)/,
                /give me news (?:about|on) (.+)/,
                /show me news (?:about|on) (.+)/,
                /tell me (?:the )?news (?:about|on) (.+)/,
                /(.+) news/
            ];
            
            for (const pattern of patterns) {
                const match = lowerMessage.match(pattern);
                if (match && match[1]) {
                    return match[1].trim();
                }
            }
            
            // If no specific topic found, return general news request
            if (isNewsRequest(message)) {
                return 'general news';
            }
            
            return message; // Return original message if no pattern matches
        }

        // ================= CALCULATOR WIDGET FUNCTIONS =================
        
        const calculatorWidget = document.getElementById('calculatorWidget');
        const calculatorInput = document.getElementById('calculatorInput');
        const calculatorResult = document.getElementById('calculatorResult');
        const calculatorLoading = document.getElementById('calculatorLoading');
        
        let currentInput = '';
        let lastAnswer = 0;
        let angleMode = 'deg'; // 'deg' or 'rad'
        let currentTab = 'main';

        function showCalculatorWidget() {
            calculatorWidget.style.display = 'block';
            console.log('🧮 Calculator widget shown');
        }

        function hideCalculatorWidget() {
            calculatorWidget.style.display = 'none';
            console.log('🧮 Calculator widget hidden');
        }

        function showCalculatorLoading() {
            calculatorResult.style.display = 'none';
            calculatorLoading.style.display = 'block';
            showCalculatorWidget();
            console.log('🧮 Calculator loading shown');
        }

        function hideCalculatorLoading() {
            calculatorResult.style.display = 'block';
            calculatorLoading.style.display = 'none';
            console.log('🧮 Calculator loading hidden');
        }

        function updateCalculatorWidget(expression, result) {
            hideCalculatorLoading();
            calculatorInput.textContent = expression;
            calculatorResult.textContent = result;
            lastAnswer = parseFloat(result) || 0;
            showCalculatorWidget();
            
            // Add a subtle animation to indicate new calculation
            calculatorWidget.style.animation = 'none';
            setTimeout(() => {
                calculatorWidget.style.animation = 'calculatorGlow 3s ease-in-out infinite';
            }, 10);
            
            console.log('🧮 Calculator widget updated:', expression, '=', result);
        }

        function appendToInput(value) {
            currentInput += value;
            calculatorInput.textContent = currentInput;
            updateDisplay();
        }

        function clearAll() {
            currentInput = '';
            calculatorInput.textContent = '';
            calculatorResult.textContent = '0';
            updateDisplay();
        }

        function backspace() {
            currentInput = currentInput.slice(0, -1);
            calculatorInput.textContent = currentInput;
            updateDisplay();
        }

        function useLastAnswer() {
            appendToInput(lastAnswer.toString());
        }

        function calculate() {
            if (!currentInput.trim()) return;
            
            try {
                // Replace display symbols with JavaScript operators
                let expression = currentInput
                    .replace(/×/g, '*')
                    .replace(/÷/g, '/')
                    .replace(/Math\.pow\(/g, 'Math.pow(')
                    .replace(/!/g, ''); // Handle factorial separately if needed
                
                // Handle angle mode for trigonometric functions
                if (angleMode === 'deg') {
                    expression = expression
                        .replace(/Math\.sin\(/g, 'Math.sin(Math.PI/180*')
                        .replace(/Math\.cos\(/g, 'Math.cos(Math.PI/180*')
                        .replace(/Math\.tan\(/g, 'Math.tan(Math.PI/180*')
                        .replace(/Math\.asin\(/g, '(180/Math.PI)*Math.asin(')
                        .replace(/Math\.acos\(/g, '(180/Math.PI)*Math.acos(')
                        .replace(/Math\.atan\(/g, '(180/Math.PI)*Math.atan(');
                }
                
                // Evaluate the expression
                const result = eval(expression);
                
                if (isNaN(result) || !isFinite(result)) {
                    throw new Error('Invalid calculation');
                }
                
                const formattedResult = Number(result.toPrecision(12)).toString();
                updateCalculatorWidget(currentInput, formattedResult);
                currentInput = formattedResult;
                
            } catch (error) {
                console.error('Calculation error:', error);
                calculatorResult.textContent = 'Error';
                calculatorResult.classList.add('error');
                setTimeout(() => {
                    calculatorResult.classList.remove('error');
                    calculatorResult.textContent = '0';
                }, 2000);
            }
        }

        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // Add active class to clicked button
            event.target.classList.add('active');
            
            currentTab = tabName;
            console.log('🧮 Switched to tab:', tabName);
        }

        function setAngleMode(mode) {
            angleMode = mode;
            
            // Remove active class from all angle buttons
            document.querySelectorAll('.angle-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Add active class to selected button
            event.target.classList.add('active');
            
            console.log('🧮 Angle mode set to:', mode);
        }

        function updateDisplay() {
            if (!currentInput) {
                calculatorResult.textContent = '0';
            }
        }

        function isCalculatorRequest(message) {
            const calculatorKeywords = [
                'calculator', 'calculate', 'calc', 'compute', 'math',
                'show calculator', 'open calculator', 'give me calculator',
                'i need calculator', 'can you calculate', 'do the math',
                'solve this', 'what is', 'what\'s'
            ];
            
            const mathOperators = ['+', '-', '*', '/', '×', '÷', '=', '^', 'sqrt', 'sin', 'cos', 'tan'];
            
            const lowerMessage = message.toLowerCase();
            
            // Check for calculator keywords
            const hasCalculatorKeyword = calculatorKeywords.some(keyword => lowerMessage.includes(keyword));
            
            // Check for math expressions (numbers with operators)
            const hasMathExpression = /\d+\s*[\+\-\*\/\×\÷\^\=]\s*\d+/.test(message) ||
                                    /\d+\s*[\+\-\*\/\×\÷]\s*\d+/.test(message) ||
                                    mathOperators.some(op => lowerMessage.includes(op));
            
            // Check for "what is" followed by math expression
            const isWhatIsMath = /what\s+is\s+\d+/.test(lowerMessage) && hasMathExpression;
            
            return hasCalculatorKeyword || hasMathExpression || isWhatIsMath;
        }

        function extractCalculationQuery(message) {
            const lowerMessage = message.toLowerCase();
            
            // Try to extract the calculation after common phrases
            const patterns = [
                /calculate (.+)/,
                /compute (.+)/,
                /what is (.+)/,
                /what's (.+)/,
                /solve (.+)/,
                /do the math (?:for|on) (.+)/,
                /can you calculate (.+)/,
                /(.+)/  // Fallback to entire message
            ];
            
            for (const pattern of patterns) {
                const match = lowerMessage.match(pattern);
                if (match && match[1]) {
                    let query = match[1].trim();
                    
                    // Clean up common words that aren't part of the calculation
                    query = query
                        .replace(/please/g, '')
                        .replace(/for me/g, '')
                        .replace(/\?/g, '')
                        .trim();
                    
                    return query;
                }
            }
            
            return message; // Return original message if no pattern matches
        }

        // ================= NOTEPAD WIDGET FUNCTIONS =================

        const notepadWidget = document.getElementById('notepadWidget');
        const notepadTextarea = document.getElementById('notepadTextarea');
        const notepadWordCount = document.getElementById('notepadWordCount');
        const notepadSaveStatus = document.getElementById('notepadSaveStatus');

        // Notepad storage keys for localStorage
        const NOTEPAD_STORAGE_KEY = 'astra_notepad_content'; // For current content
        const NOTEPAD_NOTES_STORAGE_KEY = 'astra_notepad_saved_notes'; // For saved notes list
        let notepadCurrentSize = 'normal';

        function showNotepadWidget() {
            notepadWidget.style.display = 'flex';
            
            // Load existing content
            loadNotepadContent();
            
            // Focus on the textarea
            setTimeout(() => {
                notepadTextarea.focus();
            }, 100);
            
            console.log('📝 Notepad widget shown');
        }

        function hideNotepadWidget() {
            notepadWidget.style.display = 'none';
            // Save current draft content (not as a saved note)
            saveDraftContent();
            console.log('📝 Notepad widget hidden');
        }

        function saveDraftContent() {
            try {
                const content = notepadTextarea.value;
                localStorage.setItem(NOTEPAD_STORAGE_KEY, content);
                console.log('📝 Draft content saved');
            } catch (error) {
                console.error('❌ Error saving draft content:', error);
            }
        }

        function saveNotepadContent() {
            const content = notepadTextarea.value.trim();

            if (!content) {
                alert('Please write something before saving!');
                return;
            }

            try {
                // Get existing saved notes
                const existingNotes = getSavedNotepadNotes();

                // Create new note object
                const newNote = {
                    id: Date.now().toString(),
                    content: content,
                    timestamp: new Date().toLocaleString(),
                    preview: content.substring(0, 100) + (content.length > 100 ? '...' : '')
                };

                // Add to beginning of array (newest first)
                existingNotes.unshift(newNote);

                // Save to localStorage
                localStorage.setItem(NOTEPAD_NOTES_STORAGE_KEY, JSON.stringify(existingNotes));

                // Clear the textarea after saving
                notepadTextarea.value = '';

                updateNotepadStatus('Note Saved!');
                console.log('📝 Notepad note saved to localStorage:', newNote.id);

                // Show success message briefly
                setTimeout(() => {
                    updateNotepadStatus('Ready');
                }, 2000);

            } catch (error) {
                console.error('❌ Error saving notepad note:', error);
                updateNotepadStatus('Save Error');
            }
        }

        function loadNotepadContent() {
            try {
                // Load current draft content (if any)
                const savedContent = localStorage.getItem(NOTEPAD_STORAGE_KEY) || '';
                notepadTextarea.value = savedContent;
                updateNotepadWordCount();
                console.log('📝 Notepad content loaded from localStorage');
            } catch (error) {
                console.error('❌ Error loading notepad content:', error);
                notepadTextarea.value = '';
            }
        }

        function getSavedNotepadNotes() {
            try {
                const notes = localStorage.getItem(NOTEPAD_NOTES_STORAGE_KEY);
                return notes ? JSON.parse(notes) : [];
            } catch (error) {
                console.error('Error loading notepad notes:', error);
                return [];
            }
        }

        function showSavedNotepadNotes() {
            const notes = getSavedNotepadNotes();

            if (notes.length === 0) {
                alert('No saved notes yet. Write something and click Save to create your first note!');
                return;
            }

            // Create a more interactive display
            let notesDisplay = `📝 Your Saved Notes (${notes.length} total):\n\n`;
            notes.forEach((note, index) => {
                notesDisplay += `${index + 1}. [${note.timestamp}]\n`;
                notesDisplay += `${note.preview}\n`;
                notesDisplay += `─────────────────────────────────\n\n`;
            });

            notesDisplay += '\n💡 Tip: Click a note number to load it into the editor!';

            // For now, use alert. Later we can create a proper modal
            const userChoice = confirm(notesDisplay + '\n\nWould you like to load the most recent note into the editor?');

            if (userChoice && notes.length > 0) {
                // Load the most recent note into the textarea
                notepadTextarea.value = notes[0].content;
                updateNotepadWordCount();
                updateNotepadStatus('Note Loaded');
            }
        }

        function deleteNotepadNote(noteId) {
            try {
                const notes = getSavedNotepadNotes();
                const filteredNotes = notes.filter(note => note.id !== noteId);
                localStorage.setItem(NOTEPAD_NOTES_STORAGE_KEY, JSON.stringify(filteredNotes));
                console.log('📝 Notepad note deleted:', noteId);
            } catch (error) {
                console.error('❌ Error deleting notepad note:', error);
            }
        }

        function updateNotepadWordCount() {
            const text = notepadTextarea.value;
            const wordCount = text.trim() === '' ? 0 : text.trim().split(/\s+/).length;
            notepadWordCount.textContent = `${wordCount} word${wordCount !== 1 ? 's' : ''}`;
        }

        function updateNotepadStatus(status) {
            notepadSaveStatus.textContent = status;
            
            if (status === 'Saving...') {
                setTimeout(() => {
                    notepadSaveStatus.textContent = 'Saved';
                }, 1000);
            }
        }

        function copyNotepadContent() {
            const content = notepadTextarea.value;
            
            if (content.trim() === '') {
                updateNotepadStatus('Nothing to copy');
                setTimeout(() => updateNotepadStatus('Ready'), 2000);
                return;
            }
            
            navigator.clipboard.writeText(content).then(() => {
                updateNotepadStatus('Copied!');
                setTimeout(() => {
                    updateNotepadStatus('Saved');
                }, 1500);
                console.log('📝 Notepad content copied to clipboard');
            }).catch(err => {
                console.error('❌ Failed to copy notepad content:', err);
                updateNotepadStatus('Copy failed');
                // Fallback for browsers that don't support clipboard API
                notepadTextarea.select();
                try {
                    document.execCommand('copy');
                    updateNotepadStatus('Copied!');
                    setTimeout(() => updateNotepadStatus('Saved'), 1500);
                } catch (e) {
                    updateNotepadStatus('Copy failed');
                }
            });
        }

        function clearNotepadContent() {
            if (confirm('Are you sure you want to clear all notes?')) {
                notepadTextarea.value = '';
                saveNotepadContent();
                updateNotepadWordCount();
                updateNotepadStatus('Cleared');
                setTimeout(() => updateNotepadStatus('Ready'), 2000);
                console.log('📝 Notepad content cleared');
            }
        }

        function shareNotepadContent() {
            const content = notepadTextarea.value;
            
            if (content.trim() === '') {
                updateNotepadStatus('Nothing to share');
                setTimeout(() => updateNotepadStatus('Ready'), 2000);
                return;
            }
            
            if (navigator.share) {
                navigator.share({
                    title: 'My Notes',
                    text: content
                }).then(() => {
                    updateNotepadStatus('Shared!');
                    setTimeout(() => updateNotepadStatus('Saved'), 1500);
                }).catch(err => {
                    console.log('Share cancelled or failed:', err);
                    updateNotepadStatus('Share failed');
                });
            } else {
                // Fallback - copy to clipboard
                copyNotepadContent();
            }
        }

        // Initialize notepad event listeners
        function initializeNotepadWidget() {
            // Add event listeners for notepad buttons
            document.getElementById('notepadCopyBtn').addEventListener('click', copyNotepadContent);
            document.getElementById('notepadDeleteBtn').addEventListener('click', clearNotepadContent);
            document.getElementById('notepadShareBtn').addEventListener('click', showSavedNotepadNotes);
            document.getElementById('notepadSaveBtn').addEventListener('click', saveNotepadContent);
            
            // Add event listener for textarea changes
            notepadTextarea.addEventListener('input', function() {
                updateNotepadWordCount();
                updateNotepadStatus('Typing...');
                
                // Auto-save draft after 2 seconds of no typing
                clearTimeout(notepadTextarea.autoSaveTimer);
                notepadTextarea.autoSaveTimer = setTimeout(() => {
                    saveDraftContent();
                    updateNotepadStatus('Draft Saved');
                    setTimeout(() => updateNotepadStatus('Ready'), 1000);
                }, 2000);
            });
            
            // Add event listener for textarea focus
            notepadTextarea.addEventListener('focus', function() {
                updateNotepadStatus('Ready');
            });
            
            console.log('📝 Notepad widget initialized');
        }

        // ================= WIDGET DRAG & RESIZE FUNCTIONS =================
        
        let dragState = {
            isDragging: false,
            isResizing: false,
            currentWidget: null,
            startX: 0,
            startY: 0,
            currentX: 0,
            currentY: 0,
            startLeft: 0,
            startTop: 0,
            startWidth: 0,
            startHeight: 0,
            animationFrame: null,
            positionUpdateFrame: null,
            resizeAnimationFrame: null
        };

        // Widget size cycling
        const widgetSizes = ['small', 'normal', 'large', 'xlarge'];
        let widgetSizeIndex = {};

        function cycleWidgetSize(widgetId) {
            const widget = document.getElementById(widgetId);
            if (!widget) return;

            // Initialize size index if not exists
            if (!(widgetId in widgetSizeIndex)) {
                widgetSizeIndex[widgetId] = 1; // Start with 'normal'
            }

            // Remove current size class and text scale class
            const currentSize = widgetSizes[widgetSizeIndex[widgetId]];
            widget.classList.remove(`widget-size-${currentSize}`);
            widget.classList.remove(`widget-text-scale-${currentSize}`);

            // Cycle to next size
            widgetSizeIndex[widgetId] = (widgetSizeIndex[widgetId] + 1) % widgetSizes.length;
            const newSize = widgetSizes[widgetSizeIndex[widgetId]];
            widget.classList.add(`widget-size-${newSize}`);
            widget.classList.add(`widget-text-scale-${newSize}`);

            console.log(`🔧 Widget ${widgetId} resized to: ${newSize}`);
            
            // Save position to localStorage
            saveWidgetPosition(widgetId);
        }

        // NEW: Increase widget size function
        function increaseWidgetSize(widgetId) {
            const widget = document.getElementById(widgetId);
            if (!widget) return;

            // Initialize size index if not exists
            if (!(widgetId in widgetSizeIndex)) {
                widgetSizeIndex[widgetId] = 1; // Start with 'normal'
            }

            // Don't increase if already at maximum size
            if (widgetSizeIndex[widgetId] >= widgetSizes.length - 1) {
                console.log(`🔧 Widget ${widgetId} is already at maximum size: ${widgetSizes[widgetSizeIndex[widgetId]]}`);
                return;
            }

            // Remove current size class
            const currentSize = widgetSizes[widgetSizeIndex[widgetId]];
            widget.classList.remove(`widget-size-${currentSize}`);
            widget.classList.remove(`widget-text-scale-${currentSize}`);

            // Increase to next size
            widgetSizeIndex[widgetId]++;
            const newSize = widgetSizes[widgetSizeIndex[widgetId]];
            widget.classList.add(`widget-size-${newSize}`);
            widget.classList.add(`widget-text-scale-${newSize}`);

            console.log(`🔧 Widget ${widgetId} increased to: ${newSize}`);
            
            // Save position to localStorage
            saveWidgetPosition(widgetId);
        }

        // NEW: Decrease widget size function
        function decreaseWidgetSize(widgetId) {
            const widget = document.getElementById(widgetId);
            if (!widget) return;

            // Initialize size index if not exists
            if (!(widgetId in widgetSizeIndex)) {
                widgetSizeIndex[widgetId] = 1; // Start with 'normal'
            }

            // Don't decrease if already at minimum size
            if (widgetSizeIndex[widgetId] <= 0) {
                console.log(`🔧 Widget ${widgetId} is already at minimum size: ${widgetSizes[widgetSizeIndex[widgetId]]}`);
                return;
            }

            // Remove current size class
            const currentSize = widgetSizes[widgetSizeIndex[widgetId]];
            widget.classList.remove(`widget-size-${currentSize}`);
            widget.classList.remove(`widget-text-scale-${currentSize}`);

            // Decrease to previous size
            widgetSizeIndex[widgetId]--;
            const newSize = widgetSizes[widgetSizeIndex[widgetId]];
            widget.classList.add(`widget-size-${newSize}`);
            widget.classList.add(`widget-text-scale-${newSize}`);

            console.log(`🔧 Widget ${widgetId} decreased to: ${newSize}`);
            
            // Save position to localStorage
            saveWidgetPosition(widgetId);
        }

        function saveWidgetPosition(widgetId) {
            const widget = document.getElementById(widgetId);
            if (!widget) return;

            const rect = widget.getBoundingClientRect();
            const position = {
                left: widget.style.left || rect.left + 'px',
                top: widget.style.top || rect.top + 'px',
                size: widgetSizeIndex[widgetId] || 1
            };

            // Save custom dimensions if manually resized
            if (widget.style.width && widget.style.height) {
                position.width = widget.style.width;
                position.height = widget.style.height;
            }

            localStorage.setItem(`widget_${widgetId}_position`, JSON.stringify(position));
        }

        function applyDynamicTextScaling(widget, width, height) {
            if (!widget) return;

            // Calculate scale factor based on widget size with better proportions
            const baseWidth = 300; // Base width for normal size
            const baseHeight = 150; // Base height for normal size
            const scaleX = width / baseWidth;
            const scaleY = height / baseHeight;
            const avgScale = (scaleX + scaleY) / 2;
            
            // Apply different scaling strategies for different content types
            const dampedScale = Math.pow(avgScale, 0.6); // Damping factor reduces excessive scaling
            const textScale = Math.pow(avgScale, 0.7);   // Slightly more scaling for text readability
            const elementScale = Math.pow(avgScale, 0.8); // More scaling for UI elements
            
            // Also factor in viewport scaling
            const viewportScale = getViewportScale();
            const finalScale = dampedScale * viewportScale;
            const finalTextScale = textScale * viewportScale;
            const finalElementScale = elementScale * viewportScale;
            
            // Clamp the scales to reasonable bounds
            const clampedScale = Math.max(0.5, Math.min(2.5, finalScale));
            const clampedTextScale = Math.max(0.6, Math.min(2.0, finalTextScale));
            const clampedElementScale = Math.max(0.7, Math.min(2.2, finalElementScale));

            // ================= SCALE ALL WIDGET LABELS =================
            const timeLabel = widget.querySelector('.time-label');
            const weatherLabel = widget.querySelector('.weather-label');
            const searchLabel = widget.querySelector('.search-label');
            const newsLabel = widget.querySelector('.news-label');
            
            if (timeLabel) {
                timeLabel.style.fontSize = Math.max(8, 12 * clampedTextScale) + 'px';
                timeLabel.style.padding = `${Math.max(2, 3 * clampedElementScale)}px ${Math.max(6, 10 * clampedElementScale)}px`;
                timeLabel.style.marginBottom = Math.max(4, 8 * clampedElementScale) + 'px';
            }
            if (weatherLabel) {
                weatherLabel.style.fontSize = Math.max(8, 12 * clampedTextScale) + 'px';
                weatherLabel.style.padding = `${Math.max(2, 3 * clampedElementScale)}px ${Math.max(6, 10 * clampedElementScale)}px`;
                weatherLabel.style.marginBottom = Math.max(4, 8 * clampedElementScale) + 'px';
            }
            if (searchLabel) {
                searchLabel.style.fontSize = Math.max(8, 12 * clampedTextScale) + 'px';
                searchLabel.style.padding = `${Math.max(2, 3 * clampedElementScale)}px ${Math.max(6, 10 * clampedElementScale)}px`;
                searchLabel.style.marginBottom = Math.max(4, 8 * clampedElementScale) + 'px';
            }
            if (newsLabel) {
                newsLabel.style.fontSize = Math.max(8, 12 * clampedTextScale) + 'px';
                newsLabel.style.padding = `${Math.max(2, 3 * clampedElementScale)}px ${Math.max(6, 10 * clampedElementScale)}px`;
                newsLabel.style.marginBottom = Math.max(4, 8 * clampedElementScale) + 'px';
            }

            // ================= SCALE ALL MAIN CONTENT =================
            const timeValue = widget.querySelector('.time-value');
            const searchContent = widget.querySelector('.search-content');
            const newsContent = widget.querySelector('.news-content');
            const weatherTemp = widget.querySelector('.weather-temp');
            
            // TIME WIDGET SCALING
            if (timeValue) {
                timeValue.style.fontSize = Math.max(12, 18 * clampedTextScale) + 'px';
                timeValue.style.marginTop = Math.max(4, 6 * clampedElementScale) + 'px';
                timeValue.style.lineHeight = Math.max(1.1, 1.2 * clampedElementScale);
            }
            
            // SEARCH WIDGET COMPREHENSIVE SCALING
            if (searchContent) {
                const scaledFontSize = Math.max(10, Math.min(20, 13 * clampedTextScale));
                const scaledMaxHeight = Math.max(120, 200 * clampedElementScale); // UPDATED: Use new base height of 200px
                const scaledMinHeight = Math.max(40, 60 * clampedElementScale);   // UPDATED: Use new base height of 60px
                const scaledPaddingRight = Math.max(8, 12 * clampedElementScale); // UPDATED: Use new base padding of 12px
                const scaledPaddingBottom = Math.max(6, 8 * clampedElementScale); // ADDED: Scale bottom padding
                
                searchContent.style.fontSize = scaledFontSize + 'px';
                searchContent.style.lineHeight = Math.max(1.2, 1.4 * clampedElementScale);
                searchContent.style.marginTop = Math.max(4, 6 * clampedElementScale) + 'px';
                searchContent.style.paddingRight = scaledPaddingRight + 'px';
                searchContent.style.paddingLeft = Math.max(2, 4 * clampedElementScale) + 'px';
                searchContent.style.paddingBottom = scaledPaddingBottom + 'px'; // ADDED: Scale bottom padding
                searchContent.style.maxHeight = scaledMaxHeight + 'px';
                searchContent.style.minHeight = scaledMinHeight + 'px';
                
                // ADDED: Scale scrollbar width dynamically
                const scaledScrollbarWidth = Math.max(8, 10 * clampedElementScale);
                searchContent.style.setProperty('--scrollbar-width', scaledScrollbarWidth + 'px');
                
                // Ensure text uses full width during scaling with enhanced overflow handling
                searchContent.style.width = '100%';
                searchContent.style.boxSizing = 'border-box';
                searchContent.style.display = 'block';
                searchContent.style.textAlign = 'left';
                searchContent.style.overflowY = 'auto';        // ADDED: Ensure vertical scrolling
                searchContent.style.overflowX = 'hidden';      // ADDED: Prevent horizontal overflow
                searchContent.style.wordWrap = 'break-word';   // ADDED: Ensure word wrapping
                searchContent.style.whiteSpace = 'pre-wrap';   // ADDED: Preserve formatting while wrapping
                
                // Force reflow to apply changes immediately
                searchContent.offsetHeight;
                
                console.log(`🔍 Search content FULLY scaled: ${scaledFontSize}px, padding: ${scaledPaddingRight}px, height: ${scaledMaxHeight}px, scrollbar: ${scaledScrollbarWidth}px`);
            }
            
            // NEWS WIDGET COMPREHENSIVE SCALING
            if (newsContent) {
                const scaledFontSize = Math.max(10, Math.min(20, 13 * clampedTextScale));
                newsContent.style.fontSize = scaledFontSize + 'px';
                newsContent.style.lineHeight = Math.max(1.2, 1.4 * clampedElementScale);
                newsContent.style.marginTop = Math.max(4, 6 * clampedElementScale) + 'px';
                newsContent.style.paddingRight = Math.max(6, 8 * clampedElementScale) + 'px';
                newsContent.style.paddingLeft = Math.max(2, 4 * clampedElementScale) + 'px';
                newsContent.style.maxHeight = Math.max(80, 120 * clampedElementScale) + 'px';
                newsContent.style.minHeight = Math.max(30, 40 * clampedElementScale) + 'px';
                
                // Ensure text uses full width during scaling
                newsContent.style.width = '100%';
                newsContent.style.boxSizing = 'border-box';
                newsContent.style.display = 'block';
                newsContent.style.textAlign = 'left';
                
                // Force reflow to apply changes immediately
                newsContent.offsetHeight;
                
                console.log(`📰 News content FULLY scaled: ${scaledFontSize}px, padding: ${Math.max(6, 8 * clampedElementScale)}px, height: ${Math.max(80, 120 * clampedElementScale)}px`);
            }

            // NOTES WIDGET COMPREHENSIVE SCALING
            const notesTextarea = widget.querySelector('.note-textarea');
            const notesControls = widget.querySelector('.notes-controls');
            const notesBtns = widget.querySelectorAll('.notes-btn');
            const notesListContainer = widget.querySelector('.notes-list-container');

            if (notesTextarea) {
                const scaledFontSize = Math.max(11, Math.min(18, 13 * clampedTextScale));
                notesTextarea.style.fontSize = scaledFontSize + 'px';
                notesTextarea.style.lineHeight = Math.max(1.3, 1.4 * clampedElementScale);
                notesTextarea.style.padding = Math.max(8, 12 * clampedElementScale) + 'px';
                notesTextarea.style.minHeight = Math.max(150, 200 * clampedElementScale) + 'px';

                console.log(`📝 Notes textarea scaled: ${scaledFontSize}px, padding: ${Math.max(8, 12 * clampedElementScale)}px`);
            }

            if (notesBtns.length > 0) {
                notesBtns.forEach(btn => {
                    btn.style.fontSize = Math.max(10, Math.min(14, 11 * clampedTextScale)) + 'px';
                    btn.style.padding = Math.max(4, 6 * clampedElementScale) + 'px ' + Math.max(8, 12 * clampedElementScale) + 'px';
                });
            }

            if (notesListContainer) {
                const noteItems = notesListContainer.querySelectorAll('.note-item');
                noteItems.forEach(item => {
                    const preview = item.querySelector('.note-preview');
                    const meta = item.querySelector('.note-meta');
                    if (preview) {
                        preview.style.fontSize = Math.max(10, Math.min(16, 12 * clampedTextScale)) + 'px';
                        preview.style.lineHeight = Math.max(1.2, 1.3 * clampedElementScale);
                    }
                    if (meta) {
                        meta.style.fontSize = Math.max(8, Math.min(12, 10 * clampedTextScale)) + 'px';
                    }
                });
            }

            // WEATHER TEMPERATURE SCALING
            if (weatherTemp) {
                weatherTemp.style.fontSize = Math.max(16, 24 * clampedTextScale) + 'px';
                weatherTemp.style.marginTop = Math.max(4, 6 * clampedElementScale) + 'px';
                weatherTemp.style.lineHeight = Math.max(1.0, 1.1 * clampedElementScale);
            }

            // ================= SCALE ALL WEATHER DETAILS =================
            const weatherCondition = widget.querySelector('.weather-condition');
            const weatherFeelsLike = widget.querySelector('.weather-feels-like');
            const weatherLocation = widget.querySelector('.weather-location');
            const weatherDetails = widget.querySelectorAll('.weather-details span');
            const weatherIcon = widget.querySelector('.weather-icon');
            
            if (weatherCondition) {
                weatherCondition.style.fontSize = Math.max(8, 11 * clampedTextScale) + 'px';
                weatherCondition.style.marginTop = Math.max(2, 4 * clampedElementScale) + 'px';
                weatherCondition.style.lineHeight = Math.max(1.2, 1.3 * clampedElementScale);
            }
            if (weatherFeelsLike) {
                weatherFeelsLike.style.fontSize = Math.max(8, 11 * clampedTextScale) + 'px';
                weatherFeelsLike.style.marginTop = Math.max(2, 3 * clampedElementScale) + 'px';
                weatherFeelsLike.style.lineHeight = Math.max(1.2, 1.3 * clampedElementScale);
            }
            if (weatherLocation) {
                weatherLocation.style.fontSize = Math.max(8, 11 * clampedTextScale) + 'px';
                weatherLocation.style.marginTop = Math.max(2, 3 * clampedElementScale) + 'px';
                weatherLocation.style.lineHeight = Math.max(1.2, 1.3 * clampedElementScale);
            }
            if (weatherIcon) {
                const iconSize = Math.max(16, 24 * clampedElementScale);
                weatherIcon.style.width = iconSize + 'px';
                weatherIcon.style.height = iconSize + 'px';
                weatherIcon.style.fontSize = iconSize + 'px';
            }
            
            // Scale all weather detail spans
            weatherDetails.forEach(detail => {
                detail.style.fontSize = Math.max(7, 10 * clampedTextScale) + 'px';
                detail.style.padding = `${Math.max(1, 2 * clampedElementScale)}px ${Math.max(2, 4 * clampedElementScale)}px`;
                detail.style.margin = `${Math.max(1, 2 * clampedElementScale)}px`;
            });

            // ================= SCALE WIDGET CONTROLS =================
            const widgetControls = widget.querySelector('.widget-controls');
            const controlButtons = widget.querySelectorAll('.widget-control-btn');
            const resizeHandle = widget.querySelector('.resize-handle');
            const corners = widget.querySelectorAll('[class*="corner-"]');
            
            if (widgetControls) {
                widgetControls.style.padding = Math.max(2, 4 * clampedElementScale) + 'px';
                widgetControls.style.gap = Math.max(2, 4 * clampedElementScale) + 'px';
            }
            
            controlButtons.forEach(btn => {
                btn.style.fontSize = Math.max(10, 14 * clampedTextScale) + 'px';
                btn.style.width = Math.max(16, 20 * clampedElementScale) + 'px';
                btn.style.height = Math.max(16, 20 * clampedElementScale) + 'px';
                btn.style.lineHeight = Math.max(16, 20 * clampedElementScale) + 'px';
            });
            
            if (resizeHandle) {
                const handleSize = Math.max(12, 16 * clampedElementScale);
                resizeHandle.style.width = handleSize + 'px';
                resizeHandle.style.height = handleSize + 'px';
                resizeHandle.style.borderWidth = Math.max(1, 2 * clampedElementScale) + 'px';
            }
            
            corners.forEach(corner => {
                const cornerSize = Math.max(8, 12 * clampedElementScale);
                corner.style.width = cornerSize + 'px';
                corner.style.height = cornerSize + 'px';
                corner.style.borderWidth = Math.max(1, 2 * clampedElementScale) + 'px';
            });

            // ================= SCALE LOADING INDICATORS & SPECIAL ELEMENTS =================
            const searchLoading = widget.querySelector('.search-loading');
            const newsLoading = widget.querySelector('.news-loading');
            const weatherLoading = widget.querySelector('.weather-loading');
            
            if (searchLoading) {
                searchLoading.style.fontSize = Math.max(10, 13 * clampedTextScale) + 'px';
                searchLoading.style.padding = `${Math.max(4, 8 * clampedElementScale)}px`;
                searchLoading.style.marginTop = Math.max(4, 6 * clampedElementScale) + 'px';
            }
            if (newsLoading) {
                newsLoading.style.fontSize = Math.max(10, 13 * clampedTextScale) + 'px';
                newsLoading.style.padding = `${Math.max(4, 8 * clampedElementScale)}px`;
                newsLoading.style.marginTop = Math.max(4, 6 * clampedElementScale) + 'px';
            }
            if (weatherLoading) {
                weatherLoading.style.fontSize = Math.max(10, 13 * clampedTextScale) + 'px';
                weatherLoading.style.padding = `${Math.max(4, 8 * clampedElementScale)}px`;
                weatherLoading.style.marginTop = Math.max(4, 6 * clampedElementScale) + 'px';
            }

            // ================= SCALE SCROLLBARS =================
            const scrollableElements = widget.querySelectorAll('.search-content, .news-content');
            scrollableElements.forEach(element => {
                const scrollbarWidth = Math.max(6, 8 * clampedElementScale);
                element.style.setProperty('--scrollbar-width', scrollbarWidth + 'px');
                
                // Update scrollbar styles dynamically
                const style = document.createElement('style');
                style.textContent = `
                    #${widget.id} .search-content::-webkit-scrollbar,
                    #${widget.id} .news-content::-webkit-scrollbar {
                        width: ${scrollbarWidth}px;
                    }
                    #${widget.id} .search-content::-webkit-scrollbar-track,
                    #${widget.id} .news-content::-webkit-scrollbar-track {
                        margin: ${Math.max(2, 4 * clampedElementScale)}px 0;
                        border-radius: ${Math.max(2, 4 * clampedElementScale)}px;
                    }
                    #${widget.id} .search-content::-webkit-scrollbar-thumb,
                    #${widget.id} .news-content::-webkit-scrollbar-thumb {
                        border-radius: ${Math.max(2, 4 * clampedElementScale)}px;
                        border: ${Math.max(1, 1 * clampedElementScale)}px solid rgba(0, 255, 255, 0.3);
                    }
                `;
                
                // Remove old style if exists
                const oldStyle = document.querySelector(`#scrollbar-style-${widget.id}`);
                if (oldStyle) oldStyle.remove();
                
                style.id = `scrollbar-style-${widget.id}`;
                document.head.appendChild(style);
            });

            console.log(`📏 COMPREHENSIVE scaling applied to ${widget.id}: Text=${clampedTextScale.toFixed(2)}x, Elements=${clampedElementScale.toFixed(2)}x, Base=${avgScale.toFixed(2)}x`);
        }

        // ================= VIEWPORT SCALING FUNCTIONS =================
        
        function getViewportScale() {
            // Base viewport size (what the design was optimized for)
            const baseWidth = 1920;
            const baseHeight = 1080;
            
            // Current viewport size
            const currentWidth = window.innerWidth;
            const currentHeight = window.innerHeight;
            
            // Calculate scale factors
            const scaleX = currentWidth / baseWidth;
            const scaleY = currentHeight / baseHeight;
            
            // Use the smaller scale to ensure everything fits
            // But don't go below 0.5x or above 2.0x for readability
            const scale = Math.min(scaleX, scaleY);
            return Math.max(0.5, Math.min(2.0, scale));
        }
        
        function rescaleAllWidgets() {
            console.log('🔄 Rescaling all widgets for viewport change...');
            
            // Get all widgets
            const widgets = [
                document.getElementById('timeDisplay'),
                document.getElementById('weatherDisplay'),
                document.getElementById('searchWidget'),
                document.getElementById('newsWidget')
            ].filter(widget => widget && widget.style.display !== 'none');
            
            widgets.forEach(widget => {
                // Get current dimensions
                const rect = widget.getBoundingClientRect();
                const width = widget.style.width ? parseInt(widget.style.width) : rect.width;
                const height = widget.style.height ? parseInt(widget.style.height) : rect.height;
                
                // Reapply scaling
                applyDynamicTextScaling(widget, width, height);
            });
            
            console.log(`🔄 Rescaled ${widgets.length} widgets`);
        }
        
        // Debounced resize handler to avoid too many calls
        let resizeTimeout;
        function handleViewportResize() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                rescaleAllWidgets();
            }, 150); // Wait 150ms after resize stops
        }

        function loadWidgetPosition(widgetId) {
            const saved = localStorage.getItem(`widget_${widgetId}_position`);
            if (!saved) return;

            try {
                const position = JSON.parse(saved);
                const widget = document.getElementById(widgetId);
                if (!widget) return;

                widget.style.left = position.left;
                widget.style.top = position.top;
                
                // Restore custom size if manually resized
                if (position.width && position.height) {
                    widget.style.width = position.width;
                    widget.style.height = position.height;
                    // Apply dynamic text scaling for custom sizes
                    applyDynamicTextScaling(widget, parseInt(position.width), parseInt(position.height));
                }
                
                // Restore predefined size
                if (position.size !== undefined) {
                    widgetSizeIndex[widgetId] = position.size;
                    const currentSize = widgetSizes.find(size => widget.classList.contains(`widget-size-${size}`));
                    if (currentSize) {
                        widget.classList.remove(`widget-size-${currentSize}`);
                        widget.classList.remove(`widget-text-scale-${currentSize}`);
                    }
                    const newSize = widgetSizes[position.size];
                    widget.classList.add(`widget-size-${newSize}`);
                    widget.classList.add(`widget-text-scale-${newSize}`);
                }

                console.log(`📍 Restored position for ${widgetId}:`, position);
            } catch (e) {
                console.warn(`Failed to load position for ${widgetId}:`, e);
            }
        }

        // Initialize drag functionality
        function initializeDragAndResize() {
            const widgets = document.querySelectorAll('.widget-draggable');
            
            widgets.forEach(widget => {
                // Load saved position
                loadWidgetPosition(widget.id);
                
                // Mouse down on widget (start drag)
                widget.addEventListener('mousedown', handleWidgetMouseDown);
                
                // Mouse down on resize handle
                const resizeHandle = widget.querySelector('.resize-handle');
                if (resizeHandle) {
                    resizeHandle.addEventListener('mousedown', handleResizeMouseDown);
                }
            });

            // Global mouse events
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
        }

        function handleWidgetMouseDown(e) {
            // Don't drag if clicking on control buttons or resize handle
            if (e.target.classList.contains('widget-control-btn') || 
                e.target.classList.contains('resize-handle') ||
                e.target.closest('.widget-controls') ||
                e.target.closest('.resize-handle')) {
                return;
            }

            const widget = e.currentTarget;
            
            // Enhanced check: make sure we're not near the resize corner
            const rect = widget.getBoundingClientRect();
            const clickX = e.clientX - rect.left;
            const clickY = e.clientY - rect.top;
            const cornerSize = 25; // 25px corner area reserved for resize handle
            
            // Don't drag if clicking in bottom-right corner area (resize handle area)
            if (clickX > rect.width - cornerSize && clickY > rect.height - cornerSize) {
                console.log('🚫 Click in resize corner area, not dragging');
                return;
            }
            
            // Don't drag if clicking on interactive content
            if (e.target.closest('.nav-tab') || 
                e.target.closest('.control-btn') ||
                e.target.closest('.calc-btn') ||
                e.target.closest('input') ||
                e.target.closest('button')) {
                console.log('🚫 Click on interactive element, not dragging');
                return;
            }
            
            dragState.isDragging = true;
            dragState.currentWidget = widget;
            dragState.startX = e.clientX;
            dragState.startY = e.clientY;

            dragState.startLeft = rect.left;
            dragState.startTop = rect.top;

            widget.classList.add('widget-dragging');
            document.body.style.userSelect = 'none'; // Prevent text selection
            console.log('🔄 Starting drag for widget:', widget.id);
            e.preventDefault();
        }

        function handleResizeMouseDown(e) {
            const widget = e.target.closest('.widget-draggable');
            if (!widget) return;

            dragState.isResizing = true;
            dragState.currentWidget = widget;
            dragState.startX = e.clientX;
            dragState.startY = e.clientY;

            const rect = widget.getBoundingClientRect();
            dragState.startWidth = rect.width;
            dragState.startHeight = rect.height;

            widget.classList.add('widget-resizing');
            document.body.style.userSelect = 'none'; // Prevent text selection during resize
            document.body.style.cursor = 'se-resize'; // Change cursor during resize
            console.log('📏 Starting resize for widget:', widget.id);
            e.preventDefault();
            e.stopPropagation();
        }

        function handleMouseMove(e) {
            if (dragState.isDragging && dragState.currentWidget) {
                // Prevent text selection during drag
                e.preventDefault();
                
                // Store current mouse position for smooth interpolation
                dragState.currentX = e.clientX;
                dragState.currentY = e.clientY;
                
                // Use requestAnimationFrame for ultra-smooth dragging
                if (!dragState.animationFrame) {
                    dragState.animationFrame = requestAnimationFrame(() => {
                        const deltaX = dragState.currentX - dragState.startX;
                        const deltaY = dragState.currentY - dragState.startY;

                        const newLeft = Math.max(0, Math.min(window.innerWidth - 150, dragState.startLeft + deltaX));
                        const newTop = Math.max(0, Math.min(window.innerHeight - 100, dragState.startTop + deltaY));

                        // Use transform for ultra-smooth GPU-accelerated movement with better precision
                        const translateX = Math.round(newLeft - dragState.startLeft);
                        const translateY = Math.round(newTop - dragState.startTop);
                        
                        // Only translate, no scaling or rotation to prevent size changes
                        dragState.currentWidget.style.transform = `translate3d(${translateX}px, ${translateY}px, 0)`;
                        dragState.currentWidget.style.willChange = 'transform';
                        dragState.currentWidget.style.pointerEvents = 'none'; // Prevent interference
                        
                        // Update actual position for saving (optimized timing)
                        if (!dragState.positionUpdateFrame) {
                            dragState.positionUpdateFrame = setTimeout(() => {
                                if (dragState.currentWidget) {
                                        dragState.currentWidget.style.left = newLeft + 'px';
                                dragState.currentWidget.style.top = newTop + 'px';
                                }
                                dragState.positionUpdateFrame = null;
                            }, 32); // Optimized for smooth updates
                        }
                        
                        dragState.animationFrame = null;
                    });
                }
            }
            
            if (dragState.isResizing && dragState.currentWidget) {
                // Prevent text selection during resize
                e.preventDefault();
                
                const deltaX = e.clientX - dragState.startX;
                const deltaY = e.clientY - dragState.startY;

                // REMOVED: Minimum size restrictions - users can now resize to any size they want
                const newWidth = Math.max(20, dragState.startWidth + deltaX);  // Tiny minimum (20px) - almost no restriction
                const newHeight = Math.max(15, dragState.startHeight + deltaY); // Tiny minimum (15px) - almost no restriction

                // Use requestAnimationFrame for smooth resizing
                if (!dragState.resizeAnimationFrame) {
                    dragState.resizeAnimationFrame = requestAnimationFrame(() => {
                        if (dragState.currentWidget) {
                            dragState.currentWidget.style.width = newWidth + 'px';
                            dragState.currentWidget.style.height = newHeight + 'px';
                            
                            // Apply dynamic text scaling in real-time
                            applyDynamicTextScaling(dragState.currentWidget, newWidth, newHeight);
                        }
                        dragState.resizeAnimationFrame = null;
                    });
                }
            }
        }

        function handleMouseUp(e) {
            if (dragState.isDragging && dragState.currentWidget) {
                // Cancel any pending animation frames
                if (dragState.animationFrame) {
                    cancelAnimationFrame(dragState.animationFrame);
                    dragState.animationFrame = null;
                }
                if (dragState.positionUpdateFrame) {
                    clearTimeout(dragState.positionUpdateFrame);
                    dragState.positionUpdateFrame = null;
                }
                
                // Clean up performance optimizations
                dragState.currentWidget.style.willChange = 'auto';
                dragState.currentWidget.style.pointerEvents = 'auto'; // Restore interactions
                document.body.style.userSelect = 'auto'; // Restore text selection
                
                // Reset transform to normal state with smooth transition
                dragState.currentWidget.style.transform = '';
                dragState.currentWidget.classList.remove('widget-dragging');
                
                // Add a brief transition back to normal state
                dragState.currentWidget.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.2s ease, box-shadow 0.3s ease';
                setTimeout(() => {
                    if (dragState.currentWidget) {
                        dragState.currentWidget.style.transition = '';
                    }
                }, 300);
                
                saveWidgetPosition(dragState.currentWidget.id);
                console.log(`📍 Widget ${dragState.currentWidget.id} moved to: ${dragState.currentWidget.style.left}, ${dragState.currentWidget.style.top}`);
            }
            
            if (dragState.isResizing && dragState.currentWidget) {
                // Clean up resize animation frame
                if (dragState.resizeAnimationFrame) {
                    cancelAnimationFrame(dragState.resizeAnimationFrame);
                    dragState.resizeAnimationFrame = null;
                }
                
                dragState.currentWidget.classList.remove('widget-resizing');
                document.body.style.userSelect = 'auto'; // Restore text selection
                document.body.style.cursor = 'auto'; // Restore cursor
                saveWidgetPosition(dragState.currentWidget.id);
                console.log(`📏 Widget ${dragState.currentWidget.id} resized to: ${dragState.currentWidget.style.width} x ${dragState.currentWidget.style.height}`);
            }

            // Reset all drag state
            dragState.isDragging = false;
            dragState.isResizing = false;
            dragState.currentWidget = null;
            dragState.animationFrame = null;
            dragState.positionUpdateFrame = null;
            dragState.resizeAnimationFrame = null;
            dragState.currentX = 0;
            dragState.currentY = 0;
        }

        // Reset widget positions
        function resetAllWidgetPositions() {
            const widgets = document.querySelectorAll('.widget-draggable');
            widgets.forEach(widget => {
                widget.style.left = '';
                widget.style.top = '';
                widget.style.width = '';
                widget.style.height = '';
                
                // Reset size to normal
                widgetSizes.forEach(size => {
                    widget.classList.remove(`widget-size-${size}`);
                    widget.classList.remove(`widget-text-scale-${size}`);
                });
                widget.classList.add('widget-size-normal');
                widget.classList.add('widget-text-scale-normal');
                widgetSizeIndex[widget.id] = 1;
                
                // Reset all text elements to default font sizes
                resetTextScaling(widget);
                
                localStorage.removeItem(`widget_${widget.id}_position`);
            });
            console.log('🔄 All widget positions and text scaling reset');
        }

        function resetTextScaling(widget) {
            // Reset all text elements to their default sizes
            const elements = widget.querySelectorAll('*');
            elements.forEach(el => {
                el.style.fontSize = '';
                el.style.lineHeight = '';
            });
        }

        // Initialize drag and resize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeDragAndResize();
            
            // Initialize news widget elements
            const newsWidget = document.getElementById('newsWidget');
            const newsContentArea = document.getElementById('newsContentArea');
            const newsCount = document.getElementById('newsCount');
            
            if (newsWidget && newsContentArea && newsCount) {
                console.log('✅ News widget elements initialized with Italy time support');
                // Set initial news count
                newsCount.textContent = '0 stories';
                
                // Note: Italy time will start automatically when news widget is shown
                // No dates will be displayed in news articles
                // Full content will be shown without summarization unless specifically requested
            } else {
                console.warn('❌ News widget elements not found:', {
                    newsWidget: !!newsWidget,
                    newsContentArea: !!newsContentArea,
                    newsCount: !!newsCount
                });
            }

            // Initialize notes widget elements and event listeners
            if (saveNoteBtn) {
                saveNoteBtn.addEventListener('click', saveNote);
                console.log('✅ Save button event listener added');
            }
            if (copyNoteBtn) {
                copyNoteBtn.addEventListener('click', copyNote);
                console.log('✅ Copy button event listener added');
            }
            if (viewNotesBtn) {
                viewNotesBtn.addEventListener('click', showSavedNotes);
                console.log('✅ View Notes button event listener added');
            }
            if (newNoteBtn) {
                newNoteBtn.addEventListener('click', newNote);
                console.log('✅ New Note button event listener added');
            }
            if (clearNoteBtn) {
                clearNoteBtn.addEventListener('click', clearNote);
                console.log('✅ Clear button event listener added');
            }
            if (backToEditorBtn) {
                backToEditorBtn.addEventListener('click', backToEditor);
                console.log('✅ Back button event listener added');
            }

            // Add keyboard shortcut for saving notes (Ctrl+S)
            if (noteTextarea) {
                noteTextarea.addEventListener('keydown', function(e) {
                    if (e.ctrlKey && e.key === 's') {
                        e.preventDefault();
                        saveNote();
                    }
                });

                // Add click event listener for debugging
                noteTextarea.addEventListener('click', function(e) {
                    console.log('📝 Textarea clicked!', e);
                    noteTextarea.focus();
                });

                // Add focus event listener for debugging
                noteTextarea.addEventListener('focus', function(e) {
                    console.log('📝 Textarea focused!', e);
                });

                // Add input event listener for debugging
                noteTextarea.addEventListener('input', function(e) {
                    console.log('📝 Textarea input detected:', e.target.value);
                });

                // Make sure textarea is not disabled or readonly
                noteTextarea.disabled = false;
                noteTextarea.readOnly = false;
                noteTextarea.tabIndex = 0;

                console.log('📝 Textarea event listeners added and properties set');
            }

            console.log('✅ Notes widget initialized with event listeners');

            // Debug: Check if all elements are found
            console.log('📝 Notes widget elements check:', {
                notesWidget: !!notesWidget,
                noteTextarea: !!noteTextarea,
                saveNoteBtn: !!saveNoteBtn,
                copyNoteBtn: !!copyNoteBtn,
                viewNotesBtn: !!viewNotesBtn,
                clearNoteBtn: !!clearNoteBtn
            });
        });
        
        // Add window resize listener for viewport scaling
        window.addEventListener('resize', handleViewportResize);
        
        // ================= AI WIDGET MOVEMENT COMMANDS =================
        
        function moveWidgetByCommand(widgetName, direction, amount = 'normal') {
            // Enhanced widget name mapping with more variations
            const widgetMap = {
                'time': 'timeDisplay',
                'time display': 'timeDisplay',
                'time widget': 'timeDisplay',
                'clock': 'timeDisplay',
                'timer': 'timeDisplay',
                'weather': 'weatherDisplay', 
                'weather display': 'weatherDisplay',
                'weather widget': 'weatherDisplay',
                'weather info': 'weatherDisplay',
                'forecast': 'weatherDisplay',
                'search': 'searchWidget',
                'search box': 'searchWidget',
                'search bar': 'searchWidget',
                'search display': 'searchWidget',
                'search widget': 'searchWidget',
                'news': 'newsWidget',
                'news display': 'newsWidget',
                'news widget': 'newsWidget',
                'news feed': 'newsWidget',
                'headlines': 'newsWidget',
                'notes': 'notesWidget',
                'notes widget': 'notesWidget',
                'notepad': 'notesWidget',
                'note taking': 'notesWidget',
                'write notes': 'notesWidget'
            };
            
            // Direction mapping
            const directionMap = {
                'up': { x: 0, y: -1 },
                'down': { x: 0, y: 1 },
                'left': { x: -1, y: 0 },
                'right': { x: 1, y: 0 },
                'top': { x: 0, y: -1 },
                'bottom': { x: 0, y: 1 },
                'center': { x: 0, y: 0, special: 'center' },
                'middle': { x: 0, y: 0, special: 'center' }
            };
            
            // Amount mapping (pixels)
            const amountMap = {
                'tiny': 10,
                'small': 25,
                'little': 25,
                'bit': 50,
                'normal': 100,
                'much': 150,
                'lot': 200,
                'far': 250
            };
            
            const widgetId = widgetMap[widgetName.toLowerCase()];
            const widget = document.getElementById(widgetId);
            
            if (!widget) {
                console.log(`❌ Widget "${widgetName}" not found or not visible`);
                return `I couldn't find the ${widgetName} widget. Make sure it's currently displayed.`;
            }
            
            const directionData = directionMap[direction.toLowerCase()];
            if (!directionData) {
                console.log(`❌ Direction "${direction}" not recognized`);
                return `I don't understand the direction "${direction}". Try: up, down, left, right, center.`;
            }
            
            const moveAmount = amountMap[amount.toLowerCase()] || amountMap['normal'];
            
            // Get current position
            const rect = widget.getBoundingClientRect();
            let currentLeft = parseInt(widget.style.left) || rect.left;
            let currentTop = parseInt(widget.style.top) || rect.top;
            
            let newLeft, newTop;
            
            if (directionData.special === 'center') {
                // Center the widget
                newLeft = (window.innerWidth - rect.width) / 2;
                newTop = (window.innerHeight - rect.height) / 2;
            } else {
                // Move in specified direction
                newLeft = currentLeft + (directionData.x * moveAmount);
                newTop = currentTop + (directionData.y * moveAmount);
            }
            
            // Ensure widget stays within viewport
            newLeft = Math.max(0, Math.min(window.innerWidth - rect.width, newLeft));
            newTop = Math.max(0, Math.min(window.innerHeight - rect.height, newTop));
            
            // Animate the movement
            animateWidgetMovement(widget, newLeft, newTop);
            
            // Save the new position
            setTimeout(() => {
                saveWidgetPosition(widgetId);
            }, 500);
            
            const directionText = directionData.special === 'center' ? 'to center' : direction;
            console.log(`📍 Moving ${widgetName} ${directionText} by ${moveAmount}px`);
            return `✅ Moved ${widgetName} ${directionText}${directionData.special !== 'center' ? ` by ${moveAmount} pixels` : ''}`;
        }
        
        function animateWidgetMovement(widget, newLeft, newTop) {
            // Add AI movement animation class
            widget.classList.add('widget-ai-moving');
            
            // Start the movement after a brief delay for visual effect
            setTimeout(() => {
                widget.style.left = newLeft + 'px';
                widget.style.top = newTop + 'px';
            }, 100);
            
            // Remove animation class after movement completes
            setTimeout(() => {
                widget.classList.remove('widget-ai-moving');
            }, 700);
        }
        
        function processWidgetMovementCommand(message) {
            // Enhanced natural language movement command patterns with more widget variations
            const widgetPattern = '(time|clock|timer|weather|forecast|search|news|headlines)';
            
            const patterns = [
                // Natural conversational patterns with "hey nova" or similar
                new RegExp(`(?:hey\\s+)?(?:nova|ai)?\\s*,?\\s*move\\s+(?:the\\s+)?${widgetPattern}(?:\\s+(?:display|widget|info|feed|box|bar))?\\s+(?:to\\s+(?:the\\s+)?)?(up|down|left|right|top|bottom|center|middle)(?:\\s+(?:a\\s+)?(tiny|small|little|bit|much|lot|far))?`, 'i'),
                
                // All other patterns using the enhanced widget pattern
                new RegExp(`move\\s+(?:the\\s+)?${widgetPattern}(?:\\s+(?:display|widget|info|feed|box|bar))?\\s+to\\s+(?:the\\s+)?(up|down|left|right|top|bottom|center|middle)(?:\\s+(?:a\\s+)?(tiny|small|little|bit|much|lot|far))?`, 'i'),
                new RegExp(`move\\s+(?:the\\s+)?${widgetPattern}(?:\\s+(?:display|widget|info|feed|box|bar))?\\s+(up|down|left|right|top|bottom|center|middle)(?:\\s+(?:a\\s+)?(tiny|small|little|bit|much|lot|far))?`, 'i'),
                new RegExp(`move\\s+(?:the\\s+)?${widgetPattern}(?:\\s+(?:display|widget|info|feed|box|bar))?\\s+(?:a\\s+)?(tiny|small|little|bit|much|lot|far)\\s+(up|down|left|right|top|bottom)`, 'i'),
                new RegExp(`(?:the\\s+)?${widgetPattern}(?:\\s+(?:display|widget|info|feed|box|bar))?\\s+move\\s+(?:to\\s+(?:the\\s+)?)?(up|down|left|right|top|bottom|center|middle)(?:\\s+(?:a\\s+)?(tiny|small|little|bit|much|lot|far))?`, 'i'),
                new RegExp(`(?:shift|slide|drag)\\s+(?:the\\s+)?${widgetPattern}(?:\\s+(?:display|widget|info|feed|box|bar))?\\s+(?:to\\s+(?:the\\s+)?)?(up|down|left|right|top|bottom)(?:\\s+(?:a\\s+)?(tiny|small|little|bit|much|lot|far))?`, 'i'),
                new RegExp(`(?:center|middle)\\s+(?:the\\s+)?${widgetPattern}(?:\\s+(?:display|widget|info|feed|box|bar))?`, 'i'),
                new RegExp(`put\\s+(?:the\\s+)?${widgetPattern}(?:\\s+(?:display|widget|info|feed|box|bar))?\\s+(?:in\\s+(?:the\\s+)?|on\\s+(?:the\\s+)?|at\\s+(?:the\\s+)?)?(center|middle|top|bottom|left|right)`, 'i'),
                new RegExp(`(?:the\\s+)?${widgetPattern}(?:\\s+(?:display|widget|info|feed|box|bar))?\\s+to\\s+(?:the\\s+)?(up|down|left|right|top|bottom|center|middle)(?:\\s+(?:a\\s+)?(tiny|small|little|bit|much|lot|far))?`, 'i'),
                new RegExp(`(?:can\\s+you\\s+|could\\s+you\\s+|please\\s+)?move\\s+(?:the\\s+)?${widgetPattern}(?:\\s+(?:display|widget|info|feed|box|bar))?\\s+(?:to\\s+(?:the\\s+)?)?(up|down|left|right|top|bottom|center|middle)(?:\\s+(?:a\\s+)?(tiny|small|little|bit|much|lot|far))?`, 'i'),
                new RegExp(`(?:the\\s+)?${widgetPattern}(?:\\s+(?:display|widget|info|feed|box|bar))?\\s+(?:needs?\\s+to\\s+go|should\\s+go|go)\\s+(?:to\\s+(?:the\\s+)?)?(up|down|left|right|top|bottom|center|middle)(?:\\s+(?:a\\s+)?(tiny|small|little|bit|much|lot|far))?`, 'i'),
                new RegExp(`take\\s+(?:the\\s+)?${widgetPattern}(?:\\s+(?:display|widget|info|feed|box|bar))?\\s+(?:and\\s+(?:move\\s+it\\s+)?)?(?:to\\s+(?:the\\s+)?)?(up|down|left|right|top|bottom|center|middle)(?:\\s+(?:a\\s+)?(tiny|small|little|bit|much|lot|far))?`, 'i')
            ];
            
            for (const pattern of patterns) {
                const match = message.match(pattern);
                if (match) {
                    let widgetName = match[1] || 'unknown';
                    let direction = 'center';
                    let amount = 'normal';
                    
                    // Smart pattern detection
                    const patternSource = pattern.source;
                    
                    if (patternSource.includes('center|middle') || (patternSource.includes('center') && !patternSource.includes('put'))) {
                        // Center/middle patterns
                        direction = 'center';
                        amount = 'normal';
                    } else if (patternSource.includes('put')) {
                        // "put [widget] in/on/at [direction]" patterns
                        direction = match[2] || 'center';
                        amount = 'normal';
                    } else {
                        // All other movement patterns - find direction and amount
                        const directionWords = ['up', 'down', 'left', 'right', 'top', 'bottom', 'center', 'middle'];
                        const amountWords = ['tiny', 'small', 'little', 'bit', 'much', 'lot', 'far'];
                        
                        let foundDirection = null;
                        let foundAmount = null;
                        
                        // Search through all match groups for direction and amount
                        for (let i = 2; i < match.length; i++) {
                            if (match[i]) {
                                const word = match[i].toLowerCase();
                                if (directionWords.includes(word)) {
                                    foundDirection = word;
                                } else if (amountWords.includes(word)) {
                                    foundAmount = word;
                                }
                            }
                        }
                        
                        direction = foundDirection || 'center';
                        amount = foundAmount || 'normal';
                    }
                    
                    return moveWidgetByCommand(widgetName, direction, amount);
                }
            }
            
            return null; // No movement command found
        }

        // Enhanced scrolling with keyboard support
        function addScrollKeyboardSupport() {
            const scrollableElements = document.querySelectorAll('.search-content, .news-content');
            
            scrollableElements.forEach(element => {
                // Make elements focusable
                element.setAttribute('tabindex', '0');
                
                // Add keyboard event listener
                element.addEventListener('keydown', (e) => {
                    const scrollAmount = 30; // pixels to scroll
                    
                    switch(e.key) {
                        case 'ArrowUp':
                            e.preventDefault();
                            element.scrollTop -= scrollAmount;
                            break;
                        case 'ArrowDown':
                            e.preventDefault();
                            element.scrollTop += scrollAmount;
                            break;
                        case 'PageUp':
                            e.preventDefault();
                            element.scrollTop -= element.clientHeight * 0.8;
                            break;
                        case 'PageDown':
                            e.preventDefault();
                            element.scrollTop += element.clientHeight * 0.8;
                            break;
                        case 'Home':
                            e.preventDefault();
                            element.scrollTop = 0;
                            break;
                        case 'End':
                            e.preventDefault();
                            element.scrollTop = element.scrollHeight;
                            break;
                    }
                });
                
                // Add visual focus indicator
                element.addEventListener('focus', () => {
                    element.style.outline = '2px solid rgba(0, 255, 255, 0.5)';
                    element.style.outlineOffset = '2px';
                });
                
                element.addEventListener('blur', () => {
                    element.style.outline = 'none';
                });
            });
        }
        
        // Initial scaling when page loads
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                rescaleAllWidgets();
                addScrollKeyboardSupport();
                console.log('🎯 Initial widget scaling and scroll support applied');
            }, 500); // Wait for widgets to be fully loaded
        });
        
        // ================= MESSAGE INTERCEPTION FOR WIDGET COMMANDS =================
        
        // Intercept messages to check for widget movement commands
        function interceptMessage(message) {
            // Check for widget movement commands first
            const movementResult = processWidgetMovementCommand(message);
            if (movementResult) {
                // Display the result in chat
                displayMovementResult(movementResult);
                return true; // Command was handled
            }
            return false; // Not a movement command, continue normal processing
        }
        
        function displayMovementResult(result) {
            // Create a temporary message element to show the result
            const messagesContainer = document.querySelector('.chat-messages');
            if (messagesContainer) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message ai-message';
                messageDiv.innerHTML = `
                    <div class="message-content">
                        <div class="message-text">${result}</div>
                    </div>
                `;
                messagesContainer.appendChild(messageDiv);
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
                
                // Auto-remove after 3 seconds
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.remove();
                    }
                }, 3000);
            }
        }
        
        // Global function to be called from external scripts
        window.checkWidgetMovementCommand = function(message) {
            return interceptMessage(message);
        };
        
        // Also expose the movement function globally for direct calls
        window.moveWidget = function(widgetName, direction, amount) {
            return moveWidgetByCommand(widgetName, direction, amount);
        };

        // ================= COPY FUNCTIONALITY =================
        
        function copySearchContent(text, button) {
            navigator.clipboard.writeText(text).then(() => {
                showCopySuccess(button, 'Search results copied!');
                console.log('🔍 Search content copied to clipboard');
            }).catch(err => {
                console.error('❌ Failed to copy search content:', err);
                showCopyError(button, 'Failed to copy');
            });
        }
        
        function copyNewsContent(text, button) {
            navigator.clipboard.writeText(text).then(() => {
                showCopySuccess(button, 'Article copied!');
                console.log('📰 News content copied to clipboard');
            }).catch(err => {
                console.error('❌ Failed to copy news content:', err);
                showCopyError(button, 'Failed to copy');
            });
        }
        
        function showCopySuccess(button, message) {
            const originalHTML = button.innerHTML;
            const originalClass = button.className;
            
            // Change to checkmark icon
            button.innerHTML = `
                <svg viewBox="0 0 24 24">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                </svg>
            `;
            button.className = originalClass + ' copied';
            button.title = message;
            
            // Reset after 2 seconds
            setTimeout(() => {
                button.innerHTML = originalHTML;
                button.className = originalClass;
                button.title = button.title.includes('search') ? 'Copy search results' : 'Copy news article';
            }, 2000);
        }
        
        function showCopyError(button, message) {
            const originalTitle = button.title;
            button.title = message;
            
            // Flash red briefly
            button.style.background = 'linear-gradient(135deg, rgba(255, 0, 0, 0.8) 0%, rgba(255, 100, 100, 0.9) 100%)';
            
            setTimeout(() => {
                button.style.background = '';
                button.title = originalTitle;
            }, 1500);
        }
    </script>
</body>
</html> 