#!/usr/bin/env python3
"""
Offline demo of screen capture functionality without API calls
Shows how the screen capture works and provides mock AI responses
"""

import os
import sys
import base64
import time
from pathlib import Path
from io import BytesIO
import mss
from PIL import Image

class OfflineScreenDemo:
    """Offline demo of screen capture with mock AI responses"""
    
    def __init__(self):
        """Initialize the offline demo"""
        self.demo_responses = [
            """I can see a terminal window with code and text. The main content shows:

1. **Main Content**: A command prompt/terminal interface with Python code execution
2. **UI Elements**: Dark terminal background, white/green text, command line interface
3. **Text Content**: Python script output, file paths, and system messages
4. **Activity**: Software development or system administration work
5. **Context**: Programming/development environment on Windows
6. **Notable Details**: Multiple lines of output, file system navigation, script execution

This appears to be a developer working with Python scripts and terminal commands.""",

            """I can see a code editor or development environment. The screen shows:

1. **Main Content**: Text editor or IDE with code files open
2. **UI Elements**: Menu bars, file explorer, editor panes
3. **Text Content**: Programming code with syntax highlighting
4. **Activity**: Active coding or software development
5. **Context**: Professional development workflow
6. **Notable Details**: Multiple files open, organized workspace, development tools

This looks like an active programming session with code editing in progress.""",

            """I can see a desktop environment with various applications. The content includes:

1. **Main Content**: Desktop with multiple windows and applications
2. **UI Elements**: Taskbar, window frames, application interfaces
3. **Text Content**: Various text in different applications and windows
4. **Activity**: Multitasking with several programs open
5. **Context**: General computer usage and productivity work
6. **Notable Details**: Organized workspace, multiple active applications

This appears to be a typical work session with multiple applications running."""
        ]
        self.response_index = 0
    
    def capture_screen(self, monitor_number=1):
        """Capture the screen using mss"""
        try:
            with mss.mss() as sct:
                # Get monitor info
                monitors = sct.monitors
                if monitor_number >= len(monitors):
                    monitor_number = 1
                
                monitor = monitors[monitor_number]
                print(f"📸 Capturing screen {monitor_number}: {monitor['width']}x{monitor['height']}")
                
                # Capture screenshot
                screenshot = sct.grab(monitor)
                
                # Convert to PIL Image
                img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
                
                # Save a demo screenshot (optional)
                demo_path = Path("demo_screenshot.png")
                img.save(demo_path)
                print(f"💾 Screenshot saved as: {demo_path}")
                
                print(f"✅ Screen captured successfully: {img.size[0]}x{img.size[1]} pixels")
                return img.size
                
        except Exception as e:
            print(f"❌ Screen capture failed: {e}")
            print("💡 Make sure mss is installed: pip install mss")
            return None
    
    def get_mock_analysis(self, question=None):
        """Get a mock AI analysis response"""
        # Simulate AI processing time
        print("🧠 Analyzing with AI (Demo Mode)...")
        time.sleep(2)  # Simulate processing time
        
        if question and question.lower().strip():
            # Custom response for specific questions
            if "error" in question.lower():
                return "I don't see any obvious error messages on your screen. The terminal output appears to show normal program execution."
            elif "language" in question.lower() or "programming" in question.lower():
                return "The code visible on your screen appears to be **Python**. I can see Python syntax, imports, and typical Python development patterns."
            elif "document" in question.lower():
                return "I can see what appears to be code or terminal output rather than a traditional document. This looks like a development environment."
            else:
                return f"Based on your question '{question}', I can see relevant content on your screen that relates to your query. The screen shows development-related content and terminal interfaces."
        
        # Get rotating demo response
        response = self.demo_responses[self.response_index]
        self.response_index = (self.response_index + 1) % len(self.demo_responses)
        return response
    
    def print_analysis_result(self, analysis, size=None, question=None):
        """Print the analysis result in a formatted way"""
        print("\n" + "="*70)
        print("🤖 AI ANALYSIS RESULTS (DEMO MODE)")
        print("="*70)
        
        # Print metadata
        meta_info = ["Type: demo"]
        if size:
            meta_info.append(f"Size: {size[0]}x{size[1]}")
        meta_info.append("Model: offline-demo")
        
        print(f"📊 {' | '.join(meta_info)}")
        print("-"*70)
        
        # Print analysis
        print(analysis)
        print("="*70)
        print("💡 This is a demo response. For real AI analysis, ensure your API quota is available.")
        print("="*70)
    
    def interactive_mode(self):
        """Run interactive demo mode"""
        print("\n" + "="*70)
        print("🚀 GINI 1.5 - OFFLINE DEMO MODE")
        print("="*70)
        print("📸 Screen capture with mock AI responses")
        print("💡 No API calls - perfect for testing screen capture functionality")
        print("="*70)
        print()
        print("💡 Commands:")
        print("  - 'what do you see on my screen?' - Capture screen + mock analysis")
        print("  - 'capture' - Just capture screen without analysis")
        print("  - 'demo' - Show mock analysis without capture")
        print("  - 'help' - Show this help")
        print("  - 'quit' or 'exit' - Exit the program")
        print()
        
        while True:
            try:
                user_input = input("🎤 You: ").strip().lower()
                
                if not user_input:
                    continue
                
                if user_input in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                elif user_input == 'help':
                    self.show_help()
                
                elif user_input == 'capture':
                    size = self.capture_screen()
                    if size:
                        print(f"✅ Screen capture completed: {size[0]}x{size[1]} pixels")
                
                elif user_input == 'demo':
                    analysis = self.get_mock_analysis()
                    self.print_analysis_result(analysis)
                
                elif 'what do you see on my screen' in user_input or 'whats on my screen' in user_input:
                    # Full demo: capture + analysis
                    size = self.capture_screen()
                    if size:
                        analysis = self.get_mock_analysis()
                        self.print_analysis_result(analysis, size)
                
                elif '?' in user_input:
                    # Custom question
                    size = self.capture_screen()
                    if size:
                        analysis = self.get_mock_analysis(user_input)
                        self.print_analysis_result(analysis, size, user_input)
                
                else:
                    print("❓ I didn't understand that. Type 'help' for commands.")
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def show_help(self):
        """Show help information"""
        print("\n" + "="*70)
        print("📖 HELP - OFFLINE DEMO COMMANDS")
        print("="*70)
        print()
        print("🖥️  SCREEN CAPTURE + ANALYSIS:")
        print("   'what do you see on my screen?'")
        print("   'whats on my screen?'")
        print("   'What errors do you see?' (custom question)")
        print()
        print("📸 SCREEN CAPTURE ONLY:")
        print("   'capture' - Just capture screen, save as demo_screenshot.png")
        print()
        print("🤖 MOCK ANALYSIS ONLY:")
        print("   'demo' - Show sample AI response without capture")
        print()
        print("🎮 CONTROLS:")
        print("   'help' - Show this help")
        print("   'quit' or 'exit' - Exit program")
        print()
        print("💡 NOTE: This is offline demo mode with mock responses.")
        print("   For real AI analysis, use the full system with API quota.")
        print("="*70)

def main():
    """Main demo function"""
    # Check if mss is available
    try:
        import mss
    except ImportError:
        print("❌ mss package required for screen capture")
        print("💡 Install with: pip install mss")
        return
    
    print("🚀 GINI 1.5 - OFFLINE SCREEN CAPTURE DEMO")
    print("="*50)
    print("📸 Test screen capture functionality without API calls")
    print("🤖 Mock AI responses for demonstration")
    print("💡 Perfect for testing when API quota is exceeded")
    print("="*50)
    
    demo = OfflineScreenDemo()
    
    if len(sys.argv) > 1:
        # Command line mode
        command = ' '.join(sys.argv[1:]).lower()
        
        if 'screen' in command or 'what' in command:
            size = demo.capture_screen()
            if size:
                analysis = demo.get_mock_analysis(command if '?' in command else None)
                demo.print_analysis_result(analysis, size)
        elif command == 'capture':
            demo.capture_screen()
        else:
            print(f"❓ Unknown command: {command}")
            print("💡 Try: python offline_screen_demo.py 'what do you see on my screen?'")
    else:
        # Interactive mode
        demo.interactive_mode()

if __name__ == "__main__":
    main()
