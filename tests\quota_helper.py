#!/usr/bin/env python3
"""
Quota Helper for Gini 1.5 Screen Analyzer
Helps manage API quota and provides alternatives when quota is exceeded
"""

import os
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path

def check_quota_status():
    """Check API quota status and provide recommendations"""
    print("🔍 GOOGLE GEMINI API QUOTA CHECKER")
    print("="*50)
    
    api_key = os.getenv('GOOGLE_AI_API_KEY') or os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ No API key found")
        return False
    
    print(f"✅ API Key: {api_key[:10]}...{api_key[-4:]}")
    
    try:
        # Try to import and test connection
        import google.generativeai as genai
        from PIL import Image
        
        genai.configure(api_key=api_key)
        
        # Test with different models
        models_to_test = [
            'gemini-1.5-flash',  # Lower quota usage
            'gemini-pro-vision', # Alternative model
            'gemini-1.5-pro'     # Original model
        ]
        
        working_models = []
        
        for model_name in models_to_test:
            try:
                print(f"\n🧪 Testing {model_name}...")
                model = genai.GenerativeModel(model_name)
                
                # Create tiny test image to minimize quota usage
                test_image = Image.new('RGB', (1, 1), color='white')
                
                response = model.generate_content(
                    ["Color?", test_image],
                    generation_config=genai.types.GenerationConfig(
                        max_output_tokens=5,  # Minimal tokens
                        temperature=0.1
                    )
                )
                
                print(f"✅ {model_name} - Working")
                working_models.append(model_name)
                
            except Exception as e:
                error_str = str(e)
                if "429" in error_str or "quota" in error_str.lower():
                    print(f"❌ {model_name} - Quota exceeded")
                elif "404" in error_str:
                    print(f"⚠️  {model_name} - Model not available")
                else:
                    print(f"❌ {model_name} - Error: {str(e)[:50]}...")
        
        return working_models
        
    except ImportError:
        print("❌ google-generativeai not installed")
        return False
    except Exception as e:
        print(f"❌ Error testing quota: {e}")
        return False

def show_quota_solutions():
    """Show solutions for quota issues"""
    print("\n" + "="*60)
    print("💡 QUOTA EXCEEDED SOLUTIONS")
    print("="*60)
    
    print("\n🕐 IMMEDIATE SOLUTIONS:")
    print("   1. Wait 5-10 minutes and try again")
    print("   2. Use offline demo mode:")
    print("      python offline_screen_demo.py")
    print("   3. Try different model (if available):")
    print("      - gemini-1.5-flash (lower quota usage)")
    print("      - gemini-pro-vision (alternative)")
    
    print("\n🔑 API KEY SOLUTIONS:")
    print("   1. Create new API key at: https://aistudio.google.com/app/apikey")
    print("   2. Use different Google account")
    print("   3. Check quota limits at: https://aistudio.google.com/app/quota")
    
    print("\n💳 UPGRADE OPTIONS:")
    print("   1. Upgrade to paid plan for higher quotas")
    print("   2. Enable billing at: https://console.cloud.google.com/billing")
    print("   3. Request quota increase if needed")
    
    print("\n⚙️  OPTIMIZATION TIPS:")
    print("   1. Use smaller images when possible")
    print("   2. Ask more specific questions (shorter responses)")
    print("   3. Batch multiple questions together")
    print("   4. Use 'quick' analysis mode instead of 'detailed'")

def show_alternative_tools():
    """Show alternative tools when API is unavailable"""
    print("\n" + "="*60)
    print("🛠️  ALTERNATIVE TOOLS")
    print("="*60)
    
    print("\n📸 SCREEN CAPTURE ONLY:")
    print("   python offline_screen_demo.py capture")
    print("   - Captures screen and saves as demo_screenshot.png")
    print("   - No AI analysis, just screen capture functionality")
    
    print("\n🤖 MOCK AI DEMO:")
    print("   python offline_screen_demo.py")
    print("   - Full interactive demo with mock AI responses")
    print("   - Test all functionality without API calls")
    
    print("\n🌐 WEB INTERFACE:")
    print("   python demo_gini_15.py")
    print("   - Beautiful web interface (may also hit quota)")
    print("   - Try if terminal quota is separate from web quota")
    
    print("\n🔧 MANUAL ANALYSIS:")
    print("   1. Take screenshot manually (Print Screen key)")
    print("   2. Save image and describe it yourself")
    print("   3. Use other AI tools like ChatGPT with image upload")

def reset_quota_tracking():
    """Reset local quota tracking (if implemented)"""
    print("\n🔄 QUOTA RESET OPTIONS:")
    print("   1. Wait for daily quota reset (usually midnight UTC)")
    print("   2. Wait for minute quota reset (1 minute)")
    print("   3. Try different API key")
    print("   4. Use offline demo mode while waiting")

def main():
    """Main quota helper function"""
    print("🚀 GINI 1.5 - QUOTA HELPER")
    print("="*40)
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'check':
            working_models = check_quota_status()
            if working_models:
                print(f"\n✅ Working models: {', '.join(working_models)}")
            else:
                show_quota_solutions()
        
        elif command == 'solutions':
            show_quota_solutions()
        
        elif command == 'alternatives':
            show_alternative_tools()
        
        elif command == 'demo':
            print("🚀 Starting offline demo...")
            os.system("python offline_screen_demo.py")
        
        else:
            print(f"❓ Unknown command: {command}")
            print("💡 Available commands: check, solutions, alternatives, demo")
    
    else:
        # Interactive mode
        print("💡 What would you like to do?")
        print("   1. Check quota status")
        print("   2. Show quota solutions")
        print("   3. Show alternative tools")
        print("   4. Start offline demo")
        print("   5. Exit")
        
        try:
            choice = input("\nEnter choice (1-5): ").strip()
            
            if choice == "1":
                working_models = check_quota_status()
                if not working_models:
                    show_quota_solutions()
            elif choice == "2":
                show_quota_solutions()
            elif choice == "3":
                show_alternative_tools()
            elif choice == "4":
                print("🚀 Starting offline demo...")
                os.system("python offline_screen_demo.py")
            elif choice == "5":
                print("👋 Goodbye!")
            else:
                print("❌ Invalid choice")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")

if __name__ == "__main__":
    main()
