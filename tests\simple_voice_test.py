#!/usr/bin/env python3
"""
Simple test for AI voice filtering
"""

import json
import re

def should_skip_response(response: str) -> bool:
    """Check if response should be skipped (UI-specific responses)"""
    if not response or len(response.strip()) < 3:
        return True
        
    # Skip responses that are UI commands or contain special formatting
    skip_patterns = [
        "TIME_DISPLAY_SHOW:",
        "WEATHER_DISPLAY_SHOW:",
        "SEARCH_RESULT:",
        "NEWS_RESULT:",
        "WEATHER_DATA:",
        "WIDGET_TIME:",
        "I apologize, but I encountered an error generating a response",
        "Search error:",
        "Sorry, I couldn't get weather information",
        "Sorry, I couldn't get the time",
    ]
    
    response_upper = response.upper()
    for pattern in skip_patterns:
        if pattern.upper() in response_upper:
            return True
            
    # Skip responses that are mostly JSON data
    if response.strip().startswith('{') and response.strip().endswith('}'):
        return True
        
    # Skip responses that contain large amounts of structured data
    if '"temperature":' in response or '"humidity":' in response:
        return True
        
    return False

def clean_response_for_speech(response: str) -> str:
    """Clean response text for better speech synthesis"""
    if not response:
        return ""
        
    # Remove code blocks and markdown formatting
    response = re.sub(r'```[\s\S]*?```', '', response)
    response = re.sub(r'`[^`]*`', '', response)
    
    # Remove XML/HTML-like tags
    response = re.sub(r'<[^>]*>', '', response)
    
    # Remove special UI formatting that might have slipped through
    response = re.sub(r'TIME_DISPLAY_SHOW:.*', '', response)
    response = re.sub(r'WEATHER_DISPLAY_SHOW:.*', '', response)
    response = re.sub(r'SEARCH_RESULT:.*', '', response)
    response = re.sub(r'NEWS_RESULT:.*', '', response)
    response = re.sub(r'WEATHER_DATA:.*', '', response)
    response = re.sub(r'WIDGET_TIME:.*', '', response)
    
    # Remove JSON data blocks
    response = re.sub(r'\{[^}]*"temperature"[^}]*\}', '', response)
    response = re.sub(r'\{[^}]*"humidity"[^}]*\}', '', response)
    
    # Remove URLs and links
    response = re.sub(r'https?://\S+', '', response)
    
    # Remove source citations
    response = re.sub(r'📍\s*\*\*Sources:\*\*.*', '', response)
    response = re.sub(r'_Sources:.*_', '', response)
    
    # Remove excessive formatting symbols
    response = re.sub(r'[*_#]{2,}', '', response)
    response = response.replace('**', '')
    response = response.replace('__', '')
    
    # Clean up whitespace
    response = re.sub(r'\s+', ' ', response)
    response = response.strip()
    
    return response

def test_filtering():
    print("🧪 Testing AI Voice Response Filtering")
    print("=" * 50)
    
    # Test responses from the actual file
    try:
        with open("astra_ai/ui/ai_responses.json", 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        print(f"📄 Found {len(data)} responses in ai_responses.json")
        print("\n🔍 Testing last 10 responses:")
        
        speakable_count = 0
        skipped_count = 0
        
        for i, entry in enumerate(data[-10:], 1):
            response = entry.get("conversation_data", {}).get("ai_response", "")
            message_id = entry.get("conversation_data", {}).get("message_id", "unknown")
            
            should_skip = should_skip_response(response)
            
            if should_skip:
                status = "🚫 SKIP"
                skipped_count += 1
            else:
                status = "🔊 SPEAK"
                speakable_count += 1
                cleaned = clean_response_for_speech(response)
                print(f"  {i:2d}. {status} - {cleaned[:80]}...")
            
            if should_skip:
                print(f"  {i:2d}. {status} - {response[:80]}...")
        
        print(f"\n📊 Summary:")
        print(f"  🔊 Speakable responses: {speakable_count}")
        print(f"  🚫 Skipped responses: {skipped_count}")
        
        if speakable_count > 0:
            print(f"\n✅ AI Voice system should work - found {speakable_count} speakable responses")
        else:
            print(f"\n⚠️ No speakable responses found in recent messages")
            
    except FileNotFoundError:
        print("❌ ai_responses.json file not found")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_filtering()
