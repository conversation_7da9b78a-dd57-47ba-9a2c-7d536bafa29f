#!/usr/bin/env python3
"""
Terminal-based Screen Analyzer using Google Gemini 1.5 Vision
Responds to "What do you see on my screen?" and analyzes images
"""

import os
import sys
import base64
import time
from pathlib import Path
from io import BytesIO
import mss
from PIL import Image
from dotenv import load_dotenv

# Load environment variables
load_dotenv('astra_ai/.env')

# Also try to set the API key directly if .env loading fails
if not os.getenv('GOOGLE_AI_API_KEY') and not os.getenv('GEMINI_API_KEY'):
    # Try to read from .env file directly
    env_file = Path('astra_ai/.env')
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                if line.startswith('GOOGLE_AI_API_KEY='):
                    api_key = line.split('=', 1)[1].strip()
                    os.environ['GOOGLE_AI_API_KEY'] = api_key
                    break

# Add astra_ai to path for imports
sys.path.insert(0, str(Path("astra_ai").absolute()))

try:
    from services.gemini_vision import GeminiVisionAnalyzer
    GEMINI_AVAILABLE = True
except ImportError as e:
    print(f"❌ Error importing Gemini service: {e}")
    print("💡 Make sure google-generativeai is installed: pip install google-generativeai")
    GEMINI_AVAILABLE = False

class TerminalScreenAnalyzer:
    """Terminal-based screen analyzer with Gemini Vision AI"""
    
    def __init__(self):
        """Initialize the terminal screen analyzer"""
        self.analyzer = None
        self.setup_analyzer()
        
    def setup_analyzer(self):
        """Set up the Gemini Vision analyzer"""
        if not GEMINI_AVAILABLE:
            print("❌ Gemini Vision not available")
            return False
            
        try:
            self.analyzer = GeminiVisionAnalyzer()
            print("✅ Gemini Vision AI initialized successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to initialize Gemini Vision: {e}")
            print("💡 Check your API key in astra_ai/.env file")
            return False
    
    def capture_screen(self, monitor_number=1):
        """Capture the screen using mss"""
        try:
            with mss.mss() as sct:
                # Get monitor info
                monitors = sct.monitors
                if monitor_number >= len(monitors):
                    monitor_number = 1
                
                monitor = monitors[monitor_number]
                print(f"📸 Capturing screen {monitor_number}: {monitor['width']}x{monitor['height']}")
                
                # Capture screenshot
                screenshot = sct.grab(monitor)
                
                # Convert to PIL Image
                img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
                
                # Convert to base64
                buffer = BytesIO()
                img.save(buffer, format='PNG')
                img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
                
                print(f"✅ Screen captured successfully: {img.size[0]}x{img.size[1]} pixels")
                return img_base64, img.size
                
        except Exception as e:
            print(f"❌ Screen capture failed: {e}")
            print("💡 Make sure mss is installed: pip install mss")
            return None, None
    
    def load_image_file(self, file_path):
        """Load and encode an image file"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                print(f"❌ Image file not found: {file_path}")
                return None, None
            
            # Open and convert image
            with Image.open(file_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Convert to base64
                buffer = BytesIO()
                img.save(buffer, format='PNG')
                img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
                
                print(f"✅ Image loaded: {img.size[0]}x{img.size[1]} pixels from {file_path.name}")
                return img_base64, img.size
                
        except Exception as e:
            print(f"❌ Failed to load image: {e}")
            return None, None
    
    def analyze_image(self, image_data, question=None, analysis_type="detailed"):
        """Analyze image with Gemini Vision"""
        if not self.analyzer:
            print("❌ Gemini Vision analyzer not available")
            return None
        
        try:
            print("🧠 Analyzing with Gemini Vision AI...")
            
            if analysis_type == "quick":
                result = self.analyzer.get_quick_summary(image_data)
            elif question:
                result = self.analyzer.analyze_with_custom_question(image_data, question)
            else:
                result = self.analyzer.analyze_screen_image(image_data)
            
            return result
            
        except Exception as e:
            print(f"❌ Analysis failed: {e}")
            return None
    
    def print_analysis_result(self, result):
        """Print the analysis result in a formatted way"""
        if not result:
            return

        print("\n" + "="*70)
        if result.get('success'):
            print("🤖 AI ANALYSIS RESULTS")
            print("="*70)

            # Print metadata
            meta_info = []
            if result.get('analysis_type'):
                meta_info.append(f"Type: {result['analysis_type']}")
            if result.get('image_size'):
                meta_info.append(f"Size: {result['image_size']}")
            if result.get('model'):
                meta_info.append(f"Model: {result['model']}")

            if meta_info:
                print(f"📊 {' | '.join(meta_info)}")
                print("-"*70)

            # Print analysis
            print(result['analysis'])

        else:
            print("❌ ANALYSIS FAILED")
            print("="*70)
            error_msg = result.get('error', 'Unknown error')
            print(f"Error: {error_msg}")

            # Special handling for quota errors
            if result.get('quota_exceeded'):
                print("\n💡 QUOTA SOLUTIONS:")
                print("   1. Wait 5-10 minutes and try again")
                print("   2. Use a different API key")
                print("   3. Upgrade to paid plan at: https://aistudio.google.com/")
                print("   4. Try using 'gemini-1.5-flash' model (lower quota usage)")

        print("="*70)
    
    def interactive_mode(self):
        """Run interactive terminal mode"""
        print("\n" + "="*70)
        print("🚀 GINI 1.5 - TERMINAL SCREEN ANALYZER")
        print("="*70)
        print("🧠 Powered by Google Gemini 1.5 Pro Vision")
        print("📸 Ask 'What do you see on my screen?' or provide image paths")
        print("="*70)
        print()
        print("💡 Commands:")
        print("  - 'what do you see on my screen?' - Capture and analyze screen")
        print("  - 'analyze [image_path]' - Analyze specific image file")
        print("  - 'quick [image_path]' - Quick summary of image")
        print("  - 'help' - Show this help")
        print("  - 'quit' or 'exit' - Exit the program")
        print()
        
        while True:
            try:
                user_input = input("🎤 You: ").strip().lower()
                
                if not user_input:
                    continue
                
                if user_input in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                elif user_input == 'help':
                    self.show_help()
                
                elif 'what do you see on my screen' in user_input or 'whats on my screen' in user_input:
                    self.handle_screen_analysis(user_input)
                
                elif user_input.startswith('analyze '):
                    image_path = user_input[8:].strip()
                    self.handle_image_analysis(image_path)
                
                elif user_input.startswith('quick '):
                    image_path = user_input[6:].strip()
                    self.handle_image_analysis(image_path, analysis_type="quick")
                
                elif user_input.startswith('question '):
                    # Format: "question [image_path] [question]"
                    parts = user_input[9:].strip().split(' ', 1)
                    if len(parts) >= 2:
                        image_path, question = parts
                        self.handle_image_analysis(image_path, question=question)
                    else:
                        print("💡 Usage: question [image_path] [your question]")
                
                else:
                    # Try to interpret as a question about the screen
                    if '?' in user_input:
                        print("🤔 I'll capture your screen and answer that question...")
                        self.handle_screen_analysis(user_input)
                    else:
                        print("❓ I didn't understand that. Type 'help' for commands.")
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def handle_screen_analysis(self, user_input=None):
        """Handle screen capture and analysis"""
        # Capture screen
        image_data, size = self.capture_screen()
        if not image_data:
            return
        
        # Determine if user asked a specific question
        question = None
        if user_input and '?' in user_input:
            # Extract question from user input
            question_part = user_input.split('?')[0] + '?'
            if 'what do you see' not in question_part.lower():
                question = question_part
        
        # Analyze
        result = self.analyze_image(image_data, question=question)
        self.print_analysis_result(result)
    
    def handle_image_analysis(self, image_path, question=None, analysis_type="detailed"):
        """Handle image file analysis"""
        # Load image
        image_data, size = self.load_image_file(image_path)
        if not image_data:
            return
        
        # Analyze
        result = self.analyze_image(image_data, question=question, analysis_type=analysis_type)
        self.print_analysis_result(result)
    
    def show_help(self):
        """Show help information"""
        print("\n" + "="*70)
        print("📖 HELP - GINI 1.5 TERMINAL COMMANDS")
        print("="*70)
        print()
        print("🖥️  SCREEN ANALYSIS:")
        print("   'what do you see on my screen?'")
        print("   'whats on my screen?'")
        print("   'describe my screen'")
        print()
        print("📁 IMAGE FILE ANALYSIS:")
        print("   'analyze path/to/image.jpg'")
        print("   'quick path/to/image.png'")
        print("   'question path/to/image.jpg What programming language is this?'")
        print()
        print("❓ CUSTOM QUESTIONS:")
        print("   'What errors do you see on my screen?'")
        print("   'Summarize the document on my screen'")
        print("   'What programming language is being used?'")
        print()
        print("🎮 CONTROLS:")
        print("   'help' - Show this help")
        print("   'quit' or 'exit' - Exit program")
        print("="*70)

def main():
    """Main function"""
    # Check if mss is available for screen capture
    try:
        import mss
    except ImportError:
        print("❌ mss package required for screen capture")
        print("💡 Install with: pip install mss")
        return
    
    # Create and run analyzer
    analyzer = TerminalScreenAnalyzer()
    
    if len(sys.argv) > 1:
        # Command line mode
        command = ' '.join(sys.argv[1:]).lower()
        
        if 'screen' in command:
            analyzer.handle_screen_analysis(command)
        elif command.endswith(('.jpg', '.jpeg', '.png', '.bmp', '.gif')):
            analyzer.handle_image_analysis(command)
        else:
            print(f"❓ Unknown command: {command}")
            print("💡 Try: python terminal_screen_analyzer.py 'what do you see on my screen?'")
    else:
        # Interactive mode
        analyzer.interactive_mode()

if __name__ == "__main__":
    main()
