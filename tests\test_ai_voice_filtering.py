#!/usr/bin/env python3
"""
Test script to verify AI voice filtering is working correctly
"""

import sys
import os
import json

# Add the speech directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'astra_ai', 'speech'))

try:
    from cartesia import Cartesia
    CARTESIA_AVAILABLE = True
except ImportError:
    CARTESIA_AVAILABLE = False
    print("⚠️ Cartesia not available - testing filtering only")

def test_response_filtering():
    """Test the response filtering logic"""
    print("🧪 Testing AI Voice Response Filtering")
    print("=" * 50)
    
    # Import the TTS class
    try:
        sys.path.append('astra_ai/speech')
        import importlib.util
        spec = importlib.util.spec_from_file_location("ai_voice", "astra_ai/speech/Ai vioce.py")
        ai_voice = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(ai_voice)
        TextToSpeech = ai_voice.TextToSpeech
        CartesiaConfig = ai_voice.CartesiaConfig
    except Exception as e:
        print(f"❌ Could not import AI voice modules: {e}")
        return
    
    # Create a dummy config
    config = CartesiaConfig(api_key="dummy")
    tts = TextToSpeech(config)
    
    # Test responses that should be SKIPPED
    skip_responses = [
        "TIME_DISPLAY_SHOW: Italy|The current time in Rome is 23:58. WIDGET_TIME:23:58:06",
        "WEATHER_DISPLAY_SHOW: como|WEATHER_DATA: {\"temperature\": 27, \"feelsLike\": 28}",
        "SEARCH_RESULT: What's next for AI in 2025 | MIT Technology",
        "NEWS_RESULT: 📰 **Current News: Sports** (June 21, 2025)",
        "I apologize, but I encountered an error generating a response. Could you please try again?",
        "Search error: 429 Client Error: Too Many Requests",
        '{"temperature": 25, "feelsLike": 26, "condition": "Overcast Clouds"}',
        "",
        "   ",
        "Hi"  # Too short
    ]
    
    # Test responses that should be SPOKEN
    speak_responses = [
        "You're welcome! What's next?",
        "It's currently 14:43 in Rome, Italy.",
        "The current time in Rome is 00:01:05.",
        "Glad to hear that! By the way, it's currently 3:47 PM in Italy.",
        "Hello! How can I help you today?",
        "I can help you with weather, time, news, and search queries.",
        "That's an interesting question about artificial intelligence."
    ]
    
    print("🚫 Testing responses that should be SKIPPED:")
    for i, response in enumerate(skip_responses, 1):
        should_skip = tts.should_skip_response(response)
        status = "✅ SKIPPED" if should_skip else "❌ NOT SKIPPED"
        print(f"  {i:2d}. {status} - {response[:60]}...")
    
    print("\n🔊 Testing responses that should be SPOKEN:")
    for i, response in enumerate(speak_responses, 1):
        should_skip = tts.should_skip_response(response)
        cleaned = tts.clean_response_for_speech(response)
        status = "❌ SKIPPED" if should_skip else "✅ WILL SPEAK"
        print(f"  {i:2d}. {status} - {cleaned[:60]}...")
    
    print("\n🧹 Testing response cleaning:")
    dirty_responses = [
        "📰 **Current News: Technology** (June 21, 2025)\n\n(CNN) The explosion of a SpaceX...",
        "SEARCH_RESULT: What's next for AI in 2025 | MIT Technology. Other AI competitors...",
        "Here's the weather: WEATHER_DATA: {\"temp\": 25} It's nice outside!",
        "Check this link: https://example.com for more info.",
        "**Bold text** and _italic text_ with `code` blocks.",
        "📍 **Sources:** CNN, Bloomberg"
    ]
    
    for i, response in enumerate(dirty_responses, 1):
        cleaned = tts.clean_response_for_speech(response)
        print(f"  {i:2d}. Original: {response[:50]}...")
        print(f"      Cleaned:  {cleaned[:50]}...")
        print()

def test_json_reading():
    """Test reading from the actual ai_responses.json file"""
    print("\n📄 Testing JSON file reading:")
    print("=" * 30)
    
    try:
        sys.path.append('astra_ai/speech')
        import importlib.util
        spec = importlib.util.spec_from_file_location("ai_voice", "astra_ai/speech/Ai vioce.py")
        ai_voice = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(ai_voice)
        TextToSpeech = ai_voice.TextToSpeech
        CartesiaConfig = ai_voice.CartesiaConfig

        config = CartesiaConfig(api_key="dummy")
        tts = TextToSpeech(config)
        
        # Try to read the actual file
        ai_response, message_id = tts.read_output_json()
        
        if ai_response and message_id:
            print(f"✅ Successfully read latest response")
            print(f"📝 Message ID: {message_id}")
            print(f"🔊 Will speak: {ai_response[:100]}...")
        else:
            print("🚫 Latest response was filtered out or file is empty")
            
        # Show last few entries from the unified_messages.json file
        try:
            with open("astra_ai/ui/unified_messages.json", 'r', encoding='utf-8') as f:
                data = json.load(f)
                if isinstance(data, list) and len(data) > 0:
                    print(f"\n📊 File contains {len(data)} total messages")
                    print("🔍 Last 3 messages:")
                    for i, entry in enumerate(data[-3:], 1):
                        response = entry.get("ai_response", "")
                        message_type = entry.get("message_type", "unknown")
                        should_skip = tts.should_skip_response(response)
                        status = "🚫 SKIP" if should_skip else "🔊 SPEAK"
                        print(f"  {i}. [{message_type}] {status} - {response[:60]}...")
        except Exception as e:
            print(f"❌ Error reading unified_messages.json: {e}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_response_filtering()
    test_json_reading()
    
    print("\n" + "=" * 50)
    print("🎯 Test completed!")
    
    if CARTESIA_AVAILABLE:
        print("✅ Cartesia is available - voice synthesis should work")
    else:
        print("⚠️ Cartesia not available - install with: pip install cartesia")
