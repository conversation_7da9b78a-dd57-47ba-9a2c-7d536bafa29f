#!/usr/bin/env python3
"""
Test script to verify audio data decoding from Cartesia API
"""

import base64

def test_audio_decoding():
    print("🧪 Testing Audio Data Decoding")
    print("=" * 40)
    
    try:
        from cartesia import Cartesia
        print("✅ Cartesia SDK imported successfully")
    except ImportError:
        print("❌ Cartesia SDK not installed")
        return
    
    # Initialize client
    api_key = "sk_car_5PdMH1dJjrc5jJhGTcec1i"
    voice_id = "f114a467-c40a-4db8-964d-aaba89cd08fa"
    
    try:
        client = Cartesia(api_key=api_key)
        print("✅ Cartesia client initialized")
    except Exception as e:
        print(f"❌ Failed to initialize client: {e}")
        return
    
    # Test TTS generation with proper decoding
    try:
        print("🔊 Testing TTS with audio decoding...")
        
        output_format = {
            "container": "raw",
            "encoding": "pcm_f32le",
            "sample_rate": 48000,
        }
        
        test_text = "Testing audio decoding."
        
        response = client.tts.sse(
            model_id="sonic-english",
            transcript=test_text,
            voice={"mode": "id", "id": voice_id},
            output_format=output_format
        )
        
        # Get first chunk and test decoding
        first_chunk = next(response)
        print(f"📝 Response chunk type: {type(first_chunk)}")
        
        # Extract raw data
        if hasattr(first_chunk, 'data'):
            raw_data = first_chunk.data
        elif "data" in first_chunk:
            raw_data = first_chunk["data"]
        else:
            print("❌ No data field found")
            return
            
        print(f"📝 Raw data type: {type(raw_data)}")
        print(f"📝 Raw data length: {len(raw_data) if hasattr(raw_data, '__len__') else 'Unknown'}")
        
        # Test decoding
        if isinstance(raw_data, str):
            print("🔄 Raw data is string - attempting base64 decode...")
            try:
                decoded_bytes = base64.b64decode(raw_data)
                print(f"✅ Successfully decoded base64 data")
                print(f"📝 Decoded bytes length: {len(decoded_bytes)}")
                print(f"📝 First 20 bytes: {decoded_bytes[:20]}")
                
                # Verify it's audio data (should be float32 PCM)
                import struct
                if len(decoded_bytes) >= 4:
                    first_float = struct.unpack('<f', decoded_bytes[:4])[0]
                    print(f"📝 First audio sample: {first_float}")
                    if -1.0 <= first_float <= 1.0:
                        print("✅ Audio data looks valid (float32 PCM)")
                    else:
                        print("⚠️ Audio data might be in different format")
                        
            except Exception as decode_error:
                print(f"❌ Base64 decode failed: {decode_error}")
        else:
            print(f"📝 Raw data is already bytes: {type(raw_data)}")
            print(f"📝 Bytes length: {len(raw_data)}")
            
    except Exception as e:
        print(f"❌ Error testing TTS: {e}")
    
    print("\n" + "=" * 40)
    print("🎯 Audio decoding test completed!")

if __name__ == "__main__":
    test_audio_decoding()
