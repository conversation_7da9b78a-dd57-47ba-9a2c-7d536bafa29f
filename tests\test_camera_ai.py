#!/usr/bin/env python3
"""
Test Camera AI Vision Analysis
Test script to verify camera AI functionality and troubleshoot quota issues
"""

import sys
import os
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from services.gemini_vision import GeminiVisionAnalyzer, analyze_camera_capture
from services.gini_camera_bridge import GiniCameraBridge
import base64
from PIL import Image
import io

def create_test_image():
    """Create a simple test image for analysis"""
    # Create a simple test image with text
    img = Image.new('RGB', (400, 300), color='white')
    
    # Convert to base64
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    img_data = base64.b64encode(buffer.getvalue()).decode()
    
    return f"data:image/png;base64,{img_data}"

def test_gemini_vision():
    """Test Gemini Vision API directly"""
    print("🧪 Testing Gemini Vision API...")
    
    try:
        # Initialize analyzer
        analyzer = GeminiVisionAnalyzer()
        print(f"✅ Analyzer initialized with model: {analyzer.model_name}")
        
        # Test connection
        connection_ok = analyzer.test_connection()
        print(f"🔗 Connection test: {'✅ PASSED' if connection_ok else '❌ FAILED'}")
        
        if not connection_ok:
            print("❌ Connection failed - checking quota status...")
            return False
        
        # Test with simple image
        test_image = create_test_image()
        result = analyzer.analyze_camera_image(test_image.split(',')[1], "What do you see in this test image?")
        
        if result['success']:
            print(f"✅ Analysis successful: {result['analysis'][:100]}...")
            return True
        else:
            print(f"❌ Analysis failed: {result.get('error', 'Unknown error')}")
            if result.get('quota_exceeded'):
                print("⚠️ Quota exceeded detected!")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False

def test_camera_bridge():
    """Test GINI Camera Bridge"""
    print("\n🌉 Testing GINI Camera Bridge...")

    try:
        bridge = GiniCameraBridge()

        print("✅ Bridge initialized")

        # Test vision request without image
        result = bridge.process_vision_request("what do you see?")
        print(f"📝 Vision request result: {result}")

        return True

    except Exception as e:
        print(f"❌ Bridge test failed: {e}")
        return False

def test_camera_analysis():
    """Test camera analysis function"""
    print("\n📸 Testing camera analysis function...")
    
    try:
        test_image = create_test_image()
        result = analyze_camera_capture(test_image.split(',')[1], "scene")
        
        if result['success']:
            print(f"✅ Camera analysis successful: {result['analysis'][:100]}...")
            return True
        else:
            print(f"❌ Camera analysis failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Camera analysis test failed: {e}")
        return False

def diagnose_quota_issues():
    """Diagnose and provide solutions for quota issues"""
    print("\n🔍 Diagnosing quota issues...")
    
    try:
        import google.generativeai as genai
        
        # Configure with API key
        genai.configure(api_key="AIzaSyBe7dZj5mN_40Yo9t2xIeNgEcKdJWSe42w")
        
        # Try different models to check quota status
        models_to_test = ['gemini-1.5-flash', 'gemini-pro-vision', 'gemini-1.5-pro']
        
        for model_name in models_to_test:
            try:
                print(f"🧪 Testing {model_name}...")
                model = genai.GenerativeModel(model_name)
                
                # Create minimal test
                test_image = Image.new('RGB', (1, 1), color='white')
                response = model.generate_content(
                    ["Test", test_image],
                    generation_config=genai.types.GenerationConfig(max_output_tokens=1)
                )
                
                print(f"✅ {model_name}: Available")
                return model_name
                
            except Exception as e:
                error_str = str(e)
                if "429" in error_str or "quota" in error_str.lower():
                    print(f"⚠️ {model_name}: Quota exceeded")
                elif "403" in error_str:
                    print(f"❌ {model_name}: Access denied")
                else:
                    print(f"❌ {model_name}: {error_str[:50]}...")
        
        print("\n💡 Quota Solutions:")
        print("1. Wait for quota reset (usually 24 hours)")
        print("2. Use GINI Bridge for offline analysis")
        print("3. Upgrade API plan at Google AI Studio")
        print("4. Try again later with reduced frequency")
        
        return None
        
    except Exception as e:
        print(f"❌ Quota diagnosis failed: {e}")
        return None

def main():
    """Main test function"""
    print("🚀 Camera AI Vision Analysis Test")
    print("=" * 50)
    
    # Test individual components
    gemini_ok = test_gemini_vision()
    bridge_ok = test_camera_bridge()
    analysis_ok = test_camera_analysis()
    
    print("\n📊 Test Results Summary:")
    print(f"Gemini Vision API: {'✅ PASS' if gemini_ok else '❌ FAIL'}")
    print(f"GINI Camera Bridge: {'✅ PASS' if bridge_ok else '❌ FAIL'}")
    print(f"Camera Analysis: {'✅ PASS' if analysis_ok else '❌ FAIL'}")
    
    if not any([gemini_ok, bridge_ok, analysis_ok]):
        print("\n🔍 Running quota diagnosis...")
        available_model = diagnose_quota_issues()
        
        if available_model:
            print(f"\n✅ Found working model: {available_model}")
            print("💡 Camera AI should work with this model")
        else:
            print("\n❌ All models appear to have quota issues")
            print("💡 Try the GINI Bridge for offline analysis")
    
    print("\n🎯 Recommendations:")
    if gemini_ok:
        print("✅ Camera AI should work properly")
        print("💡 Try asking 'what do you see?' in the camera widget")
    else:
        print("⚠️ Camera AI may have issues")
        print("💡 Check quota status and try GINI Bridge")
        print("💡 Run: python astra_ai/scripts/start_gini_bridge.py")

if __name__ == "__main__":
    main()
