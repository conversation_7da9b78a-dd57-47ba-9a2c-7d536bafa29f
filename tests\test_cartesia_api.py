#!/usr/bin/env python3
"""
Test script to verify Cartesia API is working correctly
"""

def test_cartesia_api():
    print("🧪 Testing Cartesia API")
    print("=" * 30)
    
    try:
        from cartesia import Cartesia
        print("✅ Cartesia SDK imported successfully")
    except ImportError:
        print("❌ Cartesia SDK not installed. Install with: pip install cartesia")
        return
    
    # Initialize client
    api_key = "sk_car_quA8Xego3FiXMwXtDuyLcR"
    voice_id = "f114a467-c40a-4db8-964d-aaba89cd08fa"
    
    try:
        client = Cartesia(api_key=api_key)
        print("✅ Cartesia client initialized")
    except Exception as e:
        print(f"❌ Failed to initialize client: {e}")
        return
    
    # Test voice retrieval
    try:
        print(f"🔍 Testing voice retrieval for ID: {voice_id}")
        voice = client.voices.get(id=voice_id)
        print(f"✅ Voice retrieved successfully")
        print(f"📝 Voice type: {type(voice)}")
        
        # Check different ways to access the voice data
        if hasattr(voice, 'embedding'):
            print("✅ Voice has 'embedding' attribute")
            embedding = voice.embedding
            print(f"📝 Embedding type: {type(embedding)}")
            print(f"📝 Embedding length: {len(embedding) if hasattr(embedding, '__len__') else 'Unknown'}")
        elif isinstance(voice, dict) and "embedding" in voice:
            print("✅ Voice is dict with 'embedding' key")
            embedding = voice["embedding"]
            print(f"📝 Embedding type: {type(embedding)}")
        else:
            print("❌ Cannot find embedding in voice response")
            print(f"📝 Available attributes: {dir(voice)}")
            if hasattr(voice, '__dict__'):
                print(f"📝 Voice dict keys: {list(voice.__dict__.keys())}")
            
    except Exception as e:
        print(f"❌ Error retrieving voice: {e}")
        return
    
    # Test TTS generation (simple test)
    try:
        print("🔊 Testing TTS generation...")
        
        # Try with embedding if available
        if 'embedding' in locals():
            print("🔄 Testing with voice embedding...")
            try:
                output_format = {
                    "container": "raw",
                    "encoding": "pcm_f32le",
                    "sample_rate": 48000,
                }
                
                # Test a very short text
                test_text = "Hello, this is a test."
                
                # Try different API formats
                try:
                    # Format 1: New API with voice dict
                    response = client.tts.sse(
                        model_id="sonic-english",
                        transcript=test_text,
                        voice={"mode": "embedding", "embedding": embedding},
                        output_format=output_format
                    )
                except Exception as e1:
                    print(f"Format 1 failed: {e1}")
                    try:
                        # Format 2: Try without output_format
                        response = client.tts.sse(
                            model_id="sonic-english",
                            transcript=test_text,
                            voice={"mode": "embedding", "embedding": embedding}
                        )
                    except Exception as e2:
                        print(f"Format 2 failed: {e2}")
                        try:
                            # Format 3: Try with voice_embedding parameter
                            response = client.tts.sse(
                                model_id="sonic-english",
                                transcript=test_text,
                                voice_embedding=embedding
                            )
                        except Exception as e3:
                            print(f"Format 3 failed: {e3}")
                            raise e3
                
                # Check what's in the response
                first_chunk = next(response)
                print(f"📝 Response chunk type: {type(first_chunk)}")
                print(f"📝 Response chunk keys: {list(first_chunk.keys()) if isinstance(first_chunk, dict) else 'Not a dict'}")

                if "audio" in first_chunk:
                    print("✅ TTS generation with embedding successful")
                    print(f"📝 Audio chunk size: {len(first_chunk['audio'])} bytes")
                elif hasattr(first_chunk, 'audio'):
                    print("✅ TTS generation with embedding successful (object format)")
                    print(f"📝 Audio chunk size: {len(first_chunk.audio)} bytes")
                else:
                    print("❌ No audio data in response")
                    print(f"📝 Available data: {first_chunk}")
                    
            except Exception as embedding_error:
                print(f"❌ TTS with embedding failed: {embedding_error}")
                
                # Try with voice_id directly
                print("🔄 Testing with voice_id...")
                try:
                    response = client.tts.sse(
                        model_id="sonic-english",
                        transcript=test_text,
                        voice={"mode": "id", "id": voice_id},
                        stream=True,
                        output_format=output_format
                    )
                    
                    first_chunk = next(response)
                    if "audio" in first_chunk:
                        print("✅ TTS generation with voice_id successful")
                        print(f"📝 Audio chunk size: {len(first_chunk['audio'])} bytes")
                    else:
                        print("❌ No audio data in response")
                        
                except Exception as voice_id_error:
                    print(f"❌ TTS with voice_id failed: {voice_id_error}")
        
    except Exception as e:
        print(f"❌ Error testing TTS: {e}")
    
    print("\n" + "=" * 30)
    print("🎯 Cartesia API test completed!")

if __name__ == "__main__":
    test_cartesia_api()
