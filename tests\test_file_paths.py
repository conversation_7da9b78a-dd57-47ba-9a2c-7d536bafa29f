#!/usr/bin/env python3
"""
Test script to verify file paths for Nova AI and AI Voice system
"""

import os
import json
from pathlib import Path

def test_file_paths():
    print("🧪 Testing File Paths for Nova AI ↔ AI Voice Integration")
    print("=" * 60)
    
    # Check where Nova AI saves responses
    nova_ai_file = "ai_responses.json"
    voice_system_file = "ai_responses.json"  # Updated to match
    
    print(f"📁 Nova AI saves to: {os.path.abspath(nova_ai_file)}")
    print(f"🎤 AI Voice reads from: {os.path.abspath(voice_system_file)}")
    
    if os.path.abspath(nova_ai_file) == os.path.abspath(voice_system_file):
        print("✅ File paths MATCH - AI Voice will read Nova AI responses!")
    else:
        print("❌ File paths DON'T MATCH - AI Voice won't see Nova AI responses!")
        return
    
    # Check if the file exists and has content
    if os.path.exists(nova_ai_file):
        print(f"✅ File exists: {nova_ai_file}")
        
        try:
            with open(nova_ai_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            if isinstance(data, list):
                print(f"📊 File contains {len(data)} responses")
                
                if len(data) > 0:
                    latest = data[-1]
                    ai_response = latest.get("conversation_data", {}).get("ai_response", "")
                    print(f"📝 Latest Nova AI response: {ai_response[:100]}...")
                    
                    # Test the filtering logic
                    from astra_ai.speech.Ai_vioce import TextToSpeech, CartesiaConfig
                    config = CartesiaConfig(api_key="dummy")
                    tts = TextToSpeech(config)
                    
                    should_skip = tts.should_skip_response(ai_response)
                    if should_skip:
                        print("🚫 This response would be SKIPPED (UI command)")
                    else:
                        print("🔊 This response would be SPOKEN")
                        cleaned = tts.clean_response_for_speech(ai_response)
                        print(f"🎤 Cleaned for speech: {cleaned[:100]}...")
                else:
                    print("📝 File is empty - no responses yet")
            else:
                print("❌ File format is incorrect")
                
        except Exception as e:
            print(f"❌ Error reading file: {e}")
    else:
        print(f"⚠️ File doesn't exist yet: {nova_ai_file}")
        print("💡 Run Nova AI first to create the file")
    
    # Test file monitoring path
    watch_path = os.path.dirname(os.path.abspath(nova_ai_file))
    print(f"\n📁 AI Voice will monitor directory: {watch_path}")
    
    if os.path.exists(watch_path):
        print("✅ Monitor directory exists")
    else:
        print("❌ Monitor directory doesn't exist")
    
    print("\n" + "=" * 60)
    print("🎯 File path test completed!")
    
    # Instructions
    print("\n📋 To test the integration:")
    print("1. Start AI Voice system: python 'astra_ai/speech/Ai vioce.py'")
    print("2. Start Nova AI in another terminal")
    print("3. Ask Nova AI a question")
    print("4. AI Voice should speak Nova AI's response INSTANTLY!")

if __name__ == "__main__":
    test_file_paths()
