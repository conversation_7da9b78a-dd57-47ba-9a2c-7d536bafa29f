#!/usr/bin/env python3
"""
Test script to verify backend API connection
"""

import requests
import json

def test_backend_api():
    """Test the backend camera API endpoint"""

    try:
        print(f"🧠 Testing backend camera API...")

        # Test the camera test endpoint
        test_url = 'http://127.0.0.1:59459/api/widget/camera/test'
        print(f"🌐 Testing URL: {test_url}")

        response = requests.get(test_url)

        print(f"📡 Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("✅ Backend API connected successfully")
            print(f"📝 Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"❌ Connection failed: {response.status_code}")
            print(f"❌ Error response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False

def test_gemini_connection():
    """Test direct Gemini API connection"""
    api_key = 'AIzaSyBe7dZj5mN_40Yo9t2xIeNgEcKdJWSe42w'
    model = 'gemini-1.5-flash'

    url = f'https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent?key={api_key}'

    payload = {
        "contents": [{
            "parts": [{
                "text": "Hello, this is a connection test."
            }]
        }],
        "generationConfig": {
            "maxOutputTokens": 10,
            "temperature": 0.1
        }
    }

    try:
        print(f"🧠 Testing direct Gemini AI connection...")
        print(f"🔑 API Key (first 10 chars): {api_key[:10]}...")
        print(f"🤖 Model: {model}")
        print(f"🌐 URL: {url.replace(api_key, 'API_KEY_HIDDEN')}")

        response = requests.post(url, json=payload, headers={'Content-Type': 'application/json'})

        print(f"📡 Response status: {response.status_code}")
        print(f"📡 Response headers: {dict(response.headers)}")

        if response.status_code == 200:
            data = response.json()
            print("✅ Direct Gemini AI connected successfully")
            print(f"📝 Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"❌ Connection failed: {response.status_code}")
            print(f"❌ Error response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("Testing Backend API")
    print("=" * 50)
    backend_success = test_backend_api()

    print("\n" + "=" * 50)
    print("Testing Direct Gemini API")
    print("=" * 50)
    gemini_success = test_gemini_connection()

    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    print(f"Backend API: {'✅ Working' if backend_success else '❌ Failed'}")
    print(f"Direct Gemini API: {'✅ Working' if gemini_success else '❌ Failed'}")
