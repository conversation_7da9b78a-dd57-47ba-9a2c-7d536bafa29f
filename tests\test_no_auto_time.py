#!/usr/bin/env python3
"""
Test No Automatic Time Announcements
Tests that the AI doesn't automatically add time information to responses
"""

import requests
import json
import time

def test_no_auto_time():
    """Test that AI doesn't automatically add time information"""
    print("🕐 TESTING NO AUTOMATIC TIME ANNOUNCEMENTS")
    print("=" * 60)
    
    # Test messages that should NOT trigger time responses
    test_messages = [
        "what uppppppppppp",
        "hi",
        "hello",
        "how are you",
        "good",
        "what's new",
        "tell me a joke",
        "what can you do",
        "help me",
        "thanks"
    ]
    
    print("Testing messages that should NOT include time information:")
    print("=" * 60)
    
    results = []
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n{i}. Testing: '{message}'")
        
        try:
            response = requests.post(
                'http://127.0.0.1:54016/api/chat',
                json={
                    'message': message,
                    'session_id': f'test_no_time_{i}'
                },
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data['response']
                
                # Check for time-related phrases
                time_phrases = [
                    'by the way',
                    'current time',
                    'currently',
                    'it\'s',
                    'time in',
                    'PM',
                    'AM',
                    ':',  # Time format like 14:43
                    'italy'
                ]
                
                has_time_info = any(phrase.lower() in ai_response.lower() for phrase in time_phrases)
                
                if has_time_info:
                    print(f"   ❌ FAILED: Response contains time information")
                    print(f"   📝 Response: {ai_response}")
                    results.append({"message": message, "passed": False, "response": ai_response})
                else:
                    print(f"   ✅ PASSED: No time information")
                    print(f"   📝 Response: {ai_response}")
                    results.append({"message": message, "passed": True, "response": ai_response})
                    
            else:
                print(f"   ❌ Server error: {response.status_code}")
                results.append({"message": message, "passed": False, "response": f"Server error: {response.status_code}"})
                
        except requests.exceptions.ConnectionError:
            print("   ❌ Could not connect to server")
            results.append({"message": message, "passed": False, "response": "Connection error"})
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results.append({"message": message, "passed": False, "response": f"Error: {e}"})
        
        # Small delay between requests
        time.sleep(1)
    
    return results

def test_explicit_time_requests():
    """Test that AI DOES respond to explicit time requests"""
    print("\n\n🕐 TESTING EXPLICIT TIME REQUESTS")
    print("=" * 60)
    
    # Test messages that SHOULD trigger time responses
    time_messages = [
        "what time is it",
        "show me the time",
        "current time",
        "what's the time",
        "time please"
    ]
    
    print("Testing messages that SHOULD include time information:")
    print("=" * 60)
    
    results = []
    
    for i, message in enumerate(time_messages, 1):
        print(f"\n{i}. Testing: '{message}'")
        
        try:
            response = requests.post(
                'http://127.0.0.1:54016/api/chat',
                json={
                    'message': message,
                    'session_id': f'test_time_request_{i}'
                },
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data['response']
                
                # Check for time-related responses
                has_time_response = any(phrase in ai_response.lower() for phrase in [
                    'time', 'clock', 'pm', 'am', ':', 'hour', 'minute'
                ])
                
                if has_time_response:
                    print(f"   ✅ PASSED: Response includes time information")
                    print(f"   📝 Response: {ai_response}")
                    results.append({"message": message, "passed": True, "response": ai_response})
                else:
                    print(f"   ❌ FAILED: No time information in response")
                    print(f"   📝 Response: {ai_response}")
                    results.append({"message": message, "passed": False, "response": ai_response})
                    
            else:
                print(f"   ❌ Server error: {response.status_code}")
                results.append({"message": message, "passed": False, "response": f"Server error: {response.status_code}"})
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results.append({"message": message, "passed": False, "response": f"Error: {e}"})
        
        # Small delay between requests
        time.sleep(1)
    
    return results

def main():
    """Run all tests"""
    print("🚀 NO AUTOMATIC TIME ANNOUNCEMENTS TEST")
    print("=" * 60)
    print("This test verifies that the AI:")
    print("✅ Does NOT automatically add time information to casual responses")
    print("✅ DOES respond with time when explicitly asked")
    print("=" * 60)
    
    # Test 1: No automatic time
    no_auto_results = test_no_auto_time()
    
    # Test 2: Explicit time requests
    explicit_results = test_explicit_time_requests()
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 TEST SUMMARY")
    print("=" * 60)
    
    # No auto time results
    no_auto_passed = sum(1 for r in no_auto_results if r["passed"])
    no_auto_total = len(no_auto_results)
    print(f"No Automatic Time: {no_auto_passed}/{no_auto_total} passed")
    
    # Explicit time results
    explicit_passed = sum(1 for r in explicit_results if r["passed"])
    explicit_total = len(explicit_results)
    print(f"Explicit Time Requests: {explicit_passed}/{explicit_total} passed")
    
    # Overall result
    total_passed = no_auto_passed + explicit_passed
    total_tests = no_auto_total + explicit_total
    
    print(f"\nOverall: {total_passed}/{total_tests} tests passed")
    
    if total_passed == total_tests:
        print("🎉 ALL TESTS PASSED! Time announcements are working correctly.")
    else:
        print("⚠️ Some tests failed. Check the results above.")
        
        # Show failed tests
        print("\nFailed tests:")
        for result in no_auto_results + explicit_results:
            if not result["passed"]:
                print(f"❌ '{result['message']}' -> {result['response'][:100]}...")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
