#!/usr/bin/env python3
"""
Test script for the Gini 1.5 Screen Analyzer system.
This script tests the Gemini Vision API integration and backend functionality.
"""

import os
import sys
import asyncio
import subprocess
from pathlib import Path

def print_header():
    print("=" * 60)
    print("🚀 GINI 1.5 SCREEN ANALYZER - TEST SUITE")
    print("=" * 60)
    print()

def test_python_packages():
    """Test if required Python packages are installed."""
    print("📦 Testing Python packages...")
    
    required_packages = [
        'google-generativeai',
        'flask',
        'flask-cors',
        'pywebview',
        'requests',
        'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'google-generativeai':
                import google.generativeai
            elif package == 'flask-cors':
                import flask_cors
            elif package == 'python-dotenv':
                import dotenv
            else:
                __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - NOT INSTALLED")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("💡 Install with: pip install " + " ".join(missing_packages))
        return False
    else:
        print("✅ All required packages are installed!")
        return True

def test_api_key():
    """Test if Gemini API key is configured."""
    print("\n🔑 Testing API key configuration...")
    
    # Try to load from .env file
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        pass
    
    api_key = os.getenv('GOOGLE_AI_API_KEY') or os.getenv('GEMINI_API_KEY')
    
    if not api_key:
        print("  ❌ No API key found in environment variables")
        print("  💡 Set GOOGLE_AI_API_KEY or GEMINI_API_KEY environment variable")
        print("  💡 Or create a .env file with your API key")
        return False
    elif api_key == 'your_gemini_api_key_here':
        print("  ❌ API key is still the placeholder value")
        print("  💡 Replace with your actual Gemini API key")
        return False
    else:
        print(f"  ✅ API key found: {api_key[:10]}...{api_key[-4:]}")
        return True

def test_gemini_connection():
    """Test connection to Gemini API."""
    print("\n🧠 Testing Gemini API connection...")
    
    try:
        import google.generativeai as genai
        from PIL import Image
        
        api_key = os.getenv('GOOGLE_AI_API_KEY') or os.getenv('GEMINI_API_KEY')
        if not api_key:
            print("  ❌ No API key available for testing")
            return False
        
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-1.5-pro')
        
        # Create a simple test image
        test_image = Image.new('RGB', (100, 100), color='blue')
        
        response = model.generate_content([
            "What color is this image? Answer in one word.",
            test_image
        ])
        
        print(f"  ✅ API connection successful!")
        print(f"  🤖 Test response: {response.text.strip()}")
        return True
        
    except Exception as e:
        print(f"  ❌ API connection failed: {e}")
        return False

def test_backend_files():
    """Test if backend files exist."""
    print("\n📁 Testing backend files...")
    
    required_files = [
        'astra_ai/services/gemini_vision.py',
        'astra_ai/scripts/run_desktop_nova.py',
        'astra_ai/ui/Gini.15.html'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} - NOT FOUND")
            all_exist = False
    
    return all_exist

def run_quick_server_test():
    """Test if the Flask server can start."""
    print("\n🌐 Testing Flask server startup...")
    
    try:
        # Import the Flask app components
        sys.path.append('astra_ai')
        from services.gemini_vision import GeminiVisionAnalyzer

        print("  ✅ Gemini service imports successfully")
        return True

    except Exception as e:
        print(f"  ❌ Server test failed: {e}")
        return False

def print_usage_instructions():
    """Print usage instructions."""
    print("\n" + "=" * 60)
    print("🎯 HOW TO USE GINI 1.5 SCREEN ANALYZER")
    print("=" * 60)
    print()
    print("1. 🔧 Setup:")
    print("   - Get API key from: https://aistudio.google.com/app/apikey")
    print("   - Set environment variable: GOOGLE_AI_API_KEY=your_key")
    print("   - Or create .env file with your API key")
    print()
    print("2. 🚀 Run the application:")
    print("   python astra_ai/scripts/run_desktop_nova.py")
    print()
    print("3. 🌐 Open in browser:")
    print("   Navigate to: http://localhost:PORT/Gini.15.html")
    print("   (PORT will be shown when server starts)")
    print()
    print("4. 📸 Use the screen analyzer:")
    print("   - Click 'What's on my screen?' button")
    print("   - Allow screen capture permission")
    print("   - Wait for AI analysis")
    print("   - Ask follow-up questions")
    print()
    print("5. 🎤 Voice commands (when integrated):")
    print("   - 'What's on my screen?'")
    print("   - 'Analyze my screen'")
    print("   - 'Describe what I'm looking at'")
    print()

def main():
    """Run all tests."""
    print_header()
    
    tests = [
        ("Python Packages", test_python_packages),
        ("API Key Configuration", test_api_key),
        ("Gemini API Connection", test_gemini_connection),
        ("Backend Files", test_backend_files),
        ("Server Components", run_quick_server_test)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} - {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your Gini 1.5 Screen Analyzer is ready to use!")
        print_usage_instructions()
    else:
        print("⚠️  Some tests failed. Please fix the issues above before using the system.")
        print("\n💡 Common fixes:")
        print("   - Install missing packages: pip install -r requirements.txt")
        print("   - Set up your Gemini API key")
        print("   - Check file paths and permissions")

if __name__ == "__main__":
    main()
