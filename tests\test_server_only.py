#!/usr/bin/env python3
"""
Test server without Nova AI import to isolate the issue
"""

import sys
import os
import json
import time
import threading
import socket
import webbrowser
from pathlib import Path
from flask import Flask, request, jsonify
from flask_cors import CORS

print("🚀 Starting Test Server...")

# Global variables
nova_ai = None
chat_histories = {}

def get_free_port():
    """Get a free port"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(('', 0))
        return s.getsockname()[1]

# Create Flask app
app = Flask(__name__)
CORS(app)

@app.route('/api/health', methods=['GET'])
def health_check():
    """System health check"""
    return jsonify({
        'status': 'healthy',
        'nova_ai_initialized': nova_ai is not None,
        'timestamp': time.time(),
        'message': 'Test server is running!'
    })

@app.route('/api/test', methods=['GET'])
def test_endpoint():
    """Test endpoint"""
    return jsonify({
        'message': 'Test server is working!',
        'timestamp': time.time(),
        'python_version': sys.version,
        'working_directory': os.getcwd()
    })

@app.route('/api/chat', methods=['POST'])
def chat():
    """Mock chat endpoint"""
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()
        
        if not user_message:
            return jsonify({'error': 'No message provided'}), 400
        
        # Mock response
        response = f"Mock response to: {user_message}"
        
        return jsonify({
            'response': response,
            'source': 'mock',
            'timestamp': time.time()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def run_flask_server(port):
    """Run the Flask server"""
    print(f"🌐 Starting test server on port {port}")
    app.run(host='127.0.0.1', port=port, debug=False, use_reloader=False)

def main():
    """Main function"""
    print("\n" + "=" * 50)
    print("🧪 TEST SERVER")
    print("   Testing Flask without Nova AI")
    print("=" * 50)
    
    # Get free port
    api_port = get_free_port()
    
    # Start Flask server
    flask_thread = threading.Thread(target=run_flask_server, args=(api_port,), daemon=True)
    flask_thread.start()
    
    # Wait for server to start
    time.sleep(2)
    
    print("\n" + "=" * 50)
    print("🎉 Test Server Started!")
    print("=" * 50)
    print(f"🌐 Server: http://127.0.0.1:{api_port}")
    print(f"🔍 Health: http://127.0.0.1:{api_port}/api/health")
    print(f"🧪 Test: http://127.0.0.1:{api_port}/api/test")
    print("=" * 50)
    
    # Open health check in browser
    health_url = f"http://127.0.0.1:{api_port}/api/health"
    print(f"\n🌐 Opening: {health_url}")
    webbrowser.open(health_url)
    
    # Keep server running
    try:
        print("\n✅ Server is running. Press Ctrl+C to stop.")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Shutting down...")

if __name__ == '__main__':
    main()
