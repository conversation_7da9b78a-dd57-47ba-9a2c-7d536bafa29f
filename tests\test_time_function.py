#!/usr/bin/env python3
"""
Test script to verify the time function is working correctly
"""

import sys
import os
from datetime import datetime

# Add the core directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'astra_ai', 'core'))

try:
    from nova_ai import get_time_in_location
    
    def test_time_function():
        print("🕐 Testing Time Function")
        print("=" * 50)
        
        # Test current system time
        current_time = datetime.now()
        print(f"System time: {current_time.strftime('%I:%M %p')} ({current_time.strftime('%H:%M:%S')})")
        print()
        
        # Test locations
        test_locations = [
            "Italy",
            "Rome", 
            "New York",
            "London",
            "Tokyo",
            "Los Angeles"
        ]
        
        for location in test_locations:
            print(f"Testing: {location}")
            try:
                result = get_time_in_location(location)
                print(f"  Result: {result}")
                
                # Extract the time from the result
                if "is " in result:
                    time_part = result.split("is ")[1].split(".")[0]
                    print(f"  Extracted time: {time_part}")
                
                print()
            except Exception as e:
                print(f"  Error: {e}")
                print()
        
        print("=" * 50)
        print("✅ Time function test completed")

    if __name__ == "__main__":
        test_time_function()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the Astra_ai directory")
except Exception as e:
    print(f"❌ Error: {e}")
