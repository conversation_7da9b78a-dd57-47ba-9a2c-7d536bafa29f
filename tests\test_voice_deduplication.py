#!/usr/bin/env python3
"""
Test script to verify voice deduplication is working correctly
"""

import json
import time
import threading
import os
from datetime import datetime

def simulate_duplicate_file_changes():
    """Simulate rapid file changes that could cause duplicate voice processing"""
    print("🧪 Testing Voice Deduplication")
    print("=" * 50)
    
    # Create a test response
    test_response = {
        "timestamp": datetime.now().strftime('%Y-%m-%dT%H:%M:%S.%fZ'),
        "conversation_data": {
            "user_message": "Test deduplication",
            "ai_response": "This is a test response for deduplication testing.",
            "message_id": "test_dedup_12345"
        }
    }
    
    # Load existing responses
    responses = []
    if os.path.exists("ai_responses.json"):
        try:
            with open("ai_responses.json", 'r', encoding='utf-8') as f:
                responses = json.load(f)
        except:
            responses = []
    
    print(f"📄 Current responses in file: {len(responses)}")
    
    # Add the test response
    responses.append(test_response)
    
    # Simulate rapid file writes (this could trigger duplicate voice processing)
    print("🔄 Simulating rapid file changes...")
    
    def write_file_rapidly():
        for i in range(5):
            with open("ai_responses.json", 'w', encoding='utf-8') as f:
                json.dump(responses, f, indent=2, ensure_ascii=False)
            time.sleep(0.05)  # 50ms between writes
            print(f"   Write {i+1}/5 completed")
    
    # Start multiple threads writing the same file
    threads = []
    for i in range(3):
        thread = threading.Thread(target=write_file_rapidly)
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    print("✅ Rapid file change simulation completed")
    print("🎤 If voice deduplication is working correctly, you should hear only ONE voice response")
    print("❌ If deduplication is broken, you might hear multiple overlapping voices")
    
    return test_response["conversation_data"]["message_id"]

def test_voice_system_status():
    """Test voice system status and debug commands"""
    print("\n🔍 Testing Voice System Status")
    print("=" * 30)
    
    try:
        # Try to import and test the voice system
        import sys
        sys.path.append('astra_ai/speech')
        import importlib.util
        spec = importlib.util.spec_from_file_location("ai_voice", "astra_ai/speech/Ai vioce.py")
        ai_voice_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(ai_voice_module)
        
        # Test the deduplication features
        CartesiaConfig = ai_voice_module.CartesiaConfig
        TextToSpeech = ai_voice_module.TextToSpeech
        
        # Create a test instance
        config = CartesiaConfig(
            api_key="test_key",
            voice_id="test_voice",
            model_id="sonic-english"
        )
        
        tts = TextToSpeech(config, service_mode=True)
        
        # Test deduplication features
        print("✅ Voice system imported successfully")
        print(f"📊 Processing lock available: {hasattr(tts, 'processing_lock')}")
        print(f"📊 Currently processing set available: {hasattr(tts, 'currently_processing')}")
        print(f"📊 Speaking lock available: {hasattr(tts, 'speaking_lock')}")
        print(f"📊 Voice busy check available: {hasattr(tts, 'is_voice_busy')}")
        
        # Test status methods
        if hasattr(tts, 'get_voice_status_detailed'):
            status = tts.get_voice_status_detailed()
            print("🔍 Voice status details:")
            for key, value in status.items():
                print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing voice system: {e}")
        return False

def monitor_processed_messages():
    """Monitor the processed messages file to verify deduplication"""
    print("\n📋 Monitoring Processed Messages")
    print("=" * 30)
    
    processed_file = "processed_messages.json"
    
    if os.path.exists(processed_file):
        try:
            with open(processed_file, 'r', encoding='utf-8') as f:
                processed = json.load(f)
            print(f"📊 Currently processed messages: {len(processed)}")
            if processed:
                print(f"📝 Last few processed IDs: {processed[-3:]}")
        except Exception as e:
            print(f"❌ Error reading processed messages: {e}")
    else:
        print("📝 No processed messages file found (normal for first run)")

def main():
    print("🎯 Voice Deduplication Test Suite")
    print("=" * 60)
    
    # Test 1: Voice system status
    voice_system_ok = test_voice_system_status()
    
    # Test 2: Monitor current state
    monitor_processed_messages()
    
    # Test 3: Simulate duplicate file changes
    if voice_system_ok:
        test_message_id = simulate_duplicate_file_changes()
        
        print(f"\n⏳ Waiting 5 seconds for voice processing...")
        time.sleep(5)
        
        # Check if message was processed correctly
        monitor_processed_messages()
        
        print(f"\n🎯 Test Results:")
        print(f"   Test message ID: {test_message_id}")
        print(f"   Expected: Only ONE voice should speak the test response")
        print(f"   If you heard multiple overlapping voices, deduplication failed")
        print(f"   If you heard only one clear voice, deduplication succeeded!")
        
    else:
        print("❌ Voice system test failed - skipping file change simulation")
    
    print("\n" + "=" * 60)
    print("🏁 Voice Deduplication Test Complete!")
    
    print("\n📋 Manual Testing Instructions:")
    print("1. Start Nova AI: python astra_ai/core/nova_ai.py")
    print("2. Send a message to Nova AI")
    print("3. Listen carefully - you should hear only ONE voice response")
    print("4. Use 'voice debug' command to check system status")
    print("5. Use 'voice clear' command if voices get stuck")

if __name__ == "__main__":
    main()
