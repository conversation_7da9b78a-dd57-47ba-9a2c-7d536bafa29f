#!/usr/bin/env python3
"""
Direct Voice Test Script
Tests the Cartesia voice synthesis directly without the full server
"""

import sys
import os
from pathlib import Path

# Add the speech module to the path
sys.path.append(str(Path(__file__).parent / 'speech'))

def test_direct_voice():
    """Test direct voice synthesis"""
    try:
        print("🎤 Testing direct voice synthesis...")
        
        # Import Cartesia
        from cartesia import Cartesia
        import pyaudio
        import base64
        import numpy as np
        import threading
        import queue
        
        # Configuration
        config = {
            'api_key': 'sk_car_quA8Xego3FiXMwXtDuyLcR',
            'voice_id': 'f114a467-c40a-4db8-964d-aaba89cd08fa',
            'model_id': 'sonic-english',
            'sample_rate': 48000,
            'volume_multiplier': 1.2
        }
        
        # Initialize Cartesia
        print("🔧 Initializing Cartesia client...")
        client = Cartesia(api_key=config['api_key'])
        
        # Initialize PyAudio
        print("🔊 Initializing audio system...")
        p = pyaudio.PyAudio()
        
        # List audio devices
        print("🔊 Available audio devices:")
        for i in range(p.get_device_count()):
            info = p.get_device_info_by_index(i)
            if info['maxOutputChannels'] > 0:
                print(f"   Device {i}: {info['name']} (Channels: {info['maxOutputChannels']})")
        
        # Open audio stream
        stream = p.open(
            format=pyaudio.paFloat32,
            channels=1,
            rate=config['sample_rate'],
            output=True,
            frames_per_buffer=1024
        )
        
        # Test text
        test_text = "Hello! This is a direct test of the voice synthesis system. Can you hear me clearly?"
        
        print(f"🎤 Synthesizing: {test_text}")
        
        # Generate speech
        print("🔧 Making Cartesia API request...")
        try:
            response = client.tts.sse(
                model_id=config['model_id'],
                transcript=test_text,
                voice={"mode": "id", "id": config['voice_id']},
                output_format={
                    "container": "raw",
                    "encoding": "pcm_f32le",
                    "sample_rate": config['sample_rate']
                }
            )

            print("✅ API request successful, processing response...")

            # Play audio
            chunk_count = 0
            for output in response:
                # Handle Cartesia WebSocketResponse_Chunk objects
                if hasattr(output, 'data') and output.data:
                    # Decode audio data
                    audio_data = base64.b64decode(output.data)
                    print(f"🔊 Audio data size: {len(audio_data)} bytes")

                    # Convert to numpy array and apply volume (make a copy to avoid read-only issues)
                    audio_array = np.frombuffer(audio_data, dtype=np.float32).copy()
                    audio_array *= config['volume_multiplier']
                    audio_array = np.clip(audio_array, -1.0, 1.0)

                    # Play audio
                    stream.write(audio_array.tobytes())
                    chunk_count += 1

                    if chunk_count % 10 == 0:
                        print(f"🔊 Playing chunk {chunk_count}...")
                elif hasattr(output, 'type'):
                    print(f"📋 Non-audio chunk type: {output.type}")
                else:
                    print(f"❓ Unknown output: {output}")

            print(f"✅ Voice test completed! Played {chunk_count} audio chunks.")

        except Exception as api_error:
            print(f"❌ Cartesia API error: {api_error}")
            return False
        
        # Cleanup
        stream.stop_stream()
        stream.close()
        p.terminate()
        
        return True
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please install: pip install cartesia pyaudio numpy")
        return False
    except Exception as e:
        print(f"❌ Voice test failed: {e}")
        return False

def test_audio_only():
    """Test just the audio system with a simple tone"""
    try:
        import pyaudio
        import numpy as np
        
        print("🔊 Testing audio system with test tone...")
        
        # Initialize PyAudio
        p = pyaudio.PyAudio()
        
        # Open audio stream
        stream = p.open(
            format=pyaudio.paFloat32,
            channels=1,
            rate=48000,
            output=True,
            frames_per_buffer=1024
        )
        
        # Generate test tone (440 Hz for 2 seconds)
        duration = 2.0
        sample_rate = 48000
        frequency = 440
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        test_tone = np.sin(2 * np.pi * frequency * t) * 0.3
        
        print("🔊 Playing 440 Hz test tone for 2 seconds...")
        
        # Play in chunks
        chunk_size = 1024
        for i in range(0, len(test_tone), chunk_size):
            chunk = test_tone[i:i+chunk_size]
            stream.write(chunk.astype(np.float32).tobytes())
        
        print("✅ Audio test completed!")
        
        # Cleanup
        stream.stop_stream()
        stream.close()
        p.terminate()
        
        return True
        
    except Exception as e:
        print(f"❌ Audio test failed: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🎤 VOICE SYSTEM TEST")
    print("=" * 60)
    
    # Test 1: Audio system only
    print("\n1. Testing audio system...")
    audio_success = test_audio_only()
    
    if audio_success:
        print("\n2. Testing voice synthesis...")
        voice_success = test_direct_voice()
        
        if voice_success:
            print("\n✅ All tests passed! Voice system is working.")
        else:
            print("\n❌ Voice synthesis failed, but audio system works.")
    else:
        print("\n❌ Audio system test failed. Check your audio drivers and speakers.")
    
    print("\n" + "=" * 60)
